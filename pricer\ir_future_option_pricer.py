import datetime as dt
import numpy as np
import pandas as pd
import QuantLib as ql
import time
from scipy.stats import norm
from scipy.interpolate import interp1d
from cachetools import cached, LRUCache
from loader.ir_future_option_data_loader import interestRateFutureOptionLoader


class interestRateFutureOptionPricer:
    def __init__(self, name: str):
        self.ir_option_name = name.upper()
        self.loader = interestRateFutureOptionLoader(self.ir_option_name)
        self.is_bond_future = self.loader.is_bond_future
        self.sign = 1 if self.is_bond_future else -1
        self.opt_params = {}
        if pd.__version__ >= "2.2":
            self.opt_params["include_groups"] = False

    @staticmethod
    def find_closest_delta(group, delta, tolerance=0.05):
        group["delta_diff"] = abs(group["delta"] - delta)
        closest_row = group.loc[group["delta_diff"].idxmin()]
        if closest_row["delta_diff"] <= tolerance:
            return closest_row[["tte", "vol", "delta", "strike", "price_atmf"]]
        else:
            return None

    @staticmethod
    def find_closest_strike(group, tolerance=0.05):
        group["strike_diff"] = abs(group["strike_diff"])
        closest_row = group.loc[group["strike_diff"].idxmin()]
        if closest_row["strike_diff"] <= tolerance:
            return closest_row[["tte", "vol", "delta", "strike", "price_atmf"]]
        else:
            return None

    @staticmethod
    def find_closest_maturity(group, maturity):
        group["tte_diff"] = abs(group["tte"] - maturity)
        closest_row = group.loc[group["tte_diff"].idxmin()]
        return closest_row[
            ["tte", "vol", "delta", "strike", "price_atmf", "ticker_root"]
        ]

    @staticmethod
    def interpolate_vol_by_closest_delta(group, delta, tolerance=0.5):
        group = group.sort_values(by="delta")
        less_than = group[
            (group["delta"] <= delta) & (group["delta"] >= delta - tolerance)
        ]
        greater_than = group[
            (group["delta"] > delta) & (group["delta"] <= delta + tolerance)
        ]
        if not less_than.empty and not greater_than.empty:
            slope = pd.concat([less_than.tail(1), greater_than.head(1)], axis=0)
            f = interp1d(
                slope["delta"], slope["vol"], kind="linear", fill_value="extrapolate"
            )
            slope["vol"] = f(delta)
            return slope.iloc[0]
        elif not less_than.empty:
            closest_less_than = less_than.iloc[-1]
            if closest_less_than["delta"] >= delta - 0.05:
                return closest_less_than
        elif not greater_than.empty:
            closest_greater_than = greater_than.iloc[0]
            if closest_greater_than["delta"] <= delta + 0.05:
                return closest_greater_than
        return None

    @staticmethod
    def interpolate_vol_by_closest_strike(group):
        less_than = group[group["strike_diff"] <= 0]
        greater_than = group[group["strike_diff"] > 0]
        if not less_than.empty and not greater_than.empty:
            slope = pd.concat(
                [
                    less_than.loc[[less_than["strike_diff"].idxmax()]],
                    greater_than.loc[[greater_than["strike_diff"].idxmin()]],
                ],
                axis=0,
            )
            f = interp1d(
                slope["strike_diff"],
                slope["vol"],
                kind="linear",
                fill_value="extrapolate",
            )
            slope["vol"] = f(0)
            return slope.iloc[0]
        elif not less_than.empty:
            closest_less_than = less_than.loc[less_than["strike_diff"].idxmax()]
            if closest_less_than["strike_diff"] >= -0.1:
                return closest_less_than
        elif not greater_than.empty:
            closest_greater_than = greater_than.loc[
                greater_than["strike_diff"].idxmin()
            ]
            if closest_greater_than["strike_diff"] <= 0.1:
                return closest_greater_than
        return None

    @staticmethod
    def interpolate_vol_by_maturity(group):
        if group.shape[0] < 2:
            return None
        group["var"] = group["vol"] ** 2 * group["tte_bus_days"]
        f = interp1d(
            group["tte_bus_days"], group["var"], kind="linear", fill_value="extrapolate"
        )
        maturity_bus_days = group["maturity_bus_days"].iloc[0]
        return np.sqrt(f(maturity_bus_days) / maturity_bus_days)

    @staticmethod
    def interpolate_vol_and_proxy_option_by_maturity(group):
        if group.shape[0] < 2:
            return None
        group["var"] = group["vol"] ** 2 * group["tte_bus_days"]
        f = interp1d(
            group["tte_bus_days"], group["var"], kind="linear", fill_value="extrapolate"
        )
        maturity_bus_days = group["maturity_bus_days"].iloc[0]
        group["vol"] = np.sqrt(f(maturity_bus_days) / maturity_bus_days)
        return group

    @staticmethod
    def interpolate_premium_by_maturity(group, maturity):
        if group.shape[0] < 2:
            return None
        f = interp1d(
            group["tte"], group["premium"], kind="linear", fill_value="extrapolate"
        )
        return f(maturity)

    def calculate_strike(
        self, data, delta, bus_days_field="tte_bus_days", sign_override=None
    ):
        # Calculate strike given atm, vol and delta
        if not sign_override:
            sign_override = self.sign * data.index.get_level_values("sign")
        if not self.is_bond_future:
            data["fixed_strike"] = (
                data["price_atmf"]
                + data["vol"]
                / 100
                * np.sqrt(data[bus_days_field] / 252)
                * norm.ppf(delta / data["discount_factor"])
                * sign_override
            )
        else:
            data["fixed_strike"] = (
                data["price_atmf"]
                * np.exp(
                    0.5 * (data["vol"] / 100) ** 2 * data[bus_days_field] / 252
                    - data["vol"]
                    / 100
                    * np.sqrt(data[bus_days_field] / 252)
                    * norm.ppf(delta / data["discount_factor"])
                )
                * sign_override
            )
        return data

    def filter_prefered_sign(self, data, prefered_sign):
        # Return the preferred sign data if it has at least 2 rows, otherwise return unpreferred sign data
        sign = data.index.get_level_values("sign")
        preferred = data[sign * prefered_sign > 0]
        unpreferred = data[sign * prefered_sign < 0]
        if len(preferred) >= 2:
            return preferred
        elif len(unpreferred) >= 2:
            return unpreferred
        return None

    @cached(
        cache=LRUCache(maxsize=32),
        key=lambda self, start_date, end_date, maturity, need_call_data, need_put_data, _: (
            self.ir_option_name,
            start_date,
            end_date,
            maturity,
            need_call_data,
            need_put_data,
        ),
    )
    def calculate_option_vol_and_delta(
        self,
        start_date: dt.date,
        end_date: dt.date,
        maturity: int,
        need_call_data: bool,
        need_put_data: bool,
        option_px_data: pd.DataFrame = pd.DataFrame(),
    ):
        load_start_time = time.time()
        if option_px_data.empty:
            option_px_data = self.loader.load_option_price(
                start_date, end_date, maturity=maturity
            )
            print(
                f"1. Total load {self.ir_option_name} data with maturity: {maturity} ---> time:",
                "%.3f" % (time.time() - load_start_time),
            )

        compute_start_time = time.time()
        option_px_data = option_px_data[option_px_data["tte_bus_days"] > 0]

        option_px_data = pd.concat(
            [
                option_px_data,
                option_px_data["ticker_root"]
                .map(lambda t: self.sign if t[-1] == "C" else -self.sign)
                .rename("sign"),
            ],
            axis=1,
        )

        option_px_data = option_px_data.groupby(["date", "sign"]).filter(
            lambda group: len(group) >= 2
        )  # Each date should have 2 option maturities, e.g. H5 and J5
        if need_call_data and not need_put_data:
            option_px_data = option_px_data[
                option_px_data["sign"] * self.sign > 0
            ].copy(deep=False)
        elif need_put_data and not need_call_data:
            option_px_data = option_px_data[
                option_px_data["sign"] * self.sign < 0
            ].copy(deep=False)

        option_px_data["ticker_root"] = option_px_data["ticker_root"].map(
            lambda t: t[:-1]
        )
        id_vars_to_melt = [
            "date",
            "ticker_root",
            "sign",
            "tte",
            "tte_bus_days",
            "price_atmf",
            "discount_factor",
        ]
        if not self.is_bond_future:  # Convert STIR futures px to rates
            option_px_data["price_atmf"] = 100.0 - option_px_data["price_atmf"]
        else:
            id_vars_to_melt.append("price_ctd_fwd_dv01")
        option_px_data = option_px_data.melt(
            id_vars=id_vars_to_melt,
            var_name="strike",
            value_name="price",
        ).dropna(
            axis=0, how="any"
        )  # dropna is important here

        option_px_data["strike"] = option_px_data["strike"].astype(float)
        if not self.is_bond_future:
            option_px_data["strike"] = 100.0 - option_px_data["strike"]

        # In the money options should worth at least intrinsic value
        # Remove options with price less than intrinsic value
        valid_option = option_px_data["price"] >= option_px_data[
            "discount_factor"
        ] * option_px_data["sign"] * (
            option_px_data["price_atmf"] - option_px_data["strike"]
        )
        option_px_data = option_px_data[valid_option]

        # Calculate implied vol and delta using Bachelier model or Black-Scholes model
        if not self.is_bond_future:
            option_px_data["vol"] = option_px_data.apply(
                lambda opt: ql.bachelierBlackFormulaImpliedVol(
                    opt["sign"],
                    opt["strike"],
                    opt["price_atmf"],
                    opt["tte_bus_days"] / 252,
                    opt["price"],
                    opt["discount_factor"],
                ),
                axis=1,
            )
            option_px_data["delta"] = option_px_data.apply(
                lambda opt: ql.bachelierBlackFormulaAssetItmProbability(
                    opt["sign"],
                    opt["strike"],
                    opt["price_atmf"],
                    opt["vol"] * np.sqrt(opt["tte_bus_days"] / 252),
                ),
                axis=1,
            )
        else:
            option_px_data["vol"] = option_px_data.apply(
                lambda opt: ql.blackFormulaImpliedStdDev(
                    opt["sign"],
                    opt["strike"],
                    opt["price_atmf"],
                    opt["price"],
                    opt["discount_factor"],
                ),
                axis=1,
            )  # StdDev
            option_px_data["delta"] = option_px_data.apply(
                lambda opt: ql.blackFormulaAssetItmProbability(
                    opt["sign"],
                    opt["strike"],
                    opt["price_atmf"],
                    opt["vol"],
                ),
                axis=1,
            )
            option_px_data["vol"] *= np.sqrt(252 / option_px_data["tte_bus_days"])
        option_px_data["vol"] = (option_px_data["vol"] * 100).astype(float)
        option_px_data["delta"] = (
            option_px_data["delta"] * option_px_data["discount_factor"]
        ).astype(float)
        print(
            f"2. Compute option vol and delta with maturity: {maturity} ---> time: ",
            "%.3f" % (time.time() - compute_start_time),
        )
        return option_px_data

    def merge_fixed_strike_data(
        self, data: pd.DataFrame, fixed_strike_data: pd.DataFrame
    ):
        if "rank" in fixed_strike_data.columns:
            data = pd.concat(
                [
                    data,
                    data.groupby(["date", "sign"])["tte"]
                    .rank(method="dense")
                    .rename("rank"),
                ],
                axis=1,
            )
            data = data.merge(
                fixed_strike_data,
                how="inner",
                left_on=["date", "rank", "sign"],
                right_on=["date", "rank", "sign"],
            )
        else:
            data = data.merge(
                fixed_strike_data,
                how="inner",
                left_on=["date", "sign"],
                right_on=["date", "sign"],
            )
        if not self.is_bond_future:
            if "fixed_moneyness" in fixed_strike_data.columns:
                data["fixed_strike"] = data["price_atmf"] + data["fixed_moneyness"]
            data["strike_diff"] = data["strike"] - data["fixed_strike"]
        else:
            if "fixed_moneyness" in fixed_strike_data.columns:
                data["fixed_strike"] = data["price_atmf"] * data["fixed_moneyness"]
            data["strike_diff"] = np.log(data["strike"] / data["fixed_strike"])
        return data

    def find_closest_tradable_option_tickers(
        self,
        maturity: int,
        option_type: str,
        call_put: str,
        delta: float,
        shift: float,
        option_px_data: pd.DataFrame = pd.DataFrame(),
        fixed_strike_data: pd.DataFrame = pd.DataFrame(),
    ):
        if call_put == "C":
            data = option_px_data[option_px_data["sign"] * self.sign > 0]
        elif call_put == "P":
            data = option_px_data[option_px_data["sign"] * self.sign < 0]
        elif option_type.startswith("FIXED"):
            sign = fixed_strike_data.index.get_level_values("sign").unique()
            if len(sign) == 1:
                call_put = "C" if sign[0] * self.sign > 0 else "P"
                data = option_px_data[option_px_data["sign"] * sign[0] > 0]
            else:
                raise ValueError(
                    f"Unknown call / put sign {sign} for fixed strike or moneyness data!"
                )
        else:
            raise ValueError("To find option tickers, please specify call or put!")
        # Find closest delta and maturity
        if not option_type.startswith("FIXED"):
            # Find vol by closest delta
            data = (
                data.groupby(["date", "ticker_root", "sign"])
                .apply(
                    interestRateFutureOptionPricer.find_closest_delta,
                    delta=delta,
                    tolerance=0.5,  # please tune this
                    **self.opt_params,
                )
                .dropna(axis=0)
            )
        else:
            data = self.merge_fixed_strike_data(data, fixed_strike_data)
            # Find vol by closest strike
            data = (
                data.groupby(["date", "ticker_root", "sign"])
                .apply(
                    interestRateFutureOptionPricer.find_closest_strike,
                    **self.opt_params,
                )
                .dropna(axis=0)
            )
        # TODO: Allow shift to be tradable, e.g. input 25C+1, 25C-1
        if shift != 0:
            raise ValueError(
                "Shift is not supported for finding tradable option tickers!"
            )
        data.reset_index(inplace=True)
        # Find vol by closest maturity
        data = data.groupby(["date", "sign"]).apply(
            interestRateFutureOptionPricer.find_closest_maturity,
            maturity=maturity,
            **self.opt_params,
        )
        data["imm_code"] = data["ticker_root"].str.removeprefix(self.ir_option_name)
        data["call_put"] = data.index.get_level_values("sign").map(
            lambda s: "C" if s * self.sign > 0 else "P"
        )
        data["ticker"] = self.loader.ticker_formatter(
            data["imm_code"], 100 - data["strike"], data["call_put"]
        )
        return data

    @cached(
        cache=LRUCache(maxsize=32),
        key=lambda self, start_date, end_date, maturity, option_type, call_put, delta, shift, output, _, __: (
            self.ir_option_name,
            start_date,
            end_date,
            maturity,
            option_type,
            call_put,
            delta,
            shift,
            output,
        ),
    )
    def filter_and_interpolate_vol(
        self,
        start_date: dt.date,
        end_date: dt.date,
        maturity: int,
        option_type: str,
        call_put: str,
        delta: float,
        shift: float,
        output: str,
        option_px_data: pd.DataFrame = pd.DataFrame(),
        fixed_strike_data: pd.DataFrame = pd.DataFrame(),
    ):
        if call_put == "C":
            data = option_px_data[option_px_data["sign"] * self.sign > 0]
        elif call_put == "P":
            data = option_px_data[option_px_data["sign"] * self.sign < 0]
        elif option_type.startswith("FIXED"):
            sign = fixed_strike_data.index.get_level_values("sign").unique()
            if len(sign) == 1:
                call_put = "C" if sign[0] * self.sign > 0 else "P"
                data = option_px_data[option_px_data["sign"] * sign[0] > 0]
            elif len(sign) == 2:
                call_put = "A"
            else:
                raise ValueError(
                    f"Unknown call / put sign {sign} for fixed strike or moneyness data!"
                )
        elif output == "Premium":
            return None
        else:
            data = option_px_data

        # return data here for debugging
        # return data
        if not option_type.startswith("FIXED"):
            # Interpolate vol by delta
            data = (
                data.groupby(["date", "ticker_root", "sign"])
                .apply(
                    interestRateFutureOptionPricer.interpolate_vol_by_closest_delta,
                    delta=delta,
                    tolerance=0.5,
                    **self.opt_params,
                )
                .dropna(axis=0)
            )
            # "vol" column is interpolated at the target delta (e.g. 25C), however,
            # "delta" column is the original option's delta (e.g. 24C)
        else:
            # Rough estimates when option_type is FIXED_STRIKE, and in this case shift is 0
            data = self.merge_fixed_strike_data(data, fixed_strike_data)
            # Interpolate vol by strike
            data = (
                data.groupby(["date", "ticker_root", "sign"])
                .apply(
                    interestRateFutureOptionPricer.interpolate_vol_by_closest_strike,
                    **self.opt_params,
                )
                .dropna(axis=0)
            )

        if shift != 0:
            # Special handling for strike shifts (e.g. 25C+1, 25C-1)
            # Since atm, vol(target delta) and target delta (e.g. 25C) is known, we can calculate the target "strike"
            strikes = self.calculate_strike(data, delta)
            strikes = strikes["fixed_strike"] + self.sign * shift
            # After the target fixed_strike (e.g. 25C-1) is calculated,
            # reset the data to the original option chain, and "filter" by preferred sign:
            # e.g. for 25C-1, we prefer call data, while fallback to put data if not enough call data
            data = option_px_data
            if call_put == "C":
                prefered_sign = self.sign
                if shift > 0:
                    data = data[data["sign"] * self.sign > 0]
            elif call_put == "P":
                prefered_sign = -self.sign
                if shift < 0:
                    data = data[data["sign"] * self.sign < 0]
            elif call_put == "A" and shift > 0:
                prefered_sign = self.sign
                data = data[data["sign"] * self.sign > 0]
            elif call_put == "A" and shift < 0:
                prefered_sign = -self.sign
                data = data[data["sign"] * self.sign < 0]
            else:
                raise ValueError(f"Unknown call / put type for shift!")
            data = data.merge(
                strikes,
                how="inner",
                left_on=["date", "ticker_root"],
                right_on=["date", "ticker_root"],
            )
            # "fixed_strike" column is the target strike (strike at the target delta + shift)
            # "strike" column is the original option chain's strike
            if not self.is_bond_future:
                data["strike_diff"] = data["strike"] - data["fixed_strike"]
            else:
                data["strike_diff"] = np.log(data["strike"] / data["fixed_strike"])
            # Interpolate vol by strike
            data = (
                data.groupby(["date", "ticker_root", "sign"])
                .apply(
                    interestRateFutureOptionPricer.interpolate_vol_by_closest_strike,
                    **self.opt_params,
                )
                .dropna(axis=0)
            )
            # "vol" column is interpolated at the target strike (with shift)
            # Then we prefer the call (or put) data and fallback to put (or call) data, e.g. for 25C-1
            data = data.groupby("date", group_keys=False).apply(
                self.filter_prefered_sign,
                prefered_sign=prefered_sign,
                **self.opt_params,
            )
        if call_put == "A" and shift == 0 and output not in ("Strike", "Moneyness"):
            data = data.groupby(["date", "ticker_root"]).agg(
                {
                    "tte": "first",
                    "tte_bus_days": "first",
                    "price_atmf": "first",
                    "discount_factor": "first",
                    "vol": "mean",
                    "delta": "first",
                }
                | ({"price_ctd_fwd_dv01": "first"} if self.is_bond_future else {})
            )

        data["maturity_bus_days"] = data.index.get_level_values("date").map(
            lambda d: self.loader.calendar.businessDaysBetween(
                ql.Date.from_date(d),
                ql.Date.from_date(d) + maturity,
            )
        )  # Convert input maturity from calendar days (e.g. 30, 90) to business days
        if output == "Premium":
            if call_put not in ("C", "P"):
                raise ValueError(
                    "Unknown call / put type, please specify call or put to calculate premium!"
                )
            if not option_type.startswith("FIXED") and shift == 0:
                data = self.calculate_strike(
                    data, delta, "tte_bus_days", 1 if call_put == "C" else -1
                )
            # An alternative way: enable / disable the proxy_option above / below (which interpolates vol at the target maturity first and assumes 2 options with same vol but diff atms and strikes)
            # and replace the calculate_strike to use maturity_bus_days instead of tte_bus_days
            data = data.groupby("date", group_keys=False).apply(
                interestRateFutureOptionPricer.interpolate_vol_and_proxy_option_by_maturity,
                **self.opt_params,
            )
            data["discount_factor"] = 1 / (
                1 + maturity / data["tte"] * (1 / data["discount_factor"] - 1)
            )
            if not self.is_bond_future:
                data["premium"] = data.apply(
                    lambda opt: ql.bachelierBlackFormula(
                        self.sign if call_put == "C" else -self.sign,
                        opt["fixed_strike"],
                        opt["price_atmf"],
                        opt["vol"] / 100 * np.sqrt(opt["maturity_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )
            else:
                data["premium"] = data.apply(
                    lambda opt: ql.blackFormula(
                        self.sign if call_put == "C" else -self.sign,
                        opt["fixed_strike"],
                        opt["price_atmf"],
                        opt["vol"] / 100 * np.sqrt(opt["maturity_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )

            data = (
                data.groupby("date")
                .apply(
                    interestRateFutureOptionPricer.interpolate_premium_by_maturity,
                    maturity=maturity,
                )
                .astype(float)
                .to_frame(name=f"{self.ir_option_name}_{maturity}_{option_type}")
            )
        elif output in ("Strike", "Moneyness"):
            # Output strike from vol and delta, used for rough estimates when option_type is FIXED_STRIKE
            if shift == 0:
                data = self.calculate_strike(data, delta)
                data["fixed_strike"] = data["fixed_strike"].astype(float)
            data["rank"] = data.groupby(["date", "sign"])["tte"].rank(method="dense")
            if output == "Strike":  # Output Strike: "fixed_strike" column
                data = data[["fixed_strike", "rank"]]
            else:  # Output Moneyness: "fixed_moneyness" column
                data["fixed_moneyness"] = (
                    data["fixed_strike"] - data["price_atmf"]
                    if not self.is_bond_future
                    else data["fixed_strike"] / data["price_atmf"]
                )
                data = data[["fixed_moneyness", "rank"]]
        else:
            if self.is_bond_future:
                # Convert StdDev of Black–Scholes pct vol to bps vol
                data["vol"] *= data["price_atmf"] / data["price_ctd_fwd_dv01"]
            data = (
                data.groupby(
                    "date",
                )
                .apply(
                    interestRateFutureOptionPricer.interpolate_vol_by_maturity,
                    **self.opt_params,
                )
                .astype(float)
                .to_frame(name=f"{self.ir_option_name}_{maturity}_{option_type}")
            )

        return data

    def calculate_vol(
        self,
        start_date: dt.date,
        end_date: dt.date,
        maturity: int,
        option_types: list[str] | str,
        output: str = "Vol",
        option_px_data: pd.DataFrame = pd.DataFrame(),
        fixed_strike_data: pd.DataFrame = pd.DataFrame(),
    ):
        if not isinstance(option_types, list):
            option_types = [option_types]
        option_types = sorted(set(s.upper() for s in option_types))
        output = output.capitalize()
        output = (
            "Vol"
            if output not in ["Premium", "Strike", "Moneyness", "Closest_tickers"]
            else output
        )
        deltas, call_put, shifts, need_call_data, need_put_data = (
            [None] * len(option_types),
            [None] * len(option_types),
            [0] * len(option_types),
            False,
            False,
        )

        for i, option_type in enumerate(option_types):
            option_type_no_shift, option_type_shift = option_type, None
            if "+" in option_type:
                option_type_no_shift = option_type.split("+")[0]
                option_type_shift = option_type.split("+")[1]
                shifts[i] = 1
            elif "-" in option_type:
                option_type_no_shift = option_type.split("-")[0]
                option_type_shift = option_type.split("-")[1]
                shifts[i] = -1

            if option_type_no_shift.endswith("C"):
                deltas[i] = (
                    0.5
                    if option_type_no_shift.startswith("ATM")
                    else float(option_type_no_shift[:-1]) / 100
                )
                call_put[i], need_call_data = "C", True
                if shifts[i] < 0:
                    need_put_data = True
            elif option_type_no_shift.endswith("P"):
                deltas[i] = (
                    0.5
                    if option_type_no_shift.startswith("ATM")
                    else float(option_type_no_shift[:-1]) / 100
                )
                call_put[i], need_put_data = "P", True
                if shifts[i] > 0:
                    need_call_data = True
            elif option_type_no_shift.startswith("ATM"):
                deltas[i] = 0.5
                call_put[i] = "A"
                if shifts[i] < 0:
                    need_put_data = True
                elif shifts[i] > 0:
                    need_call_data = True
                else:
                    need_call_data, need_put_data = True, True
            elif option_type_no_shift.startswith("FIXED"):
                if fixed_strike_data.shape[1] > 2:
                    raise ValueError(
                        "Fixed strike or moneyness data should only have one or two columns!"
                    )
                fixed_strike_data = fixed_strike_data.dropna(axis=0)
                need_call_data = (
                    need_call_data
                    or self.sign in fixed_strike_data.index.get_level_values("sign")
                )
                need_put_data = (
                    need_put_data
                    or -self.sign in fixed_strike_data.index.get_level_values("sign")
                )
            else:
                raise ValueError(f"Unknown option type {option_type}")

            if option_type_shift is not None:
                if option_type_shift.endswith("BPS"):
                    shifts[i] *= (
                        float(option_type_shift[:-3]) / 100
                    )  # Convert bps to pct
                elif option_type_shift.endswith("BP"):
                    shifts[i] *= float(option_type_shift[:-2]) / 100
                elif option_type_shift.endswith("%"):
                    shifts[i] *= float(option_type_shift[:-1])
                else:
                    shifts[i] *= float(option_type_shift)

        option_px_data = self.calculate_option_vol_and_delta(
            start_date,
            end_date,
            maturity,
            need_call_data,
            need_put_data,
            option_px_data,
        )

        filter_start_time = time.time()
        " Filter by delta"
        final_result = [None] * len(option_types)

        if output == "Closest_tickers":
            for i, option_type in enumerate(option_types):
                data = self.find_closest_tradable_option_tickers(
                    maturity,
                    option_type,
                    call_put[i],
                    deltas[i],
                    shifts[i],
                    option_px_data,
                    fixed_strike_data,
                )
                final_result[i] = data
        else:
            for i, option_type in enumerate(option_types):
                data = self.filter_and_interpolate_vol(
                    start_date,
                    end_date,
                    maturity,
                    option_type,
                    call_put[i],
                    deltas[i],
                    shifts[i],
                    output,
                    option_px_data,
                    fixed_strike_data,
                )
                final_result[i] = data

        print(
            f"3. Filter by delta: {deltas}, option types: {option_types}, interpolate by delta and maturity: {maturity}, output: {output} ---> time:",
            "%.3f" % (time.time() - filter_start_time),
        )

        if all(result is None for result in final_result):
            return None
        return pd.concat(final_result, axis=1)

    def calculate_forward_vol(
        self,
        start_date: dt.date,
        end_date: dt.date,
        forward_start: int,
        maturity: int,
        option_types: list[str] | str,
        option_px_data_1: pd.DataFrame = pd.DataFrame(),
        option_px_data_2: pd.DataFrame = pd.DataFrame(),
        fixed_strike_data: pd.DataFrame = pd.DataFrame(),
    ):
        maturity2 = forward_start + maturity
        vol1 = self.calculate_vol(
            start_date,
            end_date,
            forward_start,
            option_types,
            "Vol",
            option_px_data_1,
            fixed_strike_data,
        )
        vol2 = self.calculate_vol(
            start_date,
            end_date,
            maturity2,
            option_types,
            "Vol",
            option_px_data_2,
            fixed_strike_data,
        )
        final_result = []
        if vol1 is None or vol2 is None:
            return None
        for option_type in option_types:
            vol1_col = f"{self.ir_option_name}_{forward_start}_{option_type.upper()}"
            vol2_col = f"{self.ir_option_name}_{maturity2}_{option_type.upper()}"
            if vol1_col not in vol1.columns or vol2_col not in vol2.columns:
                continue
            vol = pd.concat([vol1[vol1_col], vol2[vol2_col]], axis=1).dropna(axis=0)
            vol["bus_days_1"] = vol.index.map(
                lambda d: self.loader.calendar.businessDaysBetween(
                    ql.Date.from_date(d), ql.Date.from_date(d) + forward_start
                )
            )
            vol["bus_days_2"] = vol.index.map(
                lambda d: self.loader.calendar.businessDaysBetween(
                    ql.Date.from_date(d), ql.Date.from_date(d) + maturity2
                )
            )
            vol["forward_vol"] = (
                vol[vol2_col] ** 2 * vol["bus_days_2"]
                - vol[vol1_col] ** 2 * vol["bus_days_1"]
            )
            vol = vol[vol["forward_vol"] >= 0]
            vol["forward_vol"] = np.sqrt(
                vol["forward_vol"] / (vol["bus_days_2"] - vol["bus_days_1"])
            )
            final_result.append(
                vol["forward_vol"].to_frame(
                    name=f"{self.ir_option_name}_{forward_start}-{maturity}_{option_type}"
                )
            )
        if not final_result:
            return None
        return pd.concat(final_result, axis=1)
