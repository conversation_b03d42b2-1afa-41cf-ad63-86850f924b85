import pandas as pd
import QuantLib as ql
from .curve import oisCurve


class tonarCurve(oisCurve):
    def __init__(self, context_key: str, **params):
        config = {
            "name": "JPY_TONAR",
            "context_key": context_key,
            "currency": "JPY",
            "fixed_day_counter": ql.Actual365Fixed(),
            "fixed_convention": ql.ModifiedFollowing,
            "swap_settlement_days": 2,
            "swap_payment_lag": 2,
        }
        swap_config = {
            "index": ql.Tona(),
            "paymentConvention": ql.ModifiedFollowing,
            "paymentFrequency": ql.Annual,
            "paymentCalendar": ql.Japan(),
            "fixedPaymentFrequency": ql.Annual,
            "fixedCalendar": ql.Japan(),
        }
        super().__init__(config, swap_config, **params)

    def set_up_helpers(
        self,
        benchmark_meta_data: pd.DataFrame | None = None,
        bbg_bar_data: pd.DataFrame | None = None,
    ):
        return super().set_up_helpers(
            benchmark_meta_data, bbg_bar_data, self.swap_config
        )
