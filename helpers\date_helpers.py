import QuantLib as ql
import os
import re
import pandas as pd
import datetime as dt
from typing import Tuple, List
from functools import lru_cache


@lru_cache(maxsize=1)
def is_dayfirst_format():
    import locale

    return "United Kingdom" in locale.setlocale(locale.LC_TIME, "")


def strp_tenor(tenors: List[str]):
    pattern = r"^(\d+[ymdYMD])?([xX-])?(\d+[ymdYMD])$"
    start_tenors, end_tenors, separators = [], [], []
    for tenor in tenors:
        if not tenor:
            start_tenors.append(None)
            end_tenors.append(None)
            separators.append(None)
        match = re.match(pattern, tenor)
        if match:
            start_tenors.append(match.group(1))
            end_tenors.append(match.group(3))
            separators.append(match.group(2))
        else:
            raise ValueError(f"Error: '{tenor}' is not a valid pattern.")
    return start_tenors, end_tenors, separators


def to_ql_date(date: dt.date | dt.datetime | pd.Timestamp) -> ql.Date:
    return ql.Date.from_date(date)


def next_weekday(
    date: dt.date | dt.datetime | pd.Timestamp, include_date: bool = False
) -> dt.date | dt.datetime | pd.Timestamp:
    if include_date and date.weekday() <= 4:
        return date
    return date + dt.timedelta(days=7 - date.weekday() if date.weekday() > 3 else 1)


@lru_cache
def __get_next_nth_imm_date(date: ql.Date, n: int, mainCycle: bool = True):
    if n == 0:
        return date
    elif n < 0:
        raise ValueError(f"{n} is smaller than 0!")
    return __get_next_nth_imm_date(ql.IMM.nextDate(date), n - 1, mainCycle)


def get_next_nth_imm_date(
    date: dt.date | ql.Date, n: int, mainCycle: bool = True
) -> ql.Date:
    """
    Return the n'st delivery date for next contract listed in the International Money Market section of the Chicago Mercantile Exchange.

    **Note**: This is because BBG relative futures roll at market open on the IMM date.
    """
    if isinstance(date, dt.date) or isinstance(date, dt.datetime):
        date = ql.Date.from_date(date)
    return __get_next_nth_imm_date(date, n, mainCycle)


@lru_cache(maxsize=5)
def read_meeting_dates(currency: str):
    central_banks = {
        "USD": "FOMC",  # rolls on the meeting date + 1
        "EUR": "ECB",  # rolls on the next monday after the meeting date!
        "GBP": "BOE",  # rolls on the meeting date + 1
        "CAD": "BOC",  # rolls on the effective date / meeting date + 1
        "JPY": "BOJ",  # rolls on the meeting date
    }
    file_path = os.path.join(
        os.path.abspath(os.path.join(os.path.dirname(__file__), "..")),
        "loader",
        "meeting_dates",
        f"{central_banks[currency.upper()]}.csv",
    )
    meeting_dates = pd.read_csv(
        file_path, index_col=False, header=None, names=["date"], dtype={"date": str}
    )
    meeting_dates["date"] = pd.to_datetime(
        meeting_dates["date"], format="%Y%m%d"
    ).dt.date
    return meeting_dates


@lru_cache(maxsize=8)
def get_next_2_meeting_dates(
    date: dt.date, n: int, currency: str
) -> Tuple[ql.Date, ql.Date]:
    """
    Return the next 2 central bank meeting dates given the date and currency calendar.

    If *date* itself is a central bank meeting date, *date* counts towards the first meeting date (USD, GBP). Review this!

    **Note**: This is because BBG relative meeting date swaps roll on the next date of the central bank meeting date (USD, GBP).
    """

    effective_lag = {
        "USD": 0,
        "EUR": 6,  # effective date is 6 days after meeting date
        "GBP": 0,
        "CAD": 1,
        "JPY": 1,
    }
    meeting_dates = read_meeting_dates(currency).copy()
    meeting_dates = meeting_dates[
        meeting_dates["date"].apply(lambda x: x.year).isin([date.year, date.year + 1])
    ]  # initial filtering
    meeting_dates.loc[:, "date"] = meeting_dates["date"] + dt.timedelta(
        days=effective_lag[currency]
    )  # add lags between policy date and effective date
    meeting_dates.loc[:, "date"] = meeting_dates["date"].apply(
        lambda d: next_weekday(d, True)
    )
    effective_date = next_weekday(
        date + dt.timedelta(days=effective_lag[currency]), True
    )
    if currency in ["USD", "GBP", "CAD"]:
        future_meeting_dates = meeting_dates[meeting_dates["date"] >= effective_date]
    elif currency in ["JPY"]:
        future_meeting_dates = meeting_dates[meeting_dates["date"] > effective_date]
    elif currency in ["EUR"]:
        future_meeting_dates = meeting_dates[
            meeting_dates["date"] - dt.timedelta(days=2) > date
        ]  # rolls on the next monday after the meeting date
    if future_meeting_dates.shape[0] >= n + 1 and n >= 1:
        idx = future_meeting_dates.index[n - 1]
        return ql.Date.from_date(meeting_dates.loc[idx, "date"]), ql.Date.from_date(
            meeting_dates.loc[idx + 1, "date"]
        )
    raise ValueError(
        "Can't find next meeting date! Please update the meeting date data in meeting_dates folder"
    )
