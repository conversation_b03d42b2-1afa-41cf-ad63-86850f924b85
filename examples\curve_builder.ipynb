%load_ext autoreload
%autoreload 2

import sys
import os
investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), ".."))
sys.path.append(investment_parent_path)
import pandas as pd
import datetime as dt
import QuantLib as ql
from IPython.core.interactiveshell import InteractiveShell
# Set this to ensure that all outputs are displayed without truncation
InteractiveShell.ast_node_interactivity = "all"

from curves import sofrCurve, ffCurve
from helpers.plot_tools import plot_curves, plot_discount_curves
from helpers.date_helpers import to_ql_date
from plot_utils import quick_plot_curve

sofr_cal = ql.UnitedStates(ql.UnitedStates.SOFR)
fed_cal = ql.UnitedStates(ql.UnitedStates.FederalReserve)
sofr_cal.isBusinessDay(ql.Date(14,10,2024))

sofr_cal.advance(ql.Date(30,11,2024), ql.Period("1M"), ql.ModifiedFollowing)

sofr_cal.advance(ql.Date(30,11,2024), ql.Period("1M"), ql.ModifiedFollowing, True)

sofr_cal.advance(ql.Date(16,10,2024), ql.Period("1M"), ql.ModifiedFollowing)

sofr_cal.advance(ql.Date(18,10,2024), ql.Period(0, ql.Days), ql.ModifiedFollowing)

sofr_cal.advance(ql.Date(19,10,2024), ql.Period(0, ql.Days), ql.ModifiedFollowing)

# Define a range of dates to check (e.g., for the year 2024)
start_date = ql.Date(1, 1, 2024)
end_date = ql.Date(31, 12, 2024)

# Iterate through the range and find differing holidays
for i in range((end_date - start_date) + 1):
    date = start_date + i
    if sofr_cal.isHoliday(date) != fed_cal.isHoliday(date):
        print(f"Different on: {date}, SOFR Holiday: {sofr_cal.isHoliday(date)}, Fed Holiday: {fed_cal.isHoliday(date)}")


sofr_cal.advance(ql.Date(29,3,2024), ql.Period(0, ql.Days), ql.ModifiedFollowing)

fed_cal.advance(ql.Date(29,3,2024), ql.Period(0, ql.Days), ql.ModifiedFollowing)

df = pd.DataFrame([
    '18M',
    '2Y',
    '3Y',
    '4Y',
    '5Y',
    '6Y',
    '7Y',
    '8Y',
    '9Y',
    '10Y',
    '11Y',
    '12Y',
    '15Y',
    '20Y',
    '25Y',
    '30Y',
    '40Y',
    '50Y'],
columns=['Term'])

df.to_csv('./CurveDefinition/USD_SOFR.csv', encoding='utf-8', index=False)

from loader.curve_data_loader import curveBenchmarkLoader
cv = curveBenchmarkLoader("USD_SOFR")

data = cv.get_benchmarks(dt.date(2024,10,16))
data

flatCurve = ql.FlatForward(10, ql.TARGET(), ql.QuoteHandle(ql.SimpleQuote(0.05)), ql.Actual360())
quick_plot_curve(flatCurve)
print(flatCurve.referenceDate())



ql.Settings.instance().evaluationDate = to_ql_date(dt.date.today())
swap_helper = ql.OISRateHelper(
                    2, # settlement date = accrual start date
                    ql.Period("3y"),
                    ql.QuoteHandle(
                        ql.SimpleQuote(3 / 100)
                    ),
                    ql.Sofr(),
                    paymentLag=2, # payment date is 2 days after maturity date
                    pillar=ql.Pillar.MaturityDate, # use maturity date (accrual end date) as pillar date
                    paymentConvention=ql.ModifiedFollowing,
                    paymentFrequency=ql.Annual,
                    paymentCalendar=ql.UnitedStates(ql.UnitedStates.FederalReserve),
                    fixedPaymentFrequency=ql.Annual,
                    fixedCalendar=ql.UnitedStates(ql.UnitedStates.FederalReserve),
                )

swap = swap_helper.swap()

print(dt.date.today())
print(swap.startDate())
print(swap.maturityDate())
print()
print(swap_helper.earliestDate())
print(swap_helper.maturityDate())
print(swap_helper.latestDate())
print(swap_helper.latestRelevantDate())
print(swap_helper.pillarDate())

print(swap.fixedDayCount())
print(swap.floatingDayCount())

pd.DataFrame([{
    'accrualStart': cf.accrualStartDate().ISO(),
    'accrualEnd': cf.accrualEndDate().ISO(),
    "paymentDate": cf.date().ISO()
} for cf in map(ql.as_fixed_rate_coupon, swap.leg(0))])

pd.DataFrame([{
    'fixingDate': cf.fixingDate().ISO(),
    'accrualStart': cf.accrualStartDate().ISO(),
    'accrualEnd': cf.accrualEndDate().ISO(),
    "paymentDate": cf.date().ISO()
} for cf in map(ql.as_floating_rate_coupon, swap.floatingLeg())])

meeting_date_swap_helper = ql.DatedOISRateHelper(
                    ql.Date(7,11,2024),
                    ql.Date(18,12,2024),
                    ql.QuoteHandle(
                        ql.SimpleQuote(4.648 / 100)
                    ),
                    ql.Sofr(),
                    paymentLag=0,
                    paymentConvention=ql.ModifiedFollowing,
                    paymentFrequency=ql.Annual,
                    paymentCalendar=ql.UnitedStates(ql.UnitedStates.FederalReserve),
                    fixedPaymentFrequency=ql.Annual,
                    fixedCalendar=ql.UnitedStates(ql.UnitedStates.FederalReserve),
                )

meeting_date_swap_helper.maturityDate()

meeting_date_swap_helper.pillarDate()

sofr_curve = sofrCurve("2024-10-16")
ql.Settings.instance().evaluationDate = to_ql_date(sofr_curve.date)

benchmark_meta_data, bbg_bar_data = sofr_curve.fetch_curve_benchmarks()
benchmark_meta_data.merge(bbg_bar_data, left_on='ticker', right_index=True)

condition = benchmark_meta_data.apply(lambda row: row["instrument_type"] == "OvernightIndexSwap" and ql.Period(row["maturities"]) <= ql.Period('3M'), axis=1)
sub_benchmark_meta_data = benchmark_meta_data[condition].copy()
sub_benchmark_meta_data["pillar_date"] = sub_benchmark_meta_data.apply(
            lambda row: sofr_curve.calendar.advance(
                sofr_curve.calendar.advance(
                    to_ql_date(sofr_curve.date), ql.Period(row["maturities"]), sofr_curve.business_day_convention
                ),
                ql.Period(sofr_curve.swap_payment_lag, ql.Days),
                sofr_curve.business_day_convention,
            ),
            axis=1,
        )
row = sub_benchmark_meta_data[sub_benchmark_meta_data["pillar_date"] >= ql.Date(7,11,2024)].sort_values(by="pillar_date").iloc[0]
row

sofr_curve_helpers = sofr_curve.set_up_helpers()
sofr_curve_helpers.pop(0)

first_meeting_date_swap_helper =  ql.OISRateHelper(0,
    ql.Period(1,ql.Months), ql.QuoteHandle(ql.SimpleQuote(4.7824/100)),
    sofr_curve.index,
    pillar=ql.Pillar.CustomDate,
    customPillarDate=ql.Date(7,11,2024),
    paymentLag=2
)
sofr_curve_helpers += [first_meeting_date_swap_helper]

print(first_meeting_date_swap_helper.earliestDate())
print(first_meeting_date_swap_helper.maturityDate())
print(first_meeting_date_swap_helper.latestDate())
print(first_meeting_date_swap_helper.latestRelevantDate())
print(first_meeting_date_swap_helper.pillarDate())

sofr_curve.curve = ql.PiecewiseLogMixedLinearCubicDiscount(
                    to_ql_date(sofr_curve.date),
                    sofr_curve_helpers,
                    sofr_curve.day_counter,
                    [],
                    [],
                    ql.LogMixedLinearCubic(
                        n=8, behavior=ql.MixedInterpolation.SplitRanges
                    ))
sofr_curve.curve.enableExtrapolation()

plot_curves(sofr_curve)

sofr_curve = sofrCurve("2024-10-16")
sofr_curve.calibrate()

ff_curve = ffCurve("2024-10-16")
ff_curve.calibrate()

plot_curves([sofr_curve, ff_curve])

sofr_curve_flatthencubic = sofrCurve("20241016")
sofr_curve_flatthencubic_reconstruct = sofrCurve("20241016")
sofr_curve_flat = sofrCurve("20241016", interp_method='PiecewiseLogLinearDiscount')
sofr_curve_flat_reconstruct = sofrCurve("20241016", interp_method='PiecewiseLogLinearDiscount')
sofr_curve_flatthencubic.calibrate()
sofr_curve_flat.calibrate()

pd.set_option('display.float_format', '{:.16f}'.format)
original = pd.concat([pd.DataFrame(sofr_curve_flatthencubic.curve.nodes(), columns=['FlatCubic Pillar Date', 'FlatCubic DCF']), pd.DataFrame(sofr_curve_flat.curve.nodes(), columns=['Flat Pillar Date', 'Flat DCF'])], axis=1)
original

_, dcfs_fc = zip(*sofr_curve_flatthencubic.curve.nodes())
print('%.30f'%(dcfs_fc[1]))

print('%.30f'%(original.iloc[1,1])) # precision is not lost

original.dtypes

dates_fc, dcfs_fc = zip(*sofr_curve_flatthencubic.curve.nodes())
dates_fc = list(dates_fc)
dcfs_fc = list(dcfs_fc)
n = sofr_curve_flatthencubic.flat_pillar_size
sofr_curve_flatthencubic_reconstruct.curve = ql.LogMixedLinearCubicDiscountCurve(dates_fc,dcfs_fc,sofr_curve_flatthencubic.day_counter,sofr_curve_flatthencubic.calendar,ql.LogMixedLinearCubic(n=n,behavior=ql.MixedInterpolation.SplitRanges))

dates_ff, dcfs_ff = zip(*sofr_curve_flat.curve.nodes())
dates_ff = list(dates_ff)
dcfs_ff = list(dcfs_ff)
sofr_curve_flat_reconstruct.curve = ql.DiscountCurve(dates_ff,dcfs_ff,sofr_curve_flatthencubic.day_counter,sofr_curve_flatthencubic.calendar,ql.LogLinear())

reconstruct = pd.concat([pd.DataFrame(sofr_curve_flatthencubic_reconstruct.curve.nodes(), columns=['RC FlatCubic Pillar Date', 'RC FlatCubic DCF']), pd.DataFrame(sofr_curve_flat_reconstruct.curve.nodes(), columns=['RC Flat Pillar Date', 'RC Flat DCF'])], axis=1)
pd.concat([original, reconstruct], axis=1)

plot_curves([sofr_curve_flatthencubic, sofr_curve_flatthencubic_reconstruct],"","30y")

plot_curves([sofr_curve_flat, sofr_curve_flat_reconstruct],"","30y")

plot_discount_curves([sofr_curve_flatthencubic, sofr_curve_flatthencubic_reconstruct],"","30y")

plot_discount_curves([sofr_curve_flat, sofr_curve_flat_reconstruct],"","30y")

dates = [
            ql.Date(serial)
            for serial in range(ql.Date(16,10,2024).serialNumber(), ql.Date(10,11,2024).serialNumber() + 1)
        ]

dcfs = [sofr_curve.curve.discount(d) for d in dates]
pd.set_option('display.float_format', '{:.16f}'.format)
pd.DataFrame(data = dcfs, index = dates)

sofr_curve = sofrCurve("2024-10-01")
sofr_curve.calibrate()

sofr_curve = sofrCurve("2024-10-01", interp_method="PiecewiseLogLinearDiscount")
sofr_curve.calibrate()

plot_curves([sofr_curve])

dt.date(2024,11,7)-dt.date(2024,10,1)

print(ql.__version__)

import datetime as dt
from utils import get_next_imm_date
get_next_imm_date(dt.date(2024,9,17),1)

get_next_imm_date(dt.date(2024,9,18),1)

import math
from scipy.stats import norm

def black_scholes_futures_option_price(F, K, T, r, sigma, option_type='call'):
    """
    Calculate the price of a futures option using the Black model (Black-76).
    
    Parameters:
    F : float : Futures price
    K : float : Strike price
    T : float : Time to expiration (in years)
    r : float : Risk-free interest rate (as a decimal, e.g., 0.05 for 5%)
    sigma : float : Implied volatility (as a decimal, e.g., 0.15 for 15%)
    option_type : str : 'call' or 'put'
    
    Returns:
    float : Option price
    """
    
    # Calculate d1 and d2
    d1 = (math.log(F / K) + (0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    # Discount factor for the risk-free rate
    discount_factor = math.exp(-r * T)
    
    # Price calculation
    if option_type == 'call':
        option_price = discount_factor * (F * norm.cdf(d1) - K * norm.cdf(d2))
    elif option_type == 'put':
        option_price = discount_factor * (K * norm.cdf(-d2) - F * norm.cdf(-d1))
    else:
        raise ValueError("option_type must be 'call' or 'put'")
    
    return option_price

# Example inputs
F = 95.597       # Futures price
K = 95.75       # Strike price
T = 0.1424        # Time to expiration (in years), e.g., 3 months = 0.25 years
r = 0.047        # Risk-free rate (5% or 0.05 as a decimal)
sigma = 0.703/100    # Implied volatility (15% or 0.15 as a decimal)
option_type = 'call'  # Change to 'put' for put option pricing

# Calculate the option price
option_price = black_scholes_futures_option_price(F, K, T, r, sigma, option_type)
print(f"The price of the {option_type} option is: {option_price:.4f}")


def normal_bachelier_futures_option_price(F, K, T, r, sigma, option_type='call'):
    """
    Calculate the price of a futures option using the Normal (Bachelier) model.
    
    Parameters:
    F : float : Futures price
    K : float : Strike price
    T : float : Time to expiration (in years)
    r : float : Risk-free interest rate (as a decimal, e.g., 0.05 for 5%)
    sigma : float : Implied volatility (as a decimal, e.g., 0.15 for 15%)
    option_type : str : 'call' or 'put'
    
    Returns:
    float : Option price
    """
    
    # Calculate d1 for the Normal model
    d1 = (F - K) / (sigma * math.sqrt(T))
    
    # Discount factor for the risk-free rate
    discount_factor = math.exp(-r * T)
    
    # Price calculation
    if option_type == 'call':
        option_price = discount_factor * ((F - K) * norm.cdf(d1) + sigma * math.sqrt(T) * norm.pdf(d1))
    elif option_type == 'put':
        option_price = discount_factor * ((K - F) * norm.cdf(-d1) + sigma * math.sqrt(T) * norm.pdf(d1))
    else:
        raise ValueError("option_type must be 'call' or 'put'")
    
    return option_price

# Example inputs
F = 95.597       # Futures price
K = 95.625       # Strike price
T = 0.1424        # Time to expiration (in years), e.g., 3 months = 0.25 years
r = 0.047        # Risk-free rate (5% or 0.05 as a decimal)
sigma = 67/100    # Implied volatility (15% or 0.15 as a decimal)
option_type = 'call'  # Change to 'put' for put option pricing

# Calculate the option price
option_price = normal_bachelier_futures_option_price(F, K, T, r, sigma, option_type)
print(f"The price of the {option_type} option is: {option_price:.4f}")

s = r"c:\Users\<USER>\Documents\investment\loader\curve_benchmark_data\USD_SOFR_20240101_marketclose.parquet"
stored_sofr_market_data = pd.read_parquet(s)
stored_sofr_market_data.loc[(stored_sofr_market_data.index >= dt.date(2024,9,17)) & (stored_sofr_market_data.index <= dt.date(2024,9,19))].iloc[:,19:]

