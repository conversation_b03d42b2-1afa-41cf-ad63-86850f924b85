%load_ext autoreload
%autoreload 2

import sys
import os
investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), ".."))
sys.path.append(investment_parent_path)

import datetime as dt
import numpy as np
import pandas as pd
from xbbg import blp
import QuantLib as ql
from loader.ir_future_option_data_loader import interestRateFutureOptionLoader
from pricer.ir_future_option_pricer import interestRateFutureOptionPricer
from backtest.option_pair_calendar import OptionCalendarBacktester
pd.options.plotting.backend = "plotly"
pd.set_option('display.max_rows', 200)  # Default is 60
pd.set_option('display.max_columns', 200)  # Default is 20

backtester = OptionCalendarBacktester(
    ir_option_name="0q",
    short_period=30,
    long_period=90,
    option_type="25p",
    premium_ratio_entry=0.4,
    exit_days=5,
    position_size=2500,
    start_date=dt.date(2020, 1, 1),
    end_date=dt.date(2025, 2, 28),
)


np.busday_count(dt.date(2025,5,14), dt.date(2025,5,15))

backtester.prepare_data()

data = backtester.fill_missing_data()

backtester.signal

backtester.signal["vol_ratio"].iloc[-5:].mean()

backtester.signal["vol_ratio"].rolling(window=5).mean()

6/8

backtester.clear_results()

backtester.generate_report()

backtester.tradable_universe

backtester.results[0].trade_log.style.format(precision=4).format("{:,.2f}", subset=["pnl", "pnl_long", "pnl_short"]).map(lambda v: "color:" + ("red" if v < 0 else "green" if v > 0  else "black") , subset=["pnl", "pnl_long", "pnl_short"])

backtester.results[0].trade_log.style.format(precision=4).format("{:,.2f}", subset=["pnl", "pnl_long", "pnl_short"]).applymap(lambda v: "color:" + ("red" if v < 0 else "green" if v > 0  else "black") , subset=["pnl", "pnl_long", "pnl_short"])

backtester.setup_backtest()

results = backtester.cerebro.run()

result = results[0]

result

# [i for i in dir(result) if not i.startswith("data")]

dates = result.lines.datetime.plot()
cash = result.observers.broker.lines.cash.plot()
market_value = result.observers.broker.lines.value.plot()

from backtrader.utils.dateintern import num2date

pd.DataFrame(
    {"market_value": market_value,
    "cash": cash},
    index = [num2date(i) for i in dates]
).plot()

backtester.analyzed_results

backtester.results_analysis["drawdown_analysis"]

backtester.results_analysis["sharpe"]

backtester.results[0].analyzers.sharpe

backtester.signal["premium_ratio"].iloc[-30:]

