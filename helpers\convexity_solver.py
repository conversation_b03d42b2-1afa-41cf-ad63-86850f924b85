import numpy as np
import pandas as pd
import QuantLib as ql
from scipy.optimize import minimize


def calculate_convexity(variance, T1, T2, model: str = "Ho-Lee"):
    convexity = variance / 2 * (2 * (T2 - T1) * T1 + T1**2) / 1e4**2
    return convexity


def calibrate_convexity_variance(
    swaps: list[ql.FixedVsFloatingSwap],
    ois_curve: ql.YieldTermStructure,
    vol_dim: int | None = None,
):
    ref_date = ois_curve.referenceDate()
    swap_floating_schedules = [None] * len(swaps)
    fixedLegNPVs = [None] * len(swaps)
    if vol_dim is None:
        vol_dim = int(
            max(
                [
                    1 + np.floor((swap.maturityDate() - ref_date) / 365 - 0.2)
                    for swap in swaps
                ]
            )
        )

    for i in range(len(swaps)):
        swap_floating_schedules[i] = pd.DataFrame(
            [
                {
                    "T1": cf.dayCounter().yearFraction(ref_date, cf.accrualStartDate()),
                    "T2": cf.dayCounter().yearFraction(ref_date, cf.accrualEndDate()),
                    "rate": cf.rate(),
                    "amount": cf.amount(),
                    "discount": ois_curve.discount(cf.date()),
                }
                for cf in map(ql.as_floating_rate_coupon, swaps[i].floatingLeg())
            ]
        )
        swap_floating_schedules[i]["idx"] = np.minimum(
            np.maximum(np.floor(swap_floating_schedules[i]["T2"] - 0.2), 0), vol_dim
        )
        fixedLegNPVs[i] = swaps[i].fixedLegNPV()
        assert (
            abs(
                (
                    swap_floating_schedules[i]["amount"]
                    * swap_floating_schedules[i]["discount"]
                ).sum()
                - abs(swaps[i].floatingLegNPV())
            )
            < 1
        ), "Swap pricing error!"

    def errorFunc(variance):
        error = 0
        for i in range(len(swap_floating_schedules)):
            variance_schedules = swap_floating_schedules[i]["idx"].map(
                lambda idx: variance[int(idx)]
            )
            convexity = calculate_convexity(
                variance_schedules,
                swap_floating_schedules[i]["T1"],
                swap_floating_schedules[i]["T2"],
            )
            floatingLegNPV = (
                swap_floating_schedules[i]["amount"]
                * (1 - convexity / swap_floating_schedules[i]["rate"])
                * swap_floating_schedules[i]["discount"]
            ).sum()
            error += (floatingLegNPV - fixedLegNPVs[i]) ** 2
        return error

    result = minimize(
        errorFunc, x0=[100**2] * vol_dim, bounds=[(-(200**2), 200**2)] * vol_dim
    )

    return result.x
