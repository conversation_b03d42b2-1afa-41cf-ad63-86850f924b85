{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "import datetime as dt\n", "from helpers.date_helpers import read_meeting_dates"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2010-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2010-03-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010-04-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2010-06-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2010-08-10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2026-07-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>2026-09-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>2026-10-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2026-12-09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2027-01-27</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>138 rows × 1 columns</p>\n", "</div>"], "text/plain": ["           date\n", "0    2010-01-27\n", "1    2010-03-16\n", "2    2010-04-28\n", "3    2010-06-23\n", "4    2010-08-10\n", "..          ...\n", "133  2026-07-29\n", "134  2026-09-16\n", "135  2026-10-28\n", "136  2026-12-09\n", "137  2027-01-27\n", "\n", "[138 rows x 1 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["usd = read_meeting_dates(\"usd\").copy()\n", "usd"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2010-01-27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010-03-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2010-04-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2010-06-23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2026-06-17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>2026-07-29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>2026-09-16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2026-10-28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2026-12-09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>138 rows × 1 columns</p>\n", "</div>"], "text/plain": ["           date\n", "0          None\n", "1    2010-01-27\n", "2    2010-03-16\n", "3    2010-04-28\n", "4    2010-06-23\n", "..          ...\n", "133  2026-06-17\n", "134  2026-07-29\n", "135  2026-09-16\n", "136  2026-10-28\n", "137  2026-12-09\n", "\n", "[138 rows x 1 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["usd.shift(1)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["usd[\"diff_dates\"] = usd.diff().dropna()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["usd = usd.dropna()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["usd.loc[:, ['diff_days']] = usd['diff_dates'].apply(lambda d: d.days)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>diff_dates</th>\n", "      <th>diff_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2010-03-16</td>\n", "      <td>48 days, 0:00:00</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2010-04-28</td>\n", "      <td>43 days, 0:00:00</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2010-06-23</td>\n", "      <td>56 days, 0:00:00</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2010-08-10</td>\n", "      <td>48 days, 0:00:00</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2010-09-21</td>\n", "      <td>42 days, 0:00:00</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>133</th>\n", "      <td>2026-07-29</td>\n", "      <td>42 days, 0:00:00</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>134</th>\n", "      <td>2026-09-16</td>\n", "      <td>49 days, 0:00:00</td>\n", "      <td>49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>135</th>\n", "      <td>2026-10-28</td>\n", "      <td>42 days, 0:00:00</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>136</th>\n", "      <td>2026-12-09</td>\n", "      <td>42 days, 0:00:00</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>137</th>\n", "      <td>2027-01-27</td>\n", "      <td>49 days, 0:00:00</td>\n", "      <td>49</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>137 rows × 3 columns</p>\n", "</div>"], "text/plain": ["           date        diff_dates  diff_days\n", "1    2010-03-16  48 days, 0:00:00         48\n", "2    2010-04-28  43 days, 0:00:00         43\n", "3    2010-06-23  56 days, 0:00:00         56\n", "4    2010-08-10  48 days, 0:00:00         48\n", "5    2010-09-21  42 days, 0:00:00         42\n", "..          ...               ...        ...\n", "133  2026-07-29  42 days, 0:00:00         42\n", "134  2026-09-16  49 days, 0:00:00         49\n", "135  2026-10-28  42 days, 0:00:00         42\n", "136  2026-12-09  42 days, 0:00:00         42\n", "137  2027-01-27  49 days, 0:00:00         49\n", "\n", "[137 rows x 3 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["usd"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>diff_dates</th>\n", "      <th>diff_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>2020-03-15</td>\n", "      <td>12 days, 0:00:00</td>\n", "      <td>12</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          date        diff_dates  diff_days\n", "82  2020-03-15  12 days, 0:00:00         12"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["usd[usd[\"diff_days\"] <= 31]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}