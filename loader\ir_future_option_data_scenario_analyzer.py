import os
import QuantLib as ql
import datetime as dt
import numpy as np
import pandas as pd
from functools import lru_cache
from scipy.interpolate import interp1d
from xbbg import blp
from loader.config import load_yaml_config
from loader.ir_future_option_data_loader import interestRateFutureOptionLoader


class interestRateFutureOptionScenarioAnalyzer(interestRateFutureOptionLoader):
    OPTION_DATA_FOLDER = "ir_future_option_data_live"
    PRIMARY_TO_WEEKLIES_MAPPER = {
        "TU": "W",
        "FV": "I",
        "TY": "M",
        "US": "C",
        "WN": "J",
    }
    WEEKLIES_TO_PRIMARY_MAPPER = {
        weekly: primary for primary, weekly in PRIMARY_TO_WEEKLIES_MAPPER.items()
    }

    def __init__(self, ir_option_name: str, **params):
        super().__init__(ir_option_name, **params)

    @lru_cache(maxsize=2)
    def cached_file_path(self, date: dt.date, use_live=True) -> str:
        return os.path.join(
            self.cached_file_dir(),
            f"{self.ir_option_name}_{date.strftime('%Y%m')}_{'live' if use_live else 'close'}.parquet",
        )

    @staticmethod
    def _filter_by_time_to_maturity(group, time_to_maturity: int):
        less_than = group[group["tte_bus_days"] <= time_to_maturity]
        greater_than = group[group["tte_bus_days"] > time_to_maturity]

        if less_than.empty:
            closest_less_than = greater_than.iloc[0]
            closest_greater_than = greater_than.iloc[0]
        elif greater_than.empty:
            closest_less_than = less_than.iloc[-1]
            closest_greater_than = less_than.iloc[-1]
        else:
            closest_less_than = less_than.iloc[-1]
            closest_greater_than = greater_than.iloc[0]
        return pd.concat(
            [
                closest_less_than.rename(lambda x: f"{x}_1"),
                closest_greater_than.rename(lambda x: f"{x}_2"),
            ]
        )

    def calculate_closest_strike(
        self,
        atm: float,
        strike: float,
        tte: int,
        tte_bus_days: int = None,
        direction: str = "round",
        atm_vol: float = None,
    ):
        config = load_yaml_config(
            self.ir_option_name, "ir_future_option_config", self.currency
        )
        min_tick_size = config["min_tick_size"]
        if (
            "min_tick_size_constraint_month" in config
            and tte > 28 * config["min_tick_size_constraint_month"]
        ):
            min_tick_size = config["tick_size"]
        elif abs(atm - strike) >= config["min_tick_size_range"]:
            min_tick_size = config["tick_size"]
        self._print("min_tick_size:", min_tick_size)
        # Add logic to handle the case when strike is very in/out of money
        if atm_vol is not None:
            var = atm_vol * np.sqrt(tte_bus_days / 252)
            threshold = 2 * var
            if self.is_bond_future:
                threshold = (
                    atm * np.exp(threshold + 0.5 * var**2) - atm
                    if strike > atm
                    else atm - atm * np.exp(-threshold + 0.5 * var**2)
                )
            if abs(strike - atm) > threshold:
                self._print("atm: ", atm, ", strike: ", strike, end=", ")
                strike = atm + np.sign(strike - atm) * threshold
                min_tick_size *= 2
                self._print("filtered strike: ", strike)
            # if strike > atm + threshold:
            #     strike = np.floor((atm + threshold) / min_tick_size) * min_tick_size
            # elif strike < atm - threshold:
            #     strike = np.ceil((atm - threshold) / min_tick_size) * min_tick_size
            # pseudo-delta outside the range of N^{-1}[5%, 95%] are deemed unreliable and hence filtered out for further consideration
        if direction == "round":
            return round(strike / min_tick_size) * min_tick_size
        elif direction == "ceil":
            return np.ceil(strike / min_tick_size) * min_tick_size
        elif direction == "floor":
            return (np.ceil(strike / min_tick_size) - 1) * min_tick_size
        else:
            raise ValueError(f"Invalid direction: {direction}")

    def _load_close_data(self, date: dt.date, tickers: list[str], flds="PX_SETTLE"):
        def fetch_and_process_data(tickers, field, bus_date):
            self._print(
                f"Fetching close data ({field}) from Bloomberg on {bus_date}:", tickers
            )
            data = (
                blp.bdp(tickers, field)
                if field == "PREV_CLOSE_VAL" or field == "PX_MID"
                else blp.bdh(tickers, field, bus_date, bus_date)
            )
            if not data.empty:
                data = self._process_underlying_price_data(data, bus_date)
            return data

        bus_date = (
            self.calendar.advance(
                ql.Date.from_date(date), ql.Period(-1, ql.Days)
            ).to_date()
            if date == dt.date.today() or date.weekday() >= 5
            else date
        )
        used_flds = (
            "PREV_CLOSE_VAL"
            if date == dt.date.today() and not isinstance(flds, list)
            else flds
        )
        file = self.cached_file_path(bus_date, False)
        if os.path.exists(file):
            try:
                data = pd.read_parquet(file)
            except Exception:
                data = pd.DataFrame()
            missing_tickers = tickers
            if bus_date in data.index.get_level_values(0).unique():
                exist_tickers = data.loc[bus_date].index.get_level_values(0)
                missing_tickers = list(set(tickers) - set(exist_tickers))
            if len(missing_tickers) > 0:
                missing_data = fetch_and_process_data(
                    missing_tickers, used_flds, bus_date
                )
                data = pd.concat([data, missing_data], axis=0).sort_index()
                data.to_parquet(file)
        else:
            data = fetch_and_process_data(tickers, used_flds, bus_date)
            if not data.empty:
                self._print("Saving data to", file)
                os.makedirs(self.cached_file_dir(), exist_ok=True)
                data.to_parquet(file)
        if not isinstance(flds, list):
            return data.loc[bus_date].loc[tickers]
        data = data.loc[bus_date]
        # data = data[~data.index.duplicated(keep="first")]
        # is_ctd_fwd_risk = data.index.str.endswith("CTD_FORWARD_DV01")
        # data_ctd_fwd_risk = data[is_ctd_fwd_risk]
        # data = data[~is_ctd_fwd_risk]
        # data_ctd_fwd_risk.index = data.index.str.removesuffix(" CTD_FORWARD_DV01")
        # data_ctd_fwd_risk.rename(columns={"price": "CTD_FORWARD_DV01"}, inplace=True)
        # data = pd.concat([data, data_ctd_fwd_risk], axis=1)
        return data.loc[tickers]

    def _load_data(
        self,
        date: dt.date,
        tickers: list[str],
        flds="PX_SETTLE",
        use_live=True,
        add_live_suffix=False,
        # is_underlying=False,
    ):
        if use_live:
            self._print("Fetching live data (PX_MID) from Bloomberg:", tickers)
            # if self.is_bond_future and is_underlying:
            #     data = blp.bdp(tickers, ["PX_MID", "CONVENTIONAL_CTD_FORWARD_FRSK"])
            # else:
            data = blp.bdp(tickers, "PX_MID")
            missing_tickers = tickers
            if not data.empty:
                mapper = {
                    "PX_MID": "price",
                    # "CONVENTIONAL_CTD_FORWARD_FRSK": "CTD_FORWARD_DV01",
                }
                data.rename(
                    columns=lambda col: mapper.get(col.upper(), col.upper()),
                    inplace=True,
                )
                if add_live_suffix:
                    data["live"] = True
                missing_tickers = list(set(tickers) - set(data.index))
            if len(missing_tickers) > 0:
                # if self.is_bond_future and is_underlying:
                #     flds = ["PX_SETTLE", "CONVENTIONAL_CTD_FORWARD_FRSK"]
                missing_data = self._load_close_data(date, missing_tickers, flds)
                if not missing_data.empty:
                    if add_live_suffix:
                        missing_data["live"] = False
                data = pd.concat([data, missing_data], axis=0).sort_index()
            self._print("Fetched live data: ", data)
            return data
        # if self.is_bond_future and is_underlying:
        #     flds = ["PX_SETTLE", "CONVENTIONAL_CTD_FORWARD_FRSK"]
        data = self._load_close_data(date, tickers, flds)
        self._print("Fetched close data: ", data)
        return data

    @staticmethod
    def _process_underlying_price_data(raw_futures_price: pd.DataFrame, date: dt.date):
        if raw_futures_price.columns.nlevels == 1:
            raw_futures_price.set_index(
                pd.MultiIndex.from_product(
                    [[date], raw_futures_price.index],
                    names=["date", "ticker"],
                ),
                inplace=True,
            )
            raw_futures_price = raw_futures_price.set_axis(
                ["price"], axis=1
            )  # raise error when multiple columns, dropna if needed
            return raw_futures_price
        return interestRateFutureOptionLoader._process_underlying_price_data(
            raw_futures_price
        )

    @staticmethod
    def calculate_moneyness(F, K, atm_vol, T, kind="standardized-normal"):
        match kind:
            case "standardized-normal":
                return (K - F) / (atm_vol * np.sqrt(T / 252))
            case "standardized-lognormal":
                return np.log(K / F) / (atm_vol * np.sqrt(T / 252))
            case "log-normal":  # Normal Model for STIR futures
                return -np.log((100 - K) / (100 - F))
            case "log-lognormal":  # Lognormal Model for bond futures
                return np.log(K / F)
            case "simple":
                return K / F
            case "absolute":
                return K - F

    @staticmethod
    def calculate_strike_from_moneyness(
        F, moneyness, atm_vol, T, kind="standardized-normal"
    ):
        match kind:
            case "standardized-normal":
                return F + moneyness * atm_vol * np.sqrt(T / 252)
            case "standardized-lognormal":
                return F * np.exp(moneyness * atm_vol * np.sqrt(T / 252))
            case "log-normal":
                return 100 - (100 - F) * np.exp(-moneyness)
            case "log-lognormal":
                return F * np.exp(moneyness)
            case "simple":
                return F * moneyness
            case "absolute":
                return F + moneyness

    @staticmethod
    def interpolate_skew_vol_by_moneyness(
        row, i, moneyness_type="standardized-normal", use_live=False
    ):
        # live futures price when use_live is True, otherwise close futures price
        live_F = row[f"atmf_{i}"]
        F = np.array(
            [
                (
                    row[f"atmf_close_{i}"]
                    if use_live and not row[f"live_{i}_0"]
                    else live_F
                ),
                (
                    row[f"atmf_close_{i}"]
                    if use_live and not row[f"live_{i}_1"]
                    else live_F
                ),
            ]
        )
        K = row[[f"strike_{i}_0", f"strike_{i}_1"]].to_numpy(dtype=float)
        atm_vol = row[f"atm_vol_{i}"]
        vol = row[[f"skew_vol_{i}_0", f"skew_vol_{i}_1"]].to_numpy(dtype=float)
        T = row[f"tte_bus_days_{i}"]
        target_moneyness = row["moneyness"]
        x = interestRateFutureOptionScenarioAnalyzer.calculate_moneyness(
            F, K, atm_vol, T, moneyness_type
        )
        f = interp1d(x, vol, kind="linear", fill_value="extrapolate")
        if target_moneyness < x.min():
            var = atm_vol * np.sqrt(T / 252)
            threshold = 3.5 * var
            if moneyness_type.endswith("lognormal"):
                threshold_moneyness = (
                    interestRateFutureOptionScenarioAnalyzer.calculate_moneyness(
                        live_F,
                        live_F * np.exp(-threshold + 0.5 * var**2),
                        atm_vol,
                        T,
                        moneyness_type,
                    )
                )
            else:
                threshold_moneyness = (
                    interestRateFutureOptionScenarioAnalyzer.calculate_moneyness(
                        live_F, live_F - threshold, atm_vol, T, moneyness_type
                    )
                )
            return f(min(x.min(), max(threshold_moneyness, target_moneyness)))
        elif target_moneyness > x.max():
            var = atm_vol * np.sqrt(T / 252)
            threshold = 3.5 * var
            if moneyness_type.endswith("lognormal"):
                threshold_moneyness = (
                    interestRateFutureOptionScenarioAnalyzer.calculate_moneyness(
                        live_F,
                        live_F * np.exp(threshold + 0.5 * var**2),
                        atm_vol,
                        T,
                        moneyness_type,
                    )
                )
            threshold_moneyness = (
                interestRateFutureOptionScenarioAnalyzer.calculate_moneyness(
                    live_F, live_F + threshold, atm_vol, T, moneyness_type
                )
            )
            return f(max(x.max(), min(threshold_moneyness, target_moneyness)))
        return f(target_moneyness)

    @staticmethod
    def interpolate_vol_by_time(vol, tte, target_tte: int):
        var = vol**2 * tte
        f = interp1d(
            tte, var, kind="linear", fill_value="extrapolate"
        )  # Review this line
        return np.sqrt(f(target_tte) / target_tte)

    @staticmethod
    def interpolate_call_price(call_px, K, vol, T, target_K):
        call_1, call_2, K_1, K_2 = (
            call_px[:, 0],
            call_px[:, 1],
            K[:, 0],
            K[:, 1],
        )
        vol = vol * np.sqrt(T / 252)
        vol_1, vol_2, vol_t = vol[:, 0], vol[:, 1], vol[:, 2]
        alpha = (vol_2 - vol_t) / (vol_2 - vol_1)
        return target_K * (alpha * call_1 / K_1 + (1 - alpha) * call_2 / K_2)

    def prepare_positions(self, ql_date: ql.Date, ir_option_tickers: list[str]):
        positions = pd.DataFrame(ir_option_tickers, columns=["ticker"])
        positions[["ticker_root", "call_put", "strike"]] = positions[
            "ticker"
        ].str.extract(r"(\w+)([cpCP])\s+(\d+(?:\.\d+)?)\s*\w*")
        positions.dropna(inplace=True)
        positions["ticker_root"] = positions["ticker_root"].str.upper()
        positions["call_put"] = positions["call_put"].str.upper()
        positions["strike"] = positions["strike"].astype(float)
        positions["imm_code"] = positions["ticker_root"].str.removeprefix(
            self.ir_option_name
        )
        weeklies = positions["imm_code"].str.len() >= 4
        positions.loc[weeklies, "option_expiry_date"] = positions.loc[
            weeklies, "imm_code"
        ].map(
            lambda code: ql.IMM.date(code[2:], ql_date - ql.Period(1, ql.Months))
        )  # Weeklies can expire after the imm date
        positions.loc[weeklies, "option_expiry_date"] = positions.loc[weeklies].apply(
            lambda row: self.calendar.advance(
                ql.Date.nthWeekday(
                    int(row["imm_code"][0]),
                    ql.Friday,
                    row["option_expiry_date"].month(),
                    row["option_expiry_date"].year(),
                ),
                0,
                ql.Days,
                ql.Preceding,
            ),
            axis=1,
        )
        positions.loc[~weeklies, "option_expiry_date"] = positions.loc[
            ~weeklies, "imm_code"
        ].map(lambda imm_code: self._get_option_expiry_date(imm_code, ql_date))
        positions["tte_bus_days"] = positions["option_expiry_date"].map(
            lambda expiry_date: self.calendar.businessDaysBetween(ql_date, expiry_date)
        )
        positions["fut_ticker"] = positions["imm_code"].map(
            lambda code: self.get_underlying_futures_tickers(
                [code],
                self.ir_option_name,
                ql_date,
            )[0]
        )
        positions.drop(columns=["ticker", "ticker_root"], inplace=True, errors="ignore")
        return positions

    def prepare_option_candidates(self, ql_date: ql.Date):
        candidates = pd.DataFrame(
            self.get_single_period_option_chain_imm_codes(ql_date),
            columns=["imm_code"],
        )
        candidates["option_expiry_date"] = candidates["imm_code"].map(
            lambda imm_code: self._get_option_expiry_date(imm_code, ql_date)
        )
        candidates["tte"] = candidates["option_expiry_date"] - ql_date
        candidates["tte_bus_days"] = candidates["option_expiry_date"].map(
            lambda expiry_date: self.calendar.businessDaysBetween(ql_date, expiry_date)
        )
        if self.is_bond_future:
            if (
                sep := self.PRIMARY_TO_WEEKLIES_MAPPER.get(self.ir_option_name)
            ) is not None:
                weeklies = self.prepare_weeklies_candidates(ql_date, sep=sep)
                weeklies = weeklies[~weeklies["tte"].isin(candidates["tte"])]
                candidates = pd.concat(
                    [candidates, weeklies], axis=0, ignore_index=True
                )
        candidates = candidates[candidates["tte_bus_days"] > 0]
        candidates.sort_values(by="tte_bus_days", ignore_index=True, inplace=True)
        self._print(candidates)
        return candidates

    def prepare_weeklies_candidates(self, ql_date: ql.Date, sep: str, weeks: int = 3):
        option_expiry_dates = []
        friday = ql.Date.nthWeekday(1, ql.Friday, ql_date.month(), ql_date.year())
        if friday <= ql_date:
            friday += (1 + (ql_date - friday) // 7) * 7
        for _ in range(weeks):
            option_expiry_dates.append(
                (
                    np.ceil(friday.dayOfMonth() / 7).astype(int).astype(str),
                    ql.IMM.nextCode(ql.Date.startOfMonth(friday), False),
                    self.calendar.advance(friday, 0, ql.Days, ql.Preceding),
                )
            )
            friday += 7
        weeklies = pd.DataFrame(
            option_expiry_dates, columns=["week", "imm_code", "option_expiry_date"]
        )
        weeklies["tte"] = weeklies["option_expiry_date"] - ql_date
        weeklies["tte_bus_days"] = weeklies["option_expiry_date"].map(
            lambda expiry_date: self.calendar.businessDaysBetween(ql_date, expiry_date)
        )
        weeklies["imm_code"] = weeklies["week"].str.cat(weeklies["imm_code"], sep=sep)
        weeklies.drop(columns=["week"], inplace=True)
        self._print(weeklies)
        return weeklies

    def load_and_calculate_rolldown_vol(
        self,
        date: dt.date,
        ir_option_tickers: list[str],
        fut_px_bump: list[float],
        bd_bump: list[int] = [0],
        use_live: bool = True,
        moneyness_type: str = "standardized-normal",
        fut_tickers: list[str] = None,
    ) -> pd.DataFrame:
        use_live = use_live and date == dt.date.today()
        if use_live and date.weekday() >= 5:
            return np.nan
        # Review: Check ir option names
        ql_date = ql.Date.from_date(date)
        # Positions
        positions = self.prepare_positions(ql_date, ir_option_tickers)
        # Option chain candidates
        candidates = self.prepare_option_candidates(ql_date)
        if isinstance(fut_tickers, list) and any(fut_tickers):
            assert len(fut_tickers) == len(
                fut_px_bump
            ), "Please provide correct future tickers and px bumps"
            positions = positions.merge(
                pd.DataFrame(
                    {
                        "fut_ticker": fut_tickers,
                        "fut_px_bump": fut_px_bump,
                    }
                ),
                how="left",
                on="fut_ticker",
            )
            assert (
                positions["fut_px_bump"].notna().all()
            ), "Please provide all the required futures px bumps"
            fut_px_bump = [0.0]

        scenarios = [None] * (len(bd_bump) * len(fut_px_bump))
        underlying_tickers, ois_tickers = set(), set()
        for i in range(len(fut_px_bump)):
            for j in range(len(bd_bump)):
                ql_date_bump = self.calendar.advance(
                    ql_date, ql.Period(-bd_bump[j], ql.Days)
                )
                scenario = positions.copy(deep=False)
                if "fut_px_bump" not in positions.columns:
                    scenario["fut_px_bump"] = fut_px_bump[i]
                scenario["bd_bump"] = bd_bump[j]
                scenario["tte"] = np.maximum(
                    scenario[f"option_expiry_date"] - ql_date_bump, 0
                )
                scenario["tte_bus_days"] = np.maximum(
                    scenario["tte_bus_days"] + bd_bump[j], 0
                )
                scenario = scenario.join(
                    scenario["tte_bus_days"].apply(
                        lambda bd: self._filter_by_time_to_maturity(candidates, bd)
                    )
                )
                scenario[f"ois_ticker"] = scenario[f"tte"].apply(
                    lambda tte: self.get_ois_ticker(self.ois_root, tte)
                )
                ois_tickers.update(scenario[f"ois_ticker"].unique())
                for k in range(1, 3):
                    scenario[f"fut_ticker_{k}"] = scenario[f"imm_code_{k}"].apply(
                        lambda code: self.get_underlying_futures_tickers(
                            [code],
                            self.ir_option_name,
                            ql_date,
                        )[0]
                    )
                    underlying_tickers.update(scenario[f"fut_ticker_{k}"].unique())
                    # scenarios[i,j][f"ois_ticker_{k}"] = scenarios[i,j][
                    #     f"tte_{k}"
                    # ].apply(lambda tte: self.get_ois_ticker(self.ois_root, tte))
                    # ois_tickers.update(
                    #     scenarios[i,j][f"ois_ticker_{k}"].unique()
                    # )
                scenarios[i * len(bd_bump) + j] = scenario

        "****************** Load the futures data and OIS data ******************"
        self._print("Futures tickers:", underlying_tickers)
        self._print("OIS tickers:", ois_tickers)
        ois_rates = self._load_data(
            date,
            list(ois_tickers),
            flds="PX_LAST",
            use_live=False,
        )
        futures_data = self._load_data(
            date,
            list(underlying_tickers),
            use_live=use_live,  # is_underlying=True
        )  # live data for futures if use_live
        if use_live:
            futures_close_data = self._load_data(
                date,
                list(underlying_tickers),
                use_live=False,  # is_underlying=True
            )

        "****************** Merge the futures px and discount factors ******************"
        scenarios = pd.concat(scenarios, axis=0, ignore_index=True)
        scenarios = scenarios.merge(
            futures_data,
            how="left",
            left_on="fut_ticker",
            right_index=True,
        ).rename(columns={"price": "fut_px"})
        if not isinstance(fut_tickers, list) or not any(fut_tickers):
            if fut_px_bump[0] <= 25:
                scenarios["fut_px_bump"] = (
                    scenarios["fut_px"] + scenarios["fut_px_bump"]
                )  # Parallel shift
        self._print(scenarios)

        for k in range(1, 3):
            scenarios = scenarios.merge(
                futures_data,
                how="left",
                left_on=f"fut_ticker_{k}",
                right_index=True,
            ).rename(
                columns={
                    "price": f"atmf_{k}",
                    # "CTD_FORWARD_DV01": f"CTD_FORWARD_DV01_{k}",
                }
            )
            if use_live:
                scenarios = scenarios.merge(
                    futures_close_data,
                    how="left",
                    left_on=f"fut_ticker_{k}",
                    right_index=True,
                ).rename(columns={"price": f"atmf_close_{k}"})
            scenarios = scenarios.merge(
                ois_rates,
                how="left",
                left_on=f"ois_ticker",
                right_index=True,
            ).rename(columns={"price": f"discount_factor_{k}"})
            scenarios[f"discount_factor_{k}"] = 1 / (
                1
                + scenarios[f"discount_factor_{k}"]
                * scenarios[f"tte_{k}"]
                / (100 * self.day_counter_denominator)
            )
            scenarios.drop(
                columns=[
                    f"option_expiry_date_{k}",
                    f"fut_ticker_{k}",
                ],
                inplace=True,
                errors="ignore",
            )
        scenarios["discount_factor"] = 1 / (
            1
            + (1 / scenarios["discount_factor_1"] - 1)
            * scenarios["tte"]
            / scenarios["tte_1"]
        )
        scenarios.drop(
            columns=["option_expiry_date", "ois_ticker"],
            inplace=True,
            errors="ignore",
        )

        ticker_formatter = np.vectorize(
            lambda imm_code, strike, option_type: (
                (f"{self.ir_option_name}" if ql.IMM.isIMMcode(imm_code, False) else "")
                + f"{imm_code}{option_type} {np.format_float_positional(strike, precision=5, trim="-", fractional=True)} Comdty"
            )
        )
        "****************** Compute the ATM option tickers ******************"
        atm_option_tickers = set()
        for i in range(1, 3):
            scenarios[f"strike_{i}"] = scenarios.apply(
                lambda row: self.calculate_closest_strike(
                    row[f"atmf_{i}"], row[f"atmf_{i}"], row[f"tte_{i}"]
                ),
                axis=1,
            )
            scenarios[f"ticker_{i}_0"] = ticker_formatter(
                scenarios[f"imm_code_{i}"], scenarios[f"strike_{i}"], "C"  # Call
            )
            scenarios[f"ticker_{i}_1"] = ticker_formatter(
                scenarios[f"imm_code_{i}"], scenarios[f"strike_{i}"], "P"  # Put
            )
            atm_option_tickers.update(set(scenarios[f"ticker_{i}_0"]))
            atm_option_tickers.update(set(scenarios[f"ticker_{i}_1"]))

        "****************** Load the ATM option data ******************"
        self._print("ATM tickers:", atm_option_tickers)
        atm_option_data = self._load_data(
            date, list(atm_option_tickers), use_live=use_live, add_live_suffix=True
        )

        "****************** Compute the ATM vol ******************"
        for i in range(1, 3):
            for j in range(2):  # 0 for call, 1 for put
                scenarios = scenarios.merge(
                    atm_option_data,
                    how="left",
                    left_on=f"ticker_{i}_{j}",
                    right_index=True,
                )
                scenarios.rename(columns={"price": f"atm_vol_{i}_{j}"}, inplace=True)
                if self.is_bond_future:
                    scenarios[f"atm_vol_{i}_{j}"] = scenarios.apply(
                        lambda opt: ql.blackFormulaImpliedStdDev(
                            -2 * j + 1,
                            opt[f"strike_{i}"],
                            (
                                opt[f"atmf_close_{i}"]
                                if use_live and not opt["live"]
                                else opt[f"atmf_{i}"]
                            ),
                            opt[f"atm_vol_{i}_{j}"],
                            opt[f"discount_factor_{i}"],
                        ),
                        axis=1,
                    )
                    scenarios[f"atm_vol_{i}_{j}"] *= np.sqrt(
                        252 / scenarios[f"tte_bus_days_{i}"]
                    )
                else:
                    scenarios[f"atm_vol_{i}_{j}"] = scenarios.apply(
                        lambda opt: ql.bachelierBlackFormulaImpliedVol(
                            2 * j - 1,
                            100 - opt[f"strike_{i}"],
                            (
                                100 - opt[f"atmf_close_{i}"]
                                if use_live and not opt["live"]
                                else 100 - opt[f"atmf_{i}"]
                            ),
                            opt[f"tte_bus_days_{i}"] / 252,
                            opt[f"atm_vol_{i}_{j}"],
                            opt[f"discount_factor_{i}"],
                        ),
                        axis=1,
                    )
                scenarios.drop(columns="live", inplace=True, errors="ignore")
            scenarios[f"atm_vol_{i}"] = scenarios[
                [f"atm_vol_{i}_0", f"atm_vol_{i}_1"]
            ].mean(axis=1)
            scenarios.drop(
                columns=[f"atm_vol_{i}_0", f"atm_vol_{i}_1"],
                inplace=True,
                errors="ignore",
            )

        "****************** Split short expiries ******************"
        short_expiry_condition = scenarios["tte_bus_days"] < scenarios["tte_bus_days_1"]
        scenarios.loc[
            short_expiry_condition, ["tte_1", "tte_bus_days_1", "atm_vol_1"]
        ] = 0
        scenarios["atm_vol"] = scenarios.apply(
            lambda row: self.interpolate_vol_by_time(
                row[["atm_vol_1", "atm_vol_2"]].to_numpy(dtype=float),
                row[["tte_bus_days_1", "tte_bus_days_2"]].to_numpy(dtype=float),
                row["tte_bus_days"],
            ),
            axis=1,
        )
        scenarios_short_expiry = scenarios[short_expiry_condition]
        scenarios = scenarios[~short_expiry_condition]

        "****************** Compute the option strikes required ******************"
        skew_option_tickers = set()
        if not scenarios.empty:
            if self.is_bond_future and not moneyness_type.endswith("lognormal"):
                moneyness_type = moneyness_type.replace("normal", "lognormal")
            scenarios["moneyness"] = self.calculate_moneyness(
                scenarios["fut_px_bump"],
                scenarios["strike"],
                scenarios["atm_vol"],
                scenarios["tte_bus_days"],
                moneyness_type,
            )
            for i in range(1, 3):  # 1 for first expiry, 2 for second expiry
                scenarios[f"strike_{i}"] = self.calculate_strike_from_moneyness(
                    scenarios[f"atmf_{i}"],  # sticky delta
                    scenarios["moneyness"],
                    scenarios[f"atm_vol_{i}"],
                    scenarios[f"tte_bus_days_{i}"],
                    moneyness_type,
                )
                for j in range(2):  # 0 for floor, 1 for ceil
                    scenarios[f"strike_{i}_{j}"] = scenarios.apply(
                        lambda row: self.calculate_closest_strike(
                            row[f"atmf_{i}"],  # sticky delta
                            row[f"strike_{i}"],
                            row[f"tte_{i}"],
                            row[f"tte_bus_days_{i}"],
                            "ceil" if j else "floor",
                            row[f"atm_vol_{i}"],
                        ),
                        axis=1,
                    )
                    scenarios[f"ticker_{i}_{j}"] = ticker_formatter(
                        scenarios[f"imm_code_{i}"],
                        scenarios[f"strike_{i}_{j}"],
                        scenarios["call_put"],  # Call
                    )
                    skew_option_tickers.update(set(scenarios[f"ticker_{i}_{j}"]))
        # scenarios.drop(columns=["strike_1", "strike_2"], inplace=True, errors="ignore")

        "****************** Compute the option strikes required for short expiries ******************"
        if not scenarios_short_expiry.empty:
            short_expiry_moneyness_type = (
                "log-lognormal" if self.is_bond_future else "absolute"
            )
            scenarios_short_expiry["moneyness"] = self.calculate_moneyness(
                scenarios_short_expiry["fut_px_bump"],
                scenarios_short_expiry["strike"],
                scenarios_short_expiry["atm_vol"],
                scenarios_short_expiry["tte_bus_days"],
                short_expiry_moneyness_type,
            )
            scenarios_short_expiry["strike_2"] = self.calculate_strike_from_moneyness(
                scenarios_short_expiry["atmf_2"],  # sticky delta
                scenarios_short_expiry["moneyness"],
                scenarios_short_expiry["atm_vol_2"],
                scenarios_short_expiry["tte_bus_days_2"],
                short_expiry_moneyness_type,
            )
            for j in range(2):  # 0 for floor, 1 for ceil
                scenarios_short_expiry[f"strike_2_{j}"] = scenarios_short_expiry.apply(
                    lambda row: self.calculate_closest_strike(
                        row["atmf_2"],  # sticky delta
                        row["strike_2"],
                        row["tte_2"],
                        row[f"tte_bus_days_{i}"],
                        "ceil" if j else "floor",
                        row["atm_vol_2"],
                    ),
                    axis=1,
                )
                scenarios_short_expiry[f"ticker_2_{j}"] = ticker_formatter(
                    scenarios_short_expiry["imm_code_2"],
                    scenarios_short_expiry[f"strike_2_{j}"],
                    scenarios_short_expiry["call_put"],  # Call
                )
                skew_option_tickers.update(set(scenarios_short_expiry[f"ticker_2_{j}"]))

        "****************** Load the option data ******************"
        self._print("Skew tickers:", sorted(skew_option_tickers - atm_option_tickers))
        skew_option_data = self._load_data(
            date,
            list(skew_option_tickers - atm_option_tickers),
            use_live=use_live,
            add_live_suffix=True,
        )
        skew_option_data = pd.concat([skew_option_data, atm_option_data], axis=0)

        "****************** Compute the skew vol ******************"
        if not scenarios.empty:
            for i in range(1, 3):
                for j in range(2):
                    scenarios = scenarios.merge(
                        skew_option_data,
                        how="left",
                        left_on=f"ticker_{i}_{j}",
                        right_index=True,
                    )
                    scenarios.rename(
                        columns={"price": f"skew_px_{i}_{j}", "live": f"live_{i}_{j}"},
                        inplace=True,
                    )
                    if self.is_bond_future:
                        scenarios[f"skew_vol_{i}_{j}"] = scenarios.apply(
                            lambda opt: ql.blackFormulaImpliedStdDev(
                                1 if opt["call_put"] == "C" else -1,
                                opt[f"strike_{i}_{j}"],
                                (
                                    opt[f"atmf_close_{i}"]
                                    if use_live and not opt[f"live_{i}_{j}"]
                                    else opt[f"atmf_{i}"]
                                ),
                                opt[f"skew_px_{i}_{j}"],
                                opt[f"discount_factor_{i}"],
                            ),
                            axis=1,
                        )
                        scenarios[f"skew_vol_{i}_{j}"] *= np.sqrt(
                            252 / scenarios[f"tte_bus_days_{i}"]
                        )
                    else:
                        scenarios[f"skew_vol_{i}_{j}"] = scenarios.apply(
                            lambda opt: ql.bachelierBlackFormulaImpliedVol(
                                -1 if opt["call_put"] == "C" else 1,
                                100 - opt[f"strike_{i}_{j}"],
                                (
                                    100 - opt[f"atmf_close_{i}"]
                                    if use_live and not opt[f"live_{i}_{j}"]
                                    else 100 - opt[f"atmf_{i}"]
                                ),
                                opt[f"tte_bus_days_{i}"] / 252,
                                opt[f"skew_px_{i}_{j}"],
                                opt[f"discount_factor_{i}"],
                            ),
                            axis=1,
                        )
                scenarios.loc[scenarios[f"skew_px_{i}_0"] == 0, f"skew_vol_{i}_0"] = (
                    scenarios.loc[scenarios[f"skew_px_{i}_0"] == 0, f"skew_vol_{i}_1"]
                )
                scenarios.loc[scenarios[f"skew_px_{i}_1"] == 0, f"skew_vol_{i}_1"] = (
                    scenarios.loc[scenarios[f"skew_px_{i}_1"] == 0, f"skew_vol_{i}_0"]
                )
                scenarios.loc[scenarios[f"skew_vol_{i}_0"] == 0, f"skew_vol_{i}_0"] = (
                    scenarios.loc[scenarios[f"skew_vol_{i}_0"] == 0, f"atm_vol_{i}"]
                )
                scenarios.loc[scenarios[f"skew_vol_{i}_1"] == 0, f"skew_vol_{i}_1"] = (
                    scenarios.loc[scenarios[f"skew_vol_{i}_1"] == 0, f"atm_vol_{i}"]
                )
                scenarios[f"skew_vol_{i}"] = scenarios.apply(
                    lambda row: self.interpolate_skew_vol_by_moneyness(
                        row, i, moneyness_type=moneyness_type, use_live=use_live
                    ),
                    axis=1,
                ).astype(float)

            scenarios["vol"] = scenarios.apply(
                lambda row: self.interpolate_vol_by_time(
                    row[["skew_vol_1", "skew_vol_2"]].to_numpy(dtype=float),
                    row[["tte_bus_days_1", "tte_bus_days_2"]].to_numpy(dtype=float),
                    row["tte_bus_days"],
                ),
                axis=1,
            )
            if self.is_bond_future:
                scenarios["option_px"] = scenarios.apply(
                    lambda opt: ql.blackFormula(
                        1 if opt["call_put"] == "C" else -1,
                        opt["strike"],
                        opt["fut_px_bump"],
                        opt["vol"] * np.sqrt(opt["tte_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )
            else:
                scenarios["option_px"] = scenarios.apply(
                    lambda opt: ql.bachelierBlackFormula(
                        -1 if opt["call_put"] == "C" else 1,
                        100 - opt["strike"],
                        100 - opt["fut_px_bump"],
                        opt["vol"] * np.sqrt(opt["tte_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )

        "****************** Compute the skew vol for short expiries  ******************"
        if not scenarios_short_expiry.empty:
            for j in range(2):
                scenarios_short_expiry = scenarios_short_expiry.merge(
                    skew_option_data,
                    how="left",
                    left_on=f"ticker_2_{j}",
                    right_index=True,
                )
                scenarios_short_expiry.rename(
                    columns={"price": f"skew_px_2_{j}", "live": f"live_2_{j}"},
                    inplace=True,
                )
                if self.is_bond_future:
                    scenarios_short_expiry[f"skew_vol_2_{j}"] = (
                        scenarios_short_expiry.apply(
                            lambda opt: ql.blackFormulaImpliedStdDev(
                                1 if opt["call_put"] == "C" else -1,
                                opt[f"strike_2_{j}"],
                                (
                                    opt["atmf_close_2"]
                                    if use_live and not opt[f"live_2_{j}"]
                                    else opt["atmf_2"]
                                ),
                                opt[f"skew_px_2_{j}"],
                                opt["discount_factor_2"],
                            ),
                            axis=1,
                        )
                    )
                    scenarios_short_expiry[f"skew_vol_2_{j}"] *= np.sqrt(
                        252 / scenarios["tte_bus_days_2"]
                    )
                else:
                    scenarios_short_expiry[f"skew_vol_2_{j}"] = (
                        scenarios_short_expiry.apply(
                            lambda opt: ql.bachelierBlackFormulaImpliedVol(
                                -1 if opt["call_put"] == "C" else 1,
                                100 - opt[f"strike_2_{j}"],
                                (
                                    100 - opt["atmf_close_2"]
                                    if use_live and not opt[f"live_2_{j}"]
                                    else 100 - opt["atmf_2"]
                                ),
                                opt["tte_bus_days_2"] / 252,
                                opt[f"skew_px_2_{j}"],
                                opt["discount_factor_2"],
                            ),
                            axis=1,
                        )
                    )
            scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_px_2_0"] == 0, "skew_vol_2_0"
            ] = scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_px_2_0"] == 0, "skew_vol_2_1"
            ]
            scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_px_2_1"] == 0, "skew_vol_2_1"
            ] = scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_px_2_1"] == 0, "skew_vol_2_0"
            ]
            scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_vol_2_0"] == 0, "skew_vol_2_0"
            ] = scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_vol_2_0"] == 0, "atm_vol_2"
            ]
            scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_vol_2_1"] == 0, "skew_vol_2_1"
            ] = scenarios_short_expiry.loc[
                scenarios_short_expiry["skew_vol_2_1"] == 0, "atm_vol_2"
            ]
            scenarios_short_expiry["skew_vol_2"] = scenarios_short_expiry.apply(
                lambda row: self.interpolate_skew_vol_by_moneyness(
                    row,
                    2,
                    moneyness_type=short_expiry_moneyness_type,
                    use_live=use_live,
                ),
                axis=1,
            ).astype(float)

            # Undiscounted call price at 0 and second expiry, discount factor = 1
            # use_call_interp = 1
            scenarios_short_expiry["call_interp"] = 1
            scenarios_short_expiry["call_px_at_0"] = 0
            # scenarios_short_expiry["call_px_at_0"] = np.maximum(
            #     0,
            #     use_call_interp
            #     * (
            #         scenarios_short_expiry["fut_px_bump"]
            #         - scenarios_short_expiry["strike"]
            #     ),
            # )

            scenarios_short_expiry.loc[
                scenarios_short_expiry["fut_px_bump"]
                >= scenarios_short_expiry["strike"],
                "call_interp",
            ] = -1

            if self.is_bond_future:
                scenarios_short_expiry["call_px_2"] = scenarios_short_expiry.apply(
                    lambda opt: ql.blackFormula(
                        opt["call_interp"],
                        opt["strike_2"],
                        opt["atmf_2"],
                        opt["skew_vol_2"] * np.sqrt(opt["tte_bus_days_2"] / 252),
                        1,
                    ),
                    axis=1,
                )
            else:
                scenarios_short_expiry["call_px_2"] = scenarios_short_expiry.apply(
                    lambda opt: ql.bachelierBlackFormula(
                        -opt["call_interp"],
                        100 - opt["strike_2"],
                        100 - opt["atmf_2"],
                        opt["skew_vol_2"] * np.sqrt(opt["tte_bus_days_2"] / 252),
                        1,
                    ),
                    axis=1,
                )
            # Interpolation based on the call price at the same moneyness from 0 to first expiry
            # Jim Gatheral: Arbitrage-free SVI volatility surfaces
            scenarios_short_expiry["atm_vol_1"] = np.maximum(
                scenarios_short_expiry["atm_vol_2"], scenarios_short_expiry["atm_vol"]
            )
            scenarios_short_expiry["call_px"] = self.interpolate_call_price(
                scenarios_short_expiry[["call_px_at_0", "call_px_2"]].to_numpy(
                    dtype=float
                ),
                scenarios_short_expiry[["strike", "strike_2"]].to_numpy(dtype=float),
                scenarios_short_expiry[["atm_vol_1", "atm_vol_2", "atm_vol"]].to_numpy(
                    dtype=float
                ),
                scenarios_short_expiry[
                    ["tte_bus_days_1", "tte_bus_days_2", "tte_bus_days"]
                ].to_numpy(dtype=float),
                scenarios_short_expiry["strike"].to_numpy(dtype=float),
            )
            if self.is_bond_future:
                scenarios_short_expiry["vol"] = scenarios_short_expiry.apply(
                    lambda opt: ql.blackFormulaImpliedStdDev(
                        1 if opt["call_put"] == "C" else -1,
                        opt["strike"],
                        opt["fut_px_bump"],
                        opt["call_px"],
                        1,
                    ),
                    axis=1,
                )
                scenarios_short_expiry["vol"] *= np.sqrt(
                    252 / scenarios_short_expiry[f"tte_bus_days"]
                )
            else:
                scenarios_short_expiry["vol"] = scenarios_short_expiry.apply(
                    lambda opt: ql.bachelierBlackFormulaImpliedVol(
                        -opt["call_interp"],
                        100 - opt["strike"],
                        100 - opt["fut_px_bump"],
                        opt["tte_bus_days"] / 252,
                        opt["call_px"],
                        1,
                    ),
                    axis=1,
                )
            scenarios_short_expiry.loc[scenarios_short_expiry["vol"] == 0, "vol"] = (
                scenarios_short_expiry.loc[
                    scenarios_short_expiry["vol"] == 0, "skew_vol_2"
                ]
            )

            if self.is_bond_future:
                scenarios_short_expiry["option_px"] = scenarios_short_expiry.apply(
                    lambda opt: ql.blackFormula(
                        1 if opt["call_put"] == "C" else -1,
                        opt["strike"],
                        opt["fut_px_bump"],
                        opt["vol"] * np.sqrt(opt["tte_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )
            else:
                scenarios_short_expiry["option_px"] = scenarios_short_expiry.apply(
                    lambda opt: ql.bachelierBlackFormula(
                        -1 if opt["call_put"] == "C" else 1,
                        100 - opt["strike"],
                        100 - opt["fut_px_bump"],
                        opt["vol"] * np.sqrt(opt["tte_bus_days"] / 252),
                        opt["discount_factor"],
                    ),
                    axis=1,
                )

        output_normal_expiry, output_short_expiry = None, None
        if not scenarios.empty:
            output_normal_expiry = scenarios[
                [
                    "call_put",
                    "strike",
                    "imm_code",
                    "fut_px_bump",
                    "bd_bump",
                    "tte_bus_days",
                    "vol",
                    "option_px",
                ]
            ]
        if not scenarios_short_expiry.empty:
            output_short_expiry = scenarios_short_expiry[
                [
                    "call_put",
                    "strike",
                    "imm_code",
                    "fut_px_bump",
                    "bd_bump",
                    "tte_bus_days",
                    "vol",
                    "option_px",
                ]
            ]

        output = pd.concat(
            [output_normal_expiry, output_short_expiry], axis=0
        ).sort_index()
        if self.debug_mode:
            return {
                "scenarios": scenarios,
                "scenarios_short_expiry": scenarios_short_expiry,
                "output": output,
            }
        return output


def load_and_calculate_rolldown_sabr_vol(
    self,
    date: dt.date,
    ir_option_tickers: list[str],
    fut_px_bump: list[float],
    bd_bump: list[int] = [0],
    use_live: bool = True,
    moneyness_type: str = "log-normal",
) -> pd.DataFrame:
    pass
