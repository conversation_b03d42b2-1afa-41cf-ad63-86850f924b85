import os
import pandas as pd
import datetime as dt
from functools import lru_cache
from curves.config import available_curves
from .curve_data_loader import curveBenchmarkLoader

CurveCalibrationResultFolderName = "curve_calibration_results"


class curveCalibrator:
    def __init__(self, name: str, data_source: str) -> None:
        """
        Initialize the curve calibrator.

        Parameters
        ----------
        name : Name of the curve to calibrate.
        data_source : Data source to use for calibration, e.g. BBG, JPM, etc.
        """
        name = name.replace("_", ".")
        if name not in available_curves:
            raise ValueError(
                f"Curve name {name} is not valid. Available curves: {list(available_curves.keys())}"
            )
        self.curve_name = name.replace(".", "_")
        self.data_source = data_source

    @staticmethod
    @lru_cache
    def __cached_file_path(
        curve_name: str, data_source: str, effective_date: dt.date | None = None
    ):
        if effective_date is None:
            return os.path.join(
                os.path.dirname(__file__),
                CurveCalibrationResultFolderName,
                f"{curve_name}_{data_source}_calib.parquet",
            )
        return os.path.join(
            os.path.dirname(__file__),
            CurveCalibrationResultFolderName,
            f"{curve_name}_{effective_date.strftime('%Y%m%d')}_{data_source}_calib.parquet",
        )

    @staticmethod
    @lru_cache
    def _load_cached_calibration_data(
        curve_name: str, data_source: str, effective_date: dt.date | None = None
    ):
        parquet_file = curveCalibrator.__cached_file_path(
            curve_name, data_source, effective_date
        )
        if os.path.exists(parquet_file):
            print("Opening cached calibration results:", parquet_file)
            return pd.read_parquet(parquet_file)
        print(
            "Please recalibrate the curve first. No cached calibration results found:",
            parquet_file,
        )
        return None

    @staticmethod
    def override_calibration_data(
        parquet_data, calibration_results, parquet_file, override
    ):
        if parquet_data is not None:
            if not override:  # Append new calibration results
                print("Appending new calibration results to cached results...")
                updated_results = pd.concat([parquet_data, calibration_results], axis=0)
            else:  # Override the original_data, and append new calibration results
                print("Overriding cached results with new calibration results...")
                updated_results = calibration_results.combine_first(parquet_data)
        else:
            print("No cached results found...")
            updated_results = calibration_results
        print("Storing calibration results:", parquet_file)
        updated_results = updated_results.sort_index()
        updated_results.to_parquet(parquet_file)

    def recalibrate(
        self, start_date: dt.date, end_date: dt.date, override: bool = True
    ):
        """
        Recalibrate the curve between the specified start and end dates and store results in a parquet file.

        Parameters
        ----------
        name : Name of the curve to recalibrate.
        start_date : Start date of the recalibration.
        end_date : End date of the recalibration.
        override : If True, overwrite existing parquet data. If False, only calibrate missing data.
        """

        def get_curve_points(
            row,
            benchmark_meta_data=None,
            bbg_bar_data=None,
        ):
            print(
                f"Calibrating {self.curve_name} curve ({self.data_source}) for date:",
                row.name,
            )
            curve = available_curves[self.curve_name.replace("_", ".")](
                row.name.isoformat(), data_source=self.data_source
            )
            curve.calibrate(
                benchmark_meta_data,
                bbg_bar_data.loc[row.name] if bbg_bar_data is not None else None,
            )
            pillar_dates, discount_factors = zip(*curve.curve.nodes())
            if self.data_source != "BBG":
                return pd.Series(
                    [[date.to_date() for date in pillar_dates], discount_factors],
                    index=["pillar_dates", "discount_factors"],
                )
            else:
                return pd.Series(
                    [
                        curve.flat_pillar_size,
                        [date.to_date() for date in pillar_dates],
                        discount_factors,
                    ],
                    index=["flat_pillar_size", "pillar_dates", "discount_factors"],
                )

        loader = curveBenchmarkLoader(self.curve_name)
        if self.data_source != "BBG":
            curveBenchmarkLoader._load_custom_data.cache_clear()
            curveCalibrator._load_cached_calibration_data.cache_clear()
            benchmarks, data = curveBenchmarkLoader._load_custom_data(
                self.curve_name, self.data_source
            )
            data = data[start_date:end_date].dropna(axis=0, how="all")
            parquet_file = curveCalibrator.__cached_file_path(
                self.curve_name, self.data_source
            )
            parquet_calibration_data = None
            if os.path.exists(parquet_file):
                print("Opening cached calibration results:", parquet_file)
                parquet_calibration_data = pd.read_parquet(parquet_file)
                if not override:
                    data = data.loc[
                        data.index.difference(parquet_calibration_data.index)
                    ]
            if data.empty:
                return
            calibration_results = data.apply(
                lambda row: get_curve_points(row),
                axis=1,
                result_type="expand",
            )
            curveCalibrator.override_calibration_data(
                parquet_calibration_data, calibration_results, parquet_file, override
            )
            return

        period_benchmarks, period_bbg_bar_data = loader.load_close_px_for_period(
            start_date, end_date
        )
        for benchmarks, data in zip(period_benchmarks, period_bbg_bar_data):
            effective_date = benchmarks["effective_date"]
            data = data[start_date:end_date].dropna(axis=0, how="any")
            parquet_file = curveCalibrator.__cached_file_path(
                self.curve_name, self.data_source, effective_date
            )
            parquet_calibration_data = None
            if os.path.exists(parquet_file):
                print("Opening cached calibration results:", parquet_file)
                parquet_calibration_data = pd.read_parquet(parquet_file)
                if not override:
                    data = data.loc[
                        data.index.difference(parquet_calibration_data.index)
                    ]
            if data.empty:
                continue

            calibration_results = data.apply(
                lambda row: get_curve_points(row, benchmarks["benchmarks"], data),
                axis=1,
                result_type="expand",
            )
            curveCalibrator.override_calibration_data(
                parquet_calibration_data, calibration_results, parquet_file, override
            )

    def load(self, start_date: dt.date, end_date: dt.date):
        loader = curveBenchmarkLoader(self.curve_name)
        if self.data_source != "BBG":
            parquet_calibration_data = curveCalibrator._load_cached_calibration_data(
                self.curve_name, self.data_source
            )
            if parquet_calibration_data is not None:
                parquet_calibration_data = parquet_calibration_data[start_date:end_date]
                if not parquet_calibration_data.empty:
                    return [parquet_calibration_data]
            return [None]

        period_benchmarks = loader.get_benchmarks_for_period(start_date, end_date)
        period_calibration_results = []
        for benchmarks in period_benchmarks:
            effective_date = benchmarks["effective_date"]
            parquet_calibration_data = curveCalibrator._load_cached_calibration_data(
                self.curve_name, self.data_source, effective_date
            )
            if parquet_calibration_data is not None:
                parquet_calibration_data = parquet_calibration_data[start_date:end_date]
                if not parquet_calibration_data.empty:
                    period_calibration_results.append(parquet_calibration_data)
                continue
        return period_calibration_results
