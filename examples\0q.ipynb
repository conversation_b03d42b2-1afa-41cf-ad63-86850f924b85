%load_ext autoreload
%autoreload 2

import sys
import os
investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), ".."))
sys.path.append(investment_parent_path)

import datetime as dt
import numpy as np
import pandas as pd
from xbbg import blp
import QuantLib as ql
from loader.ir_future_option_data_loader import interestRateFutureOptionLoader
from pricer.ir_future_option_pricer import interestRateFutureOptionPricer
pd.options.plotting.backend = "plotly"
pd.set_option('display.max_rows', 200)  # Default is 60
pd.set_option('display.max_columns', 200)  # Default is 20

data = blp.bdh(tickers=['SFRZ1C 97 Comdty','SFRZ1C 98 Comdty','SFRZ1 Comdty'], flds=['Security_Name','LAST_TRADEABLE_DT','MATURITY','PX_MID','PX_LAST','PX_SETTLE'], start_date='2021-01-01',end_date='2021-01-10', Per='W')
data

data.columns

data = blp.bdh(tickers=['SFRZ1C 97.0 Comdty','SFRZ1C 98 Comdty'], flds=['LAST_TRADEABLE_DT','MATURITY','PX_SETTLE'], start_date='2021-01-01',end_date='2021-01-10', Per='W')
data

data.index

data.index[0]

data = data.droplevel(1, axis=1)

data

data.melt(var_name="ticker",value_name="price",ignore_index=False)

loader = interestRateFutureOptionLoader("0q")
periods, period_option_tickers = loader.get_option_chain_tickers(dt.date(2024,10,30),dt.date(2024,11,19))

periods

data_new = blp.bdh(tickers=period_option_tickers[0], flds=['PX_SETTLE'], start_date='2024-10-14',end_date='2024-11-15')
data_new

data_new = data_new.droplevel(level=1, axis=1)
data_new = data_new.dropna(axis=1, how="all")
data_new

data_new = pd.read_parquet(loader.cached_file_path(periods[0][0].to_date()))

data_new.info()

data_new.head()

data_new = data_new.pivot(columns="ticker", values="price")

data_new.head()

data_new.stack().to_frame("price").sort_index().head() # Not good

isna = data_new.isna()

isna.sum().sum()

data_new[["0QH5C 94.9375 Comdty", "0QJ5C 94.9375 Comdty","0QJ5C 95 Comdty"]]

data_new.loc[isna.any(axis=1), isna.any(axis=0)]

data_new.shape

data_new.info()

len(period_option_tickers[0])

data_new.dtypes

data_new = data_new.melt(var_name="ticker", value_name="price", ignore_index=False)
data_new.head()

data_new = pd.read_parquet(loader.cached_file_path(periods[0][0].to_date()))
data_new.info()
data_new.head()

data_new = data_new.rename_axis(index="date")
data_new.head()

data_new[['ticker_root', 'strike']] = data_new['ticker'].str.extract(r'(\w+)\s+(\d+(?:\.\d+)?)\s+Comdty')
data_new

data_new.drop(columns="ticker", inplace=True)
data_new.info()
data_new.head()

data_new.set_index(["ticker_root", "strike"], append=True, inplace=True)
data_new.info()
data_new.head()

data_new.sort_index(inplace=True)
data_new.info()
data_new.head()

data_new.to_parquet(r"c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0Q_20241014_close_new.parquet")

data_new.stack().to_frame(name="price")

data = pd.read_parquet(r"c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0Q\\0Q_20240916_close.parquet")
data.info()
data

data.index[-1][0]

data = data.reset_index().pivot(index=["date","ticker_root"], columns="strike", values="price")
data.info()
data

data.index.get_level_values(1).unique()

dt.date(2025,1,10)-dt.date(2024,9,16)

tte = data.index.map(lambda idx: ql.IMM.date(idx[1].removeprefix(loader.ir_option_name).rstrip("cpCP"), ql.Date.from_date(idx[0])) - ql.Date.from_date(idx[0]) - 5)
tte

data["time_to_maturity"] = tte

first_group = data.loc[dt.date(2024,9,16)].sort_values(by="time_to_maturity")
first_group

less_than = first_group[first_group['time_to_maturity'] <= 90]
less_than.tail(2)

pd.concat([less_than.tail(2), None], axis=0)

def filter_ticker_roots_by_time_to_maturity(group, time_to_maturity = 90):
    group = group.sort_values(by="time_to_maturity")
    less_than = group[group['time_to_maturity'] <= time_to_maturity]
    closest_less_than = less_than.tail(2) if not less_than.empty else None
    greater_than = group[group['time_to_maturity'] > time_to_maturity]
    closest_greater_than = greater_than.head(2) if not greater_than.empty else None

    return pd.concat([closest_less_than, closest_greater_than], axis=0)

data = data.groupby(level='date', group_keys=False).apply(filter_ticker_roots_by_time_to_maturity)
data

data.loc[dt.date(2024,9,18)]

loader.get_underlying_futures_tickers(['0QU3C', '0QZ3C'], "0Q", ql.Date(17,1,2023))

data["underlying_future_ticker"] = data.index.map(lambda idx: loader.get_underlying_futures_tickers([idx[1]], loader.ir_option_name, ql.Date.from_date(idx[0]))[0])
data

data["ois_ticker"] = data["time_to_maturity"].map(lambda tte: loader.get_ois_ticker(loader.ois_root, tte))
data

underlying_data = pd.read_parquet(loader.cached_underlying_file_path(dt.date(2024,9,16)))
underlying_data

underlying_data.unstack()

data = data.reset_index()
data

merged_data = data.merge(underlying_data, how="left", left_on=["date", "underlying_future_ticker"], right_index=True)
merged_data

underlying_data.reset_index(inplace=True)
merged_data["date"] = pd.to_datetime(merged_data["date"])
underlying_data["date"] = pd.to_datetime(underlying_data["date"])
underlying_data

pd.merge_asof(merged_data, underlying_data, on="date", left_by="ois_ticker", right_by="ticker", suffixes=("_atmf","_ois"))

pd.merge_asof(merged_data, underlying_data, on="date", left_by="ois_ticker", right_by="ticker", suffixes=("_atmf","_ois"))

load_data = loader.load_option_price(dt.date(2024,1,19), dt.date(2024,10,11), maturity=90)

load_data[0].columns

load_data[1].columns

option_px_data = pd.concat(load_data, axis=0, ignore_index=True)

option_px_data

load_data.info()
load_data

load_data["sign"] = load_data["ticker_root"].map(
                lambda t: 1 if t[-1] == "P" else -1
            ).astype(int)
load_data["ticker_root"] = load_data["ticker_root"].map(lambda t: t[:-1])
load_data["price_atmf"] = 100.0 - load_data["price_atmf"]
load_data = load_data.melt(
        id_vars=[
            "date",
            "ticker_root",
            "sign",
            "tte",
            "tte_bus_days",
            "price_atmf",
            "discount_factor",
        ],
        var_name="strike",
        value_name="price",
    ).dropna(axis=0, how="any")
load_data["strike"] = 100 - load_data["strike"].astype(float)
load_data.info()
load_data

load_data = load_data[load_data["sign"] > 0].copy(deep=False)

load_data["vol"] = load_data.apply(lambda opt: ql.bachelierBlackFormulaImpliedVol(opt["sign"], opt["strike"], opt["price_atmf"], opt["tte_bus_days"]/252, opt["price"], opt["discount_factor"]), axis=1)


load_data["delta"] = load_data.apply(lambda opt: ql.bachelierBlackFormulaAssetItmProbability(opt["sign"], opt["strike"], opt["price_atmf"], opt["vol"] * math.sqrt(opt["tte_bus_days"]/252)), axis=1)

peak = load_data.set_index(["date","ticker_root"]).sort_index().loc[(dt.datetime(2024,9,16), "0QZ4")]
peak

peak["delta"].sort_values().values

# Define the bins for delta ranges
import numpy as np
bins = np.linspace(0, 1, 21)  # 10 equal bins between 0 and 1
labels = [f"{round(bins[i], 2)} - {round(bins[i+1], 2)}" for i in range(len(bins)-1)]

# Bin the delta values into these ranges
peak['delta_range'] = pd.cut(peak['delta'], bins=bins, labels=labels, include_lowest=True)

# Count the number of deltas in each range
delta_counts = peak['delta_range'].value_counts().sort_index()

print("\nCounts of deltas in each range:")
print(delta_counts)


peak

def find_closest_delta(group, delta):
    group['delta_diff'] = abs(group['delta'] - delta)
    closest_row = group.loc[group['delta_diff'].idxmin()]
    return closest_row[["tte", "vol", "delta"]]

load_data.groupby(['date', 'ticker_root', 'sign'], group_keys=False).apply(find_closest_delta, delta=0.25, include_groups=False)

load_data = load_data.groupby(['date', 'ticker_root', 'sign']).apply(find_closest_delta, delta=0.25, include_groups=False).groupby(['date', 'ticker_root']).agg({
    'tte': 'first',
    'vol': 'mean',
    'delta': 'mean',
})
load_data

from pricer.ir_future_option_pricer import interestRateFutureOptionPricer
pricer = interestRateFutureOptionPricer("0Q")

vol_data = pricer.calculate_vol(dt.date(2021,1,17), dt.date(2024,6,11), 90, ["atm"])
vol_data



whole_data = loader.load_option_price(dt.date(2021,1,1), dt.date(2024,11,15), maturity=90)



vol_data = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,11,15), 30, ["atm","27c"])
vol_data

vol_data["diff"] = vol_data["20C"] - vol_data["ATM"]

vol_data

import plotly.express as px
px.line(vol_data.reset_index(), x="date", y=["ATM", "20C"])

px.line(vol_data.reset_index(), x="date", y=["diff"])

whole_option_px_data = whole_option_px_data.reset_index().pivot(index=["date", "ticker_root"], columns="strike", values="price")

whole_option_px_data["tte"] = whole_option_px_data.index.map(
                    lambda idx: ql.IMM.date(
                        idx[1].removeprefix(loader.ir_option_name).rstrip("cpCP"),
                        ql.Date.from_date(idx[0]),
                    )
                    - ql.Date.from_date(idx[0])
                    - 5
                )

whole_option_px_data = whole_option_px_data.groupby(
                level="date", group_keys=False
            ).apply(
                interestRateFutureOptionLoader._filter_by_time_to_maturity,
                time_to_maturity=90,
            )

whole_option_px_data["tte_bus_days"] = whole_option_px_data.index.map(
                lambda idx: loader.calendar.businessDaysBetween(
                    ql.Date.from_date(idx[0]),
                    ql.IMM.date(
                        idx[1].removeprefix(loader.ir_option_name).rstrip("cpCP"),
                        ql.Date.from_date(idx[0]),
                    )
                    - ql.Period(5, ql.Days),
                )
            )
whole_option_px_data["underlying_future_ticker"] = (
    whole_option_px_data.index.map(
        lambda idx: loader.get_underlying_futures_tickers(
            [idx[1]], loader.ir_option_name, ql.Date.from_date(idx[0])
        )[0]
    )
)
whole_option_px_data["ois_ticker"] = whole_option_px_data["tte"].map(
    lambda tte: interestRateFutureOptionLoader.get_ois_ticker(
        loader.ois_root, tte
    )
)
whole_option_px_data.reset_index(inplace=True)

whole_option_px_data

from scipy.interpolate import interp1d
def interpolate_vol(group, maturity: int):
    group["var"] = group["vol"] ** 2 * group["tte"]
    f = interp1d(group['tte'], group['var'], kind='linear', fill_value="extrapolate")
    return np.sqrt(f(maturity) / maturity)

sample = vol_data.loc[dt.datetime(2024,6,17)].iloc[[0,2]]
sample

interpolate_vol(sample, 90)

ql.LinearInterpolation(sample["tte"], sample["delta"])







data_new = pd.read_parquet(r"c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0Q\\0Q_20240916_close.parquet")
data_new

data_new.index[0][0]

type(data_new.index[0][0])

data_new.index[0][0] == dt.datetime(2024,9,16)

data_new.index[0][0] == dt.date(2024,9,16)

new_index = pd.MultiIndex.from_arrays([pd.to_datetime(data_new.index.get_level_values('date')), data_new.index.get_level_values('ticker_root')], names=['date', 'ticker_root'])
data_new.index = new_index
data_new

data_new.to_parquet(r"c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0Q\\0Q_20240916_close_new.parquet")

from loader.config import load_yaml_config
config = load_yaml_config("0q", "ir_future_option_config")
config

loader = interestRateFutureOptionLoader("0q")
loader.get_periods(dt.date(2024,9,1),dt.date(2024,11,18))

whole_data = loader.load_option_price(dt.date(2024,6,1),dt.date(2024,9,15))

whole_data = loader.load_option_price(dt.date(2024,9,16),dt.date(2024,11,29))

whole_data = loader.load_option_price(dt.date(2024,11,1),dt.date(2024,11,30))

whole_data[0].index.get_level_values("date").unique()

whole_data = loader.load_option_price(dt.date(2024,9,1),dt.date(2024,10,13))

whole_data = loader.load_option_price(dt.date(2024,9,1),dt.date(2024,11,23))

loader.get_periods(dt.date(2023,11,1),dt.date(2024,1,18))

whole_data = loader.load_option_price(dt.date(2023,11,1),dt.date(2024,1,18))

whole_data = loader.load_option_price(dt.date(2020,11,16),dt.date(2020,12,12))

whole_data = loader.load_option_price(dt.date(2020,1,1),dt.date(2020,11,15))

whole_data = loader.load_option_price(dt.date(2021,11,1),dt.date(2024,11,23))

pricer = interestRateFutureOptionPricer("0q")

pd.read_parquet(r"c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0Q")

pxx = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,8,1), 90, "10c")

pxx

(pxx["strike"] - pxx["price_atmf"]).min()

pricer_3q = interestRateFutureOptionPricer("3q")

vol = pricer_3q.calculate_vol(dt.date(2021,6,1), dt.date(2024,5,12), 90, ["ATM", "10p", "10c"])

vol.plot()

loader = interestRateFutureOptionLoader("SFR")

data = loader.load_option_price(dt.date(2025,1,1), dt.date(2025,1,31), maturity=90)
data

data.groupby(
            "date", group_keys=False
        ).apply(
            interestRateFutureOptionLoader._filter_by_time_to_maturity,
            time_to_maturity=90,
            include_groups=True,
        ).reset_index(drop=True)

loader.load_option_price(dt.date(2021,1,1), dt.date(2021,6,1))

loader.load_option_price(dt.date(2021,5,1), dt.date(2021,11,1))

loader.load_option_price(dt.date(2021,10,1), dt.date(2022,5,1))

loader.load_option_price(dt.date(2022,4,1), dt.date(2023,7,1))

loader.load_option_price(dt.date(2022,6,1), dt.date(2023,2,1))

loader.load_option_price(dt.date(2023,1,1), dt.date(2024,5,1))

loader.load_option_price(dt.date(2023,3,1), dt.date(2023,4,15), check_missing_tickers=True)

loader.load_option_price(dt.date(2023,3,1), dt.date(2023,4,15), check_missing_tickers=True)

pricer = interestRateFutureOptionPricer("0Q")

vol_90 = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,11,15), 90, "ATM")
vol_60 = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,11,15), 60, "ATM")
vol_30 = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,11,15), 30, "ATM")



vol = pricer.calculate_vol(dt.date(2021,1,1), dt.date(2024,6,20), 90, ["atm","10c"])
vol.plot()

(vol["10C"] - vol["ATM"]).plot()

loader = interestRateFutureOptionLoader("0Q")
loader.load_option_price(dt.date(2020,12,1), dt.date(2021,6,13), check_missing_tickers=True)

vol[vol["ATM"].isna()]

data = pricer.calculate_vol(dt.date(2021,3,1), dt.date(2021,5,1), 90, ["ATM"]).set_index(["date","ticker_root","sign"])

data.loc[dt.datetime(2021,4,13)].sort_values(by="delta", kind="stable").sort_index(kind="stable")[:200]



loader = interestRateFutureOptionLoader("0Q")
periods = loader.get_periods(dt.date(2020,12,1), dt.date(2021,7,1))
for start in periods:
    df = pd.read_parquet(loader.cached_file_path(start[0].to_date()))
    start_shape = df.shape
    #print("Before", df.index.get_level_values(2).unique().sort_values())
    df = df.reset_index()

    # Normalize the 'strike' column to floats to identify duplicates
    df["strike_float"] = df["strike"].astype(float)

    # Drop duplicates based on ('date', 'ticker_root', 'strike_float')
    df = df.drop_duplicates(subset=["date", "ticker_root", "strike_float"])

    # Reconvert 'strike' to a formatted string
    df["strike"] = df["strike_float"].apply(
        lambda x: np.format_float_positional(x, trim="-")
    )

    # Drop the temporary 'strike_float' column
    df = df.drop(columns=["strike_float"])

    # Restore the index to its original format
    df = df.set_index(["date", "ticker_root", "strike"]).sort_index()
    #print("After", df.index.get_level_values(2).unique().sort_values())
    print(start_shape == df.shape)

    #df.to_parquet(loader.cached_file_path(start[0].to_date()))



df

pd.read_parquet(loader.cached_file_path(dt.date(2024,6,17))).reset_index().pivot(index=["date", "ticker_root"], columns="strike", values="price")

pricer = interestRateFutureOptionPricer("SFR")

data = pd.read_parquet(loader.cached_file_path(dt.date(2024,5,13)))



data = pricer.calculate_vol(dt.date(2024,5,1), dt.date(2024,11,1), 180, ["20p","atm","20c"])
data.plot()

data

data[data["ATM"].isna()]

imm_codes = loader.get_single_option_chain_imm_codes(ql.Date(14,10,2024))

pricer = interestRateFutureOptionPricer("SFR")

voool

data = pricer.calculate_vol(dt.date(2024,6,1), dt.date(2024,10,16), 90, ["atm"])
data

data.loc[dt.datetime(2024,6,18):dt.datetime(2024,6,27)]

data[(data["date"] == dt.datetime(2024,6,20)) & (data["strike"] >= (100-95)) & (data["strike"] <= (100-94.75))]

whole_option_px_data.loc[dt.date(2024,6,20)].loc[["SFRU4C", "SFRU4P"]].dropna(axis=1)

data.loc[[dt.date(2024,6,20),dt.date(2024,6,27),dt.date(2024,7,1)]]

vol = pricer.calculate_vol(dt.date(2024,6,1), dt.date(2024,10,16), 90, ["atm"])

vol[vol["ATM"].isna()]

data.groupby(["date", "ticker_root"], group_keys=False).apply(lambda group: group if group.shape[0] < 2 else None, include_groups=False)

whole_option_px_data = loader.load_option_price(dt.date(2024,6,1), dt.date(2024,10,16))

loader_sfi = interestRateFutureOptionLoader("SFI")

loader_sfi.load_option_price(dt.date(2021,6,1), dt.date(2022,12,1))

loader_0N = interestRateFutureOptionLoader("0N")

loader_0N.load_option_price(dt.date(2021,6,1), dt.date(2024,12,1))

loader_sfi.load_option_price(dt.date(2022,11,1), dt.date(2024,11,1))

loader_sfi.load_option_price(dt.date(2022,9,1), dt.date(2022,12,1))

loader_2q = interestRateFutureOptionLoader("2Q")

loader_3q = interestRateFutureOptionLoader("3Q")

loader_3q.load_option_price(dt.date(2021,6,1), dt.date(2021,12,12))

loader_3q.load_option_price(dt.date(2021,12,1), dt.date(2022,5,15))

loader_3q.load_option_price(dt.date(2022,5,1), dt.date(2022,6,12))

loader_3q.load_option_price(dt.date(2023,1,1), dt.date(2024,2,1))

loader_3q.load_option_price(dt.date(2022,6,1), dt.date(2022,10,16))

loader_3q.load_option_price(dt.date(2024,1,1), dt.date(2024,12,1))

loader_2q.load_option_price(dt.date(2021,3,1), dt.date(2021,7,1))

loader_2q.load_option_price(dt.date(2021,6,1), dt.date(2022,7,1))

loader_2q.load_option_price(dt.date(2022,6,1), dt.date(2023,3,1))

loader_2q.load_option_price(dt.date(2023,2,1), dt.date(2024,5,1))

loader_2q.load_option_price(dt.date(2023,4,1), dt.date(2024,7,1))

loader_2q.load_option_price(dt.date(2023,6,1), dt.date(2024,1,1))

imm_codes

whole_underlying_px_data

tickers = loader.get_single_option_chain_tickers(ql.Date(14,10,2024), whole_underlying_px_data.loc[dt.date(2024,10,14):dt.date(2024,11,15)])

tickers

import numpy as np
option_tickers = []
ticker_formatter = np.vectorize(lambda strike, option_type: f"{loader.ir_option_name}{imm_code}{option_type} {strike} Comdty")
for imm_code in imm_codes:
    underlying_ticker = loader.get_underlying_futures_tickers([imm_code], loader.ir_option_name, ql.Date(14,10,2024))[0]
    data = whole_underlying_px_data.loc[(slice(dt.date(2024,10,14),dt.date(2024,10,19)), underlying_ticker),"price"]
    vol = 300/100
    sqrt_T = np.sqrt((ql.IMM.date(imm_code, ql.Date(14,10,2024)) - ql.Date(14,10,2024))/365)
    limit = vol * sqrt_T * 1.75 # 10-delta is around 1.3, 5-delta is around 1.8
    atm_low, atm_high = np.floor(data.min() / config["min_tick_size"]) * config["min_tick_size"], np.ceil(data.max() / config["min_tick_size"]) * config["min_tick_size"]

    low, high = atm_low - config["min_tick_size_range"], atm_high + config["min_tick_size_range"]
    mid_strikes = np.arange(low, high, config["min_tick_size"])
    # For calls, download most of data with strike >= atm_low
    call_strikes = mid_strikes[mid_strikes >= atm_low] # Near OOM calls
    high_strikes = np.arange(np.ceil((atm_high+limit) / config["tick_size"]) * config["tick_size"], high, -config["tick_size"]) # Far OOM calls
    low_strikes = np.arange(np.ceil((atm_low - vol * sqrt_T * 0.1) / config["tick_size"]) * config["tick_size"], atm_low, config["tick_size"]) # A little ITM calls
    call_strikes = np.unique(np.unique(np.concat([low_strikes, call_strikes, high_strikes]))).astype(str)

    # For puts, download most of data with strike <= atm_high
    put_strikes = mid_strikes[mid_strikes <= atm_high] # Near OOM puts
    low_strikes = np.arange(np.floor((atm_low-limit) / config["tick_size"]) * config["tick_size"], low, config["tick_size"]) # Far OOM puts
    high_strikes = np.arange(np.floor((atm_high + vol * sqrt_T * 0.1) / config["tick_size"]) * config["tick_size"], atm_high, -config["tick_size"]) # A little ITM puts
    put_strikes = np.unique(np.concat([low_strikes, put_strikes, high_strikes])).astype(str)
    print(len(put_strikes))
    option_tickers.append(np.concatenate([ticker_formatter(call_strikes, "C"), ticker_formatter(put_strikes, "P")]))

option_tickers = np.concatenate(option_tickers).tolist()


whole_data = loader.load_option_price(dt.date(2022,11,14),dt.date(2023,1,15), check_missing_tickers=True)

whole_data[0].reset_index().pivot(index=["date","ticker_root"], columns="strike", values="price")

loader._get_single_option_chain_tickers(ql.Date(19,2,2024), strikes=['98.875'])

data = blp.bdh(tickers=['0QH4C 98.875 Comdty'], flds=['PX_SETTLE'], start_date='2024-02-19',end_date='2024-02-23')
data

data = blp.bdh(tickers=['SFRZ1C 98 Comdty'], flds=['PX_SETTLE'], start_date='2021-11-10',end_date='2021-11-20')
data

del whole_data

loader_0q = interestRateFutureOptionLoader("0q")
data = loader_0q.load_option_price(dt.date(2021,6,10), dt.date(2021,6,30), maturity=30)

pricer_0q = interestRateFutureOptionPricer("0q")
vol = pricer_0q.calculate_vol(dt.date(2021,6,10), dt.date(2025,2,28), 30, "25c")

pricer_0e = interestRateFutureOptionPricer("0E")

loader_0q.get_periods(dt.date(2018,1,1), dt.date(2018,12,31))

data

import math
from scipy.stats import norm

def black_scholes_futures_option_price(F, K, T, r, sigma, option_type='call'):
    """
    Calculate the price of a futures option using the Black model (Black-76).
    
    Parameters:
    F : float : Futures price
    K : float : Strike price
    T : float : Time to expiration (in years)
    r : float : Risk-free interest rate (as a decimal, e.g., 0.05 for 5%)
    sigma : float : Implied volatility (as a decimal, e.g., 0.15 for 15%)
    option_type : str : 'call' or 'put'
    
    Returns:
    float : Option price
    """
    
    # Calculate d1 and d2
    d1 = (math.log(F / K) + (0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    # Discount factor for the risk-free rate
    discount_factor = math.exp(-r*T)
    
    # Price calculation
    if option_type == 'call':
        option_price = discount_factor * (F * norm.cdf(d1) - K * norm.cdf(d2))
    elif option_type == 'put':
        option_price = discount_factor * (K * norm.cdf(-d2) - F * norm.cdf(-d1))
    else:
        raise ValueError("option_type must be 'call' or 'put'")
    
    return option_price


def normal_bachelier_futures_option_price(F, K, T, r, sigma, option_type='call', discount_factor=None):
    """
    Calculate the price of a futures option using the Normal (Bachelier) model.
    
    Parameters:
    F : float : Futures price
    K : float : Strike price
    T : float : Time to expiration (in years)
    r : float : Risk-free interest rate (as a decimal, e.g., 0.05 for 5%)
    sigma : float : Implied volatility (as a decimal, e.g., 0.15 for 15%)
    option_type : str : 'call' or 'put'
    
    Returns:
    float : Option price
    """
    
    # Calculate d1 for the Normal model
    d1 = (F - K) / (sigma * math.sqrt(T))
    
    # Discount factor for the risk-free rate
    if not discount_factor:
        discount_factor = math.exp(-r*T)
    
    # Price calculation
    if option_type == 'call':
        option_price = discount_factor * ((F - K) * norm.cdf(d1) + sigma * math.sqrt(T) * norm.pdf(d1))
    elif option_type == 'put':
        option_price = discount_factor * ((K - F) * norm.cdf(-d1) + sigma * math.sqrt(T) * norm.pdf(d1))
    else:
        raise ValueError("option_type must be 'call' or 'put'")
    
    return option_price

def normal_bachelier_futures_option_delta(F, K, T, sigma, option_type='call'):
    """
    Calculate the delta of a futures option using the Normal (Bachelier) model.
    
    Parameters:
    F : float : Futures price
    K : float : Strike price
    T : float : Time to expiration (in years)
    sigma : float : Implied volatility (as a decimal, e.g., 0.15 for 15%)
    option_type : str : 'call' or 'put'
    
    Returns:
    float : Option delta
    """
    
    # Calculate d1 for the Normal model
    d1 = (F - K) / (sigma * math.sqrt(T))
    
    # Calculate delta based on option type
    if option_type == 'call':
        delta = norm.cdf(d1)
    elif option_type == 'put':
        delta = norm.cdf(d1) - 1
    else:
        raise ValueError("option_type must be 'call' or 'put'")
    
    return delta

print(normal_bachelier_futures_option_price(0.17, 0.1875, 45/252, None, 0.256194, option_type='call', discount_factor=0.999910))
print(normal_bachelier_futures_option_delta(0.17, 0.1875, 45/252, 0.256194, 'call'))
print(normal_bachelier_futures_option_price(0.17, 0.1250, 45/252, None, 0.227675, option_type='call', discount_factor=0.999910))
print(normal_bachelier_futures_option_delta(0.17, 0.1250, 45/252, 0.227675, 'call'))

print(normal_bachelier_futures_option_price(0.11, 0.1250, 57/252, None, 0.195106, option_type='call', discount_factor=0.999964))
print(normal_bachelier_futures_option_delta(0.11, 0.1250, 57/252, 0.195106, 'call'))
print(normal_bachelier_futures_option_price(0.11, 0.0625, 57/252, None, 0.176755, option_type='call', discount_factor=0.999964))
print(normal_bachelier_futures_option_delta(0.11, 0.0625, 57/252, 0.176755, 'call'))


# Example inputs
F = 95.597       # Futures price
K = 95.75       # Strike price
T = 0.1424        # Time to expiration (in years), e.g., 3 months = 0.25 years
r = 0.047        # Risk-free rate (5% or 0.05 as a decimal)
sigma = 0.703/100    # Implied volatility (15% or 0.15 as a decimal)
option_type = 'call'  # Change to 'put' for put option pricing

# Calculate the option price
option_price = black_scholes_futures_option_price(F, K, T, r, sigma, option_type)
print(f"The price of the {option_type} option is: {option_price:.4f}")

# Example inputs
F = (100-96.278)       # Futures price
K = (100-96.25)       # Strike price
T = 37/360        # Time to expiration (in years), e.g., 3 months = 0.25 years
r = 0.0495        # Risk-free rate (5% or 0.05 as a decimal)
sigma = 130.481/100    # Implied volatility (130bps, or 1.30 in 100's, or 0.0130 in decimal)
option_type = 'put'  # Change to 'put' for put option pricing

# Calculate the option price
option_price = normal_bachelier_futures_option_price(F, K, T, r, sigma, option_type)
print(f"The price of the {option_type} option is: {option_price}")

ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, T, 0.18033494353201668, math.exp(-r * T))

ql.bachelierBlackFormula(ql.Option.Put, K, F, sigma * math.sqrt(T), math.exp(-r * T))

dt.date(2025,9,12)-dt.date(2024,11,25)

print(ql.UnitedStates(ql.UnitedStates.NYSE).isBusinessDay(ql.Date(14, 10, 2024)),
      ql.UnitedStates(ql.UnitedStates.NYSE).isBusinessDay(ql.Date(11, 11, 2024)))

print(ql.UnitedStates(ql.UnitedStates.FederalReserve).isBusinessDay(ql.Date(14, 10, 2024)),
      ql.UnitedStates(ql.UnitedStates.FederalReserve).isBusinessDay(ql.Date(11, 11, 2024)))

print(ql.UnitedStates(ql.UnitedStates.NYSE).businessDaysBetween(ql.Date(25,11,2024),ql.Date(9,12,2025)),
      ql.UnitedStates(ql.UnitedStates.FederalReserve).businessDaysBetween(ql.Date(25,11,2024),ql.Date(9,12,2025)))

# Use Act 360 day counter
F = 100-96.178       # Futures price
K = 100-96.125       # Strike price
T = 291/360        # Time to expiration (in years), e.g., 3 months = 0.25 years
r = 0.04363        # Risk-free rate (5% or 0.05 as a decimal)
sigma = 125.884/100    # Implied volatility (15% or 0.15 as a decimal)
option_type = 'put'  # Change to 'put' for put option pricing

# Calculate the option price
option_price = normal_bachelier_futures_option_price(F, K, T, r, sigma, option_type)
print(f"The price of the {option_type} option is: {option_price}")
delta = normal_bachelier_futures_option_delta(F, K, T, sigma, option_type)
print(f"Bachelier Delta for {option_type} option: {delta}")

normal_bachelier_futures_option_price(F/100, K/100, T, r, sigma/100, option_type)

125.884/math.sqrt(252)  # 7.930bps daily vol correct.

ql.bachelierBlackFormula(ql.Option.Put, K, F, sigma * math.sqrt(T), math.exp(-r * T))

ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, T, 0.46193188123215156, math.exp(-r * T))

K

ql.bachelierBlackFormulaAssetItmProbability(ql.Option.Put, K, 4.75, sigma * math.sqrt(T))

ql.bachelierBlackFormulaAssetItmProbability(ql.Option.Put, K, F, sigma * math.sqrt(T)) * math.exp(-r * T)

# Normal
ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, T, 0.46, math.exp(-r * T))

# Change day counter to Act 365
ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, 291/365, 0.46, math.exp(-r * T))

ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, 292/360, 0.46, math.exp(-r * T))

# Change day counter to Bus / 252
ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, 260/252, 0.46, math.exp(-r * T))

# Change discount to 1
ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, F, T, 0.46, 1)

ql.bachelierBlackFormulaImpliedVolChoi(ql.Option.Put, K, F, T, 0.46, math.exp(-r * T))

ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, 100-96.375, 100-96.325, 51/252, 0.2175, 1) # exactly the same!

ql.bachelierBlackFormulaImpliedVol(ql.Option.Put, K, 100-96.163, T, 0.4550, math.exp(-0.04 * T))





import blpapi
print(blpapi.__file__)

import QuantLib as ql
ql.IMM.nextCode(ql.Date(19,11,2024),False)

ql.Date.startOfMonth(ql.Date(19,11,2024))

def get_IMM_codes(serial_num: int, quarter_num: int, date: ql.Date):
    this_month_imm_date = ql.IMM.nextDate(ql.Date.startOfMonth(date), False)
    
    if date < this_month_imm_date - ql.Period("2D"):
        date = ql.IMM.nextDate(ql.Date.startOfMonth(date) - ql.Period("1M"), False)
    else:
        date = this_month_imm_date
    imm_codes = []
    imm_code = ql.IMM.code(date)
    for _ in range(quarter_num):
        imm_code = ql.IMM.nextCode(imm_code, True, date)
        imm_codes.append(imm_code)
    imm_code = ql.IMM.code(date)
    total_num = serial_num + quarter_num
    for _ in range(total_num):
        imm_code = ql.IMM.nextCode(imm_code, False, date)
        if imm_code not in imm_codes:
            imm_codes.append(imm_code)
        if len(imm_codes) == total_num:
            break

    return imm_codes

get_IMM_codes(4,5,ql.Date(13,10,2024))

get_IMM_codes(4,5,ql.Date(14,10,2024))

get_IMM_codes(4,5,ql.Date(17,11,2024))

get_IMM_codes(4,5,ql.Date(18,11,2024))

get_IMM_codes(4,5,ql.Date(30,11,2024))

ql.IMM.nextCode("z9",False)

loader = interestRateFutureOptionLoader("0q")

pd.read_parquet(loader.cached_file_path(dt.date(2024,6,17)))

loader = interestRateFutureOptionLoader("SFR")

option_px = loader.load_option_price(dt.date(2024,10,1), dt.date(2024,11,15))

pd.read_parquet(loader.cached_file_path(dt.date(2024,10,14)))

loader = interestRateFutureOptionLoader("ty", debug_mode=True)

px_data = loader.load_underlying_price(dt.date(2020,2,24), dt.date(2020,3,27))

poor_data = px_data.xs("TYZ20 Comdty", level=1, drop_level=True)[
                "price"
            ]
poor_data

pricer = interestRateFutureOptionPricer("us")

pricer.loader.load_option_price(dt.date(2020,3,6), dt.date(2020,4,20)).loc[dt.datetime(2020,3,9)]

pricer.loader.load_option_price(dt.date(2020,3,6), dt.date(2020,4,20), maturity=75)

