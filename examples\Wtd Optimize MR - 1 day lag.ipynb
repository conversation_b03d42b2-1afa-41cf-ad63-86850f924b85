{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3a4a7efa", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime\n", "from dateutil.relativedelta import relativedelta\n", "from xbbg import blp\n", "from scipy.stats import pearsonr\n", "import matplotlib.pyplot as plt\n", "import matplotlib.ticker as mticker\n", "import plotly.graph_objects as go\n", "import plotly.io as pio\n", "from statsmodels.tsa.stattools import adfuller"]}, {"cell_type": "code", "execution_count": null, "id": "9bae54b3", "metadata": {}, "outputs": [], "source": ["# citi_data = pd.read_excel(\"Citi Swap Spot & Forwards.xlsm\", sheet_name=\"Data\", header=1)\n", "# citi_data.to_parquet(\"citi_data.parquet\")"]}, {"cell_type": "code", "execution_count": null, "id": "50d10182", "metadata": {}, "outputs": [], "source": ["# parquet file reading is much faster than excel file reading\n", "citi_data = pd.read_parquet(\"citi_data.parquet\")\n", "\n", "# This drops the whole date row if there is any missing value in that row\n", "citi_data = citi_data.dropna().reset_index(drop=True)\n", "citi_data = citi_data.sort_values(by=\"Date\", ascending=True).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "443dfb4b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.1Y.1Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.2Y.1Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.3Y.1Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.3Y.2Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.5Y.2Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.5Y.5Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.7Y.3Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.7Y.5Y - CLOSE</th>\n", "      <th>RATES.SWAP_LIBOR.EUR.FWD.10Y.2Y - CLOSE</th>\n", "      <th>...</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.11Y.2Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.11Y.5Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.11Y.10Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.11Y.20Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.15Y.5Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.15Y.15Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.20Y.5Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.20Y.10Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.20Y.15Y - CLOSE</th>\n", "      <th>RATES.OIS.JPY_TONAR.FWD.30Y.10Y - CLOSE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2012-03-02</td>\n", "      <td>0.753139</td>\n", "      <td>1.12660</td>\n", "      <td>1.66801</td>\n", "      <td>2.05891</td>\n", "      <td>2.84750</td>\n", "      <td>3.04333</td>\n", "      <td>3.18391</td>\n", "      <td>3.25507</td>\n", "      <td>3.37096</td>\n", "      <td>...</td>\n", "      <td>1.94175</td>\n", "      <td>2.05725</td>\n", "      <td>2.06997</td>\n", "      <td>1.94579</td>\n", "      <td>2.12055</td>\n", "      <td>1.92829</td>\n", "      <td>1.88480</td>\n", "      <td>1.81754</td>\n", "      <td>1.83026</td>\n", "      <td>1.92457</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2012-03-07</td>\n", "      <td>0.778688</td>\n", "      <td>1.14258</td>\n", "      <td>1.70298</td>\n", "      <td>2.09718</td>\n", "      <td>2.86066</td>\n", "      <td>3.06220</td>\n", "      <td>3.20800</td>\n", "      <td>3.28342</td>\n", "      <td>3.40584</td>\n", "      <td>...</td>\n", "      <td>1.35931</td>\n", "      <td>1.96679</td>\n", "      <td>2.16383</td>\n", "      <td>1.79672</td>\n", "      <td>2.11788</td>\n", "      <td>1.92291</td>\n", "      <td>1.87774</td>\n", "      <td>1.81060</td>\n", "      <td>1.82888</td>\n", "      <td>1.93016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2012-03-08</td>\n", "      <td>0.799925</td>\n", "      <td>1.15674</td>\n", "      <td>1.70476</td>\n", "      <td>2.10037</td>\n", "      <td>2.88459</td>\n", "      <td>3.08315</td>\n", "      <td>3.22571</td>\n", "      <td>3.30054</td>\n", "      <td>3.42275</td>\n", "      <td>...</td>\n", "      <td>1.95149</td>\n", "      <td>2.06431</td>\n", "      <td>2.08090</td>\n", "      <td>1.95328</td>\n", "      <td>2.13434</td>\n", "      <td>1.93630</td>\n", "      <td>1.89385</td>\n", "      <td>1.82208</td>\n", "      <td>1.84156</td>\n", "      <td>1.94210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2012-03-09</td>\n", "      <td>0.790792</td>\n", "      <td>1.14272</td>\n", "      <td>1.67471</td>\n", "      <td>2.07259</td>\n", "      <td>2.86847</td>\n", "      <td>3.07560</td>\n", "      <td>3.22443</td>\n", "      <td>3.29609</td>\n", "      <td>3.41294</td>\n", "      <td>...</td>\n", "      <td>1.97907</td>\n", "      <td>2.07769</td>\n", "      <td>2.09595</td>\n", "      <td>1.96949</td>\n", "      <td>2.14758</td>\n", "      <td>1.95240</td>\n", "      <td>1.91023</td>\n", "      <td>1.83975</td>\n", "      <td>1.86127</td>\n", "      <td>1.97431</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2012-03-12</td>\n", "      <td>0.790597</td>\n", "      <td>1.13236</td>\n", "      <td>1.64491</td>\n", "      <td>2.04483</td>\n", "      <td>2.83030</td>\n", "      <td>3.04262</td>\n", "      <td>3.19519</td>\n", "      <td>3.26730</td>\n", "      <td>3.38458</td>\n", "      <td>...</td>\n", "      <td>1.97732</td>\n", "      <td>2.08618</td>\n", "      <td>2.10974</td>\n", "      <td>1.98706</td>\n", "      <td>2.16826</td>\n", "      <td>1.97358</td>\n", "      <td>1.92786</td>\n", "      <td>1.86106</td>\n", "      <td>1.88117</td>\n", "      <td>1.99283</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 471 columns</p>\n", "</div>"], "text/plain": ["        Date  RATES.SWAP_LIBOR.EUR.FWD.1Y.1Y - CLOSE  \\\n", "0 2012-03-02                                0.753139   \n", "1 2012-03-07                                0.778688   \n", "2 2012-03-08                                0.799925   \n", "3 2012-03-09                                0.790792   \n", "4 2012-03-12                                0.790597   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.2Y.1Y - CLOSE  \\\n", "0                                 1.12660   \n", "1                                 1.14258   \n", "2                                 1.15674   \n", "3                                 1.14272   \n", "4                                 1.13236   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.3Y.1Y - CLOSE  \\\n", "0                                 1.66801   \n", "1                                 1.70298   \n", "2                                 1.70476   \n", "3                                 1.67471   \n", "4                                 1.64491   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.3Y.2Y - CLOSE  \\\n", "0                                 2.05891   \n", "1                                 2.09718   \n", "2                                 2.10037   \n", "3                                 2.07259   \n", "4                                 2.04483   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.5Y.2Y - CLOSE  \\\n", "0                                 2.84750   \n", "1                                 2.86066   \n", "2                                 2.88459   \n", "3                                 2.86847   \n", "4                                 2.83030   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.5Y.5Y - CLOSE  \\\n", "0                                 3.04333   \n", "1                                 3.06220   \n", "2                                 3.08315   \n", "3                                 3.07560   \n", "4                                 3.04262   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.7Y.3Y - CLOSE  \\\n", "0                                 3.18391   \n", "1                                 3.20800   \n", "2                                 3.22571   \n", "3                                 3.22443   \n", "4                                 3.19519   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.7Y.5Y - CLOSE  \\\n", "0                                 3.25507   \n", "1                                 3.28342   \n", "2                                 3.30054   \n", "3                                 3.29609   \n", "4                                 3.26730   \n", "\n", "   RATES.SWAP_LIBOR.EUR.FWD.10Y.2Y - CLOSE  ...  \\\n", "0                                  3.37096  ...   \n", "1                                  3.40584  ...   \n", "2                                  3.42275  ...   \n", "3                                  3.41294  ...   \n", "4                                  3.38458  ...   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.11Y.2Y - CLOSE  \\\n", "0                                 1.94175   \n", "1                                 1.35931   \n", "2                                 1.95149   \n", "3                                 1.97907   \n", "4                                 1.97732   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.11Y.5Y - CLOSE  \\\n", "0                                 2.05725   \n", "1                                 1.96679   \n", "2                                 2.06431   \n", "3                                 2.07769   \n", "4                                 2.08618   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.11Y.10Y - CLOSE  \\\n", "0                                  2.06997   \n", "1                                  2.16383   \n", "2                                  2.08090   \n", "3                                  2.09595   \n", "4                                  2.10974   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.11Y.20Y - CLOSE  \\\n", "0                                  1.94579   \n", "1                                  1.79672   \n", "2                                  1.95328   \n", "3                                  1.96949   \n", "4                                  1.98706   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.15Y.5Y - CLOSE  \\\n", "0                                 2.12055   \n", "1                                 2.11788   \n", "2                                 2.13434   \n", "3                                 2.14758   \n", "4                                 2.16826   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.15Y.15Y - CLOSE  \\\n", "0                                  1.92829   \n", "1                                  1.92291   \n", "2                                  1.93630   \n", "3                                  1.95240   \n", "4                                  1.97358   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.20Y.5Y - CLOSE  \\\n", "0                                 1.88480   \n", "1                                 1.87774   \n", "2                                 1.89385   \n", "3                                 1.91023   \n", "4                                 1.92786   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.20Y.10Y - CLOSE  \\\n", "0                                  1.81754   \n", "1                                  1.81060   \n", "2                                  1.82208   \n", "3                                  1.83975   \n", "4                                  1.86106   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.20Y.15Y - CLOSE  \\\n", "0                                  1.83026   \n", "1                                  1.82888   \n", "2                                  1.84156   \n", "3                                  1.86127   \n", "4                                  1.88117   \n", "\n", "   RATES.OIS.JPY_TONAR.FWD.30Y.10Y - CLOSE  \n", "0                                  1.92457  \n", "1                                  1.93016  \n", "2                                  1.94210  \n", "3                                  1.97431  \n", "4                                  1.99283  \n", "\n", "[5 rows x 471 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["citi_data.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "fe147330", "metadata": {}, "outputs": [], "source": ["curr = [\"USD Libor\",\"USD SOFR\",\"EUR\",\"GBP\",\"SEK\",\"AUD\",\"JPY Libor\",\"JPY TONAR\"] #,\"CAD Libor\",\"CAD CORRA\",\n", "liquid_points = [\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 25, 30],  # USD Libor\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 25, 30],  # USD Sofr\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 25, 30],  # EUR\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 25, 30],  # GBP\n", "    [2, 3, 5, 7, 10, 12, 15],                      # SEK\n", "    [2, 3, 5, 7, 10],          # AUD\n", "#    [2, 3, 5, 7, 10, 12, 15, 20, 30],          # CA<PERSON>bor\n", "#    [2, 3, 5, 7, 10, 12, 15, 20, 30],          # CAD CORRA\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 30],          # <PERSON><PERSON>\n", "    [2, 3, 5, 7, 10, 12, 15, 20, 30]          # JPY TONAR\n", "]\n", "\n", "spot_slippage = [0.1, 0.1,  ### USD\n", "                 0.15,0.15, ### EUR, GBP\n", "                 0.25,0.25,#0.25,0.25, ### SEK, AUD, CAD\n", "                 0.15,0.15] ### JPY\n", "\n", "forward_points = [\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "#    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"],\n", "#    [\"1y1y\", \"2y1y\",\"3y1y\",\"3y2y\",\"5y2y\",\"5y5y\",\"7y3y\",\"7y5y\",\"10y2y\",\"10y5y\",\"10y10y\",\"10y20y\",\"15y5y\",\"15y15y\",\"20y10y\"]\n", "]  \n", "\n", "fwd_forwards = [\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"30y10y\"],\n", "#    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"25y10y\"],\n", "#    [\"2y1y\", \"3y1y\",\"4y1y\",\"4y2y\",\"6y2y\",\"6y5y\",\"8y3y\",\"8y5y\",\"11y2y\",\"11y5y\",\"11y10y\",\"11y20y\",\"20y5y\",\"20y15y\",\"30y10y\"]\n", "] \n", "\n", "forward_slippage = [0.25, 0.25,  ### USD\n", "                 0.35,0.35, ### EUR, GBP\n", "                 0.5,0.5,#0.5,0.5, ### SEK, AUD, CAD\n", "                 0.35,0.35] ### JPY\n", "\n", "num_currencies = len(curr)\n", "num_liquid_points = len(liquid_points[0])\n", "num_forward_points = len(forward_points[0])\n", "\n", "d_dates = pd.to_datetime(citi_data[\"Date\"]).dt.date\n", "first_date = d_dates[0]\n", "last_date = d_dates.iloc[-1]\n", "today = datetime.date.today()\n", "\n", "curr_start_date = [first_date,datetime.date(2022,8,26),first_date,first_date,first_date,first_date,first_date,datetime.date(2022,8,26),first_date,datetime.date(2022,8,26)]\n", "curr_end_date = [datetime.date(2022,8,26),last_date,last_date,last_date,last_date,last_date,datetime.date(2022,8,26),last_date,datetime.date(2022,8,26),last_date]\n", "\n", "spot_data = [[[] for _ in range(num_liquid_points)] for _ in range(num_currencies)]\n", "spot_carry = [[[] for _ in range(num_liquid_points)] for _ in range(num_currencies)]\n", "\n", "forward_data = [[[] for _ in range(num_forward_points)] for _ in range(num_currencies)]\n", "forward_carry = [[[] for _ in range(num_forward_points)] for _ in range(num_currencies)]\n", "\n", "\n", "for i in range(0,num_currencies):\n", "    for j in range(0,len(liquid_points[i])):\n", "        if(curr[i] == \"USD Libor\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.USD.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.USD.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"USD SOFR\"):\n", "            spot_data[i][j] = citi_data[\"RATES.OIS.USD_SOFR.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.OIS.USD_SOFR.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"EUR\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.EUR.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.EUR.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"GBP\"):\n", "            spot_data[i][j] = citi_data[\"RATES.OIS.GBP_SONIA.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.OIS.GBP_SONIA.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"SEK\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.SEK.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.SEK.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"AUD\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.AUD.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.AUD.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"JPY TONAR\"):\n", "            spot_data[i][j] = citi_data[\"RATES.OIS.JPY_TONAR.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.OIS.JPY_TONAR.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"JPY Libor\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.JPY.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.JPY.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"CAD CORRA\"):\n", "            spot_data[i][j] = citi_data[\"RATES.OIS.CAD_CORRA.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.OIS.CAD_CORRA.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "        elif(curr[i] == \"CAD Libor\"):\n", "            spot_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.CAD.PAR.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100\n", "            spot_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.CAD.FWD.1M.\"+str(liquid_points[i][j])+\"Y - CLOSE\"].values *100 - spot_data[i][j])\n", "\n", "\n", "for i in range(0,num_currencies):\n", "    for j in range(0,len(forward_points[i])):\n", "        parts = forward_points[i][j].split('y')\n", "        fwd_parts = fwd_forwards[i][j].split('y')\n", "        if(curr[i] == \"USD Libor\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.USD.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values *100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.USD.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "        \n", "        if(curr[i] == \"USD SOFR\"):\n", "            forward_data[i][j] = citi_data[\"RATES.OIS.USD_SOFR.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values *100\n", "            forward_carry[i][j] = (citi_data[\"RATES.OIS.USD_SOFR.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "            \n", "        if(curr[i] == \"EUR\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.EUR.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.EUR.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "        \n", "        if(curr[i] == \"GBP\"):\n", "            forward_data[i][j] = citi_data[\"RATES.OIS.GBP_SONIA.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values *100\n", "            forward_carry[i][j] = (citi_data[\"RATES.OIS.GBP_SONIA.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "        \n", "        if(curr[i] == \"SEK\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.SEK.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.SEK.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "            \n", "        if(curr[i] == \"AUD\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.AUD.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.AUD.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "        \n", "        if(curr[i] == \"CAD Libor\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.CAD.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.CAD.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "    \n", "        if(curr[i] == \"CAD CORRA\"):\n", "            forward_data[i][j] = citi_data[\"RATES.OIS.CAD_CORRA.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.OIS.CAD_CORRA.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "\n", "        if(curr[i] == \"JPY Libor\"):\n", "            forward_data[i][j] = citi_data[\"RATES.SWAP_LIBOR.JPY.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.SWAP_LIBOR.JPY.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))\n", "\n", "        if(curr[i] == \"JPY TONAR\"):\n", "            forward_data[i][j] = citi_data[\"RATES.OIS.JPY_TONAR.FWD.\"+parts[0]+\"Y.\"+parts[1]+\"Y - CLOSE\"].values*100\n", "            forward_carry[i][j] = (citi_data[\"RATES.OIS.JPY_TONAR.FWD.\"+fwd_parts[0]+\"Y.\"+fwd_parts[1]+\"Y - CLOSE\"].values*100 - forward_data[i][j])/(12*(int(fwd_parts[0])-int(parts[0])))"]}, {"cell_type": "code", "execution_count": 7, "id": "78193104", "metadata": {}, "outputs": [], "source": ["#### 1 -> Check ADF and zscore and do mean reversion + halflife + target/slippage ratio\n", "num_years = 1\n", "num_months = 0\n", "vol_ratio_threshold = 2\n", "mr_threshold = 0.25\n", "correl_threshold = 0.8\n", "\n", "entry_threshold = 2\n", "stoploss_threshold = 4\n", "halflife_entry_threshold = 5\n", "halflife_exit_threshold = 10\n", "target_slippage_ratio = 10    #### max slippage should be < 10% of expected P&L\n", "\n", "trade_count = 0\n", "profitable_trades = 0\n", "loss_trades = 0\n", "total_profitable_pnl = 0\n", "total_loss_pnl = 0\n", "total_profitable_holding_days = 0\n", "total_loss_holding_days = 0\n", "\n", "\n", "\n", "def find_closest_next_date(target_date):\n", "    filtered_dates = d_dates[d_dates >= target_date]\n", "    return filtered_dates.index[0] if not filtered_dates.empty else len(d_dates) - 1"]}, {"cell_type": "code", "execution_count": 8, "id": "cbe4a5f2", "metadata": {}, "outputs": [], "source": ["curve_spot_shape = (num_currencies, num_liquid_points, num_liquid_points)\n", "fly_spot_shape = (num_currencies, num_liquid_points, num_liquid_points, num_liquid_points)\n", "curve_forward_shape = (num_currencies, num_forward_points, num_forward_points)\n", "fly_forward_shape = (num_currencies, num_forward_points, num_forward_points, num_forward_points)\n", "\n", "\n", "position_size_spot_curve = np.zeros(curve_spot_shape)\n", "position_pnl_spot_curve = np.zeros(curve_spot_shape)\n", "position_number_spot_curve = np.zeros(curve_spot_shape, dtype=int)\n", "lag_status_spot_curve = np.zeros(curve_spot_shape, dtype=int)\n", "position_ratio_spot_curve = np.zeros(curve_spot_shape)\n", "\n", "position_size_forward_curve = np.zeros(curve_forward_shape)\n", "position_pnl_forward_curve = np.zeros(curve_forward_shape)\n", "position_number_forward_curve = np.zeros(curve_forward_shape, dtype=int)\n", "lag_status_forward_curve = np.zeros(curve_forward_shape, dtype=int)\n", "position_ratio_forward_curve = np.zeros(curve_forward_shape)\n", "\n", "position_size_spot_fly = np.zeros(fly_spot_shape)\n", "position_pnl_spot_fly = np.zeros(fly_spot_shape)\n", "position_number_spot_fly = np.zeros(fly_spot_shape, dtype=int)\n", "lag_status_spot_fly = np.zeros(fly_spot_shape, dtype=int)\n", "position_ratio_spot_fly = np.zeros(fly_spot_shape)\n", "\n", "position_size_forward_fly = np.zeros(fly_forward_shape)\n", "position_pnl_forward_fly = np.zeros(fly_forward_shape)\n", "position_number_forward_fly = np.zeros(fly_forward_shape, dtype=int)\n", "lag_status_forward_fly = np.zeros(fly_forward_shape, dtype=int)\n", "position_ratio_forward_fly = np.zeros(fly_forward_shape)\n", "\n", "\n", "random_date = datetime.date(1900, 1, 1)\n", "swap_start_date_spot_curve = np.full(curve_spot_shape, random_date, dtype=object)\n", "swap_start_date_spot_fly = np.full(fly_spot_shape, random_date, dtype=object)\n", "swap_start_date_forward_curve = np.full(curve_forward_shape, random_date, dtype=object)\n", "swap_start_date_forward_fly = np.full(fly_forward_shape, random_date, dtype=object)\n", "\n", "position_enter_date_spot_curve = np.full(curve_spot_shape, random_date, dtype=object)\n", "position_enter_date_spot_fly = np.full(fly_spot_shape, random_date, dtype=object)\n", "position_enter_date_forward_curve = np.full(curve_forward_shape, random_date, dtype=object)\n", "position_enter_date_forward_fly = np.full(fly_forward_shape, random_date, dtype=object)\n", "\n", "\n", "start_date = first_date + relativedelta(years=num_years, months=num_months)\n", "start_index = find_closest_next_date(start_date)\n", "\n", "daily_PL = []\n", "currency_PL = np.zeros((len(curr), len(d_dates)))\n", "currency_slippage = np.zeros(len(curr))\n", "trade_count = -1\n", "\n", "model_data, model_dates, trade_enter_index = [], [], []\n", "trade_data, trade_dates, trade_text, trade_size = [], [], [], []\n", "post_trade_data, post_trade_dates, trade_pnl = [], [], []\n", "trade_text2, trade_text3 = [], []\n", "trade_ratio,trade_currency, trade_type, trade_max_pnl, trade_min_pnl, trade_holding_days = [],[], [], [], [], []\n", "\n", "max_pnl, drawdown = 0, 0"]}, {"cell_type": "code", "execution_count": 58, "id": "dd00d8ed", "metadata": {}, "outputs": [], "source": ["# Ornstein <PERSON> process - returns half life, theta (speed of mean reversion), sigma (volatility), mu (mean)\n", "def OU_process(ou_data):\n", "    results = {}\n", "    N = len(ou_data) - 1\n", "\n", "    Sx = np.sum(ou_data[:-1])\n", "    Sy = np.sum(ou_data[1:])\n", "    Sxx = np.sum(ou_data[:-1] ** 2)\n", "    Sxy = np.sum(ou_data[:-1] * ou_data[1:])\n", "    Syy = np.sum(ou_data[1:] ** 2)\n", "\n", "    mu = (Sy * Sxx - Sx * Sxy) / (N * (Sxx - Sxy) - (Sx**2 - Sx * Sy))\n", "    theta = -np.log((Sxy - mu * Sx - mu * Sy + N * mu**2) / (Sxx - 2 * mu * Sx + N * mu**2))\n", "    sigma = np.sqrt(\n", "        2\n", "        * theta\n", "        * (Syy - 2 * np.exp(-theta) * Sxy + np.exp(-2 * theta) * Sxx - 2 * mu * (1 - np.exp(-theta)) * (Sy - np.exp(-theta) * Sx) + N * mu**2 * (1 - np.exp(-theta)) ** 2)\n", "        / (N * (1 - np.exp(-2 * theta)))\n", "    )\n", "\n", "    halflife = np.log(2) / theta if theta > 0 else 1e7\n", "    stdev = sigma / np.sqrt(2 * theta) if theta > 0 else 0\n", "\n", "    results[\"mu\"] = mu\n", "    results[\"theta\"] = max(theta, 1e-18)\n", "    results[\"sigma\"] = sigma\n", "    results[\"halflife\"] = halflife\n", "    results[\"stdev\"] = stdev\n", "\n", "    return results\n", "\n", "\n", "def check_correlation(data,start_index,end_index):\n", "    \n", "    for c in range(0,len(curr)):\n", "        for x in range(0,len(liquid_points[c])):\n", "            for y in range(x+1,len(liquid_points[c])):\n", "                if(position_size_spot_curve[c][x][y] != 0):\n", "                    data_test = spot_data[c][y][start_index:end_index] - spot_data[c][x][start_index:end_index]\n", "                    correl, p_value = pearsonr(data[-93:], data_test[-93:])\n", "    \n", "                    if(correl>correl_threshold):\n", "                        return 1\n", "                for z in range(y+1,len(liquid_points[c])):\n", "                    if(position_size_spot_fly[c][x][y][z] != 0):\n", "                        data_test = spot_data[c][y][start_index:end_index]*2 - spot_data[c][x][start_index:end_index] - spot_data[c][z][start_index:end_index]\n", "                        correl, p_value = pearsonr(data[-93:], data_test[-93:])\n", "    \n", "                        if(correl>correl_threshold):\n", "                            return 1\n", "\n", "        for x in range(0,len(forward_points[c])):\n", "            for y in range(x+1,len(forward_points[c])):\n", "                if(position_size_forward_curve[c][x][y] != 0):\n", "                    data_test = forward_data[c][y][start_index:end_index] - forward_data[c][x][start_index:end_index]\n", "                    correl, p_value = pearsonr(data[-93:], data_test[-93:])\n", "    \n", "                    if(correl>correl_threshold):\n", "                        return 1\n", "                for z in range(y+1,len(forward_points[c])):\n", "                    if(position_size_forward_fly[c][x][y][z] != 0):\n", "                        data_test = forward_data[c][y][start_index:end_index]*2 - forward_data[c][x][start_index:end_index] - forward_data[c][z][start_index:end_index]\n", "                        correl, p_value = pearsonr(data[-93:], data_test[-93:])\n", "    \n", "                        if(correl>correl_threshold):\n", "                            return 1\n", "\n", "    return 0                \n", "    "]}, {"cell_type": "code", "execution_count": 9, "id": "6305dd63", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([2.00000000e-01, 1.11022302e-16])"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# An example of how to use np.polyfit\n", "np.polyfit([1,2,3,4],[0.2,0.4,0.6,0.8],1)"]}, {"cell_type": "code", "execution_count": 35, "id": "7840a6da", "metadata": {}, "outputs": [], "source": ["data1, data2 = spot_data[1][0][-252:], spot_data[1][1][-252:]"]}, {"cell_type": "code", "execution_count": 41, "id": "e92c1622", "metadata": {}, "outputs": [], "source": ["position_ratio, res = np.polyfit(data1, data2, 1)"]}, {"cell_type": "code", "execution_count": 42, "id": "3c10eec4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame([data1, data2]).T.plot()"]}, {"cell_type": "code", "execution_count": 46, "id": "4e1cab6f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.DataFrame([data1 * position_ratio + res, data2]).T.plot()"]}, {"cell_type": "code", "execution_count": 51, "id": "94731579", "metadata": {}, "outputs": [], "source": ["data = data1*position_ratio - data2"]}, {"cell_type": "code", "execution_count": 52, "id": "26f7f73e", "metadata": {}, "outputs": [{"data": {"text/plain": ["9.237055564881302e-14"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["res + np.mean(data)"]}, {"cell_type": "code", "execution_count": 53, "id": "df167c0e", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-23.85875605, -26.81093555, -25.10041954, -25.66851905,\n", "       -27.51669866, -27.96993866, -30.61618907, -29.5264956 ,\n", "       -28.5958662 , -29.57508268, -29.84526171, -29.02301889,\n", "       -29.79607297, -29.62805943, -28.55860462, -28.4772597 ,\n", "       -28.21692521, -28.06643726, -26.52374226, -26.66877721,\n", "       -25.26440829, -24.57167588, -24.18154763, -23.96814017,\n", "       -24.17697079, -25.53660042, -25.8816129 , -29.25988852,\n", "       -30.11275358, -30.79087905, -29.75392138, -29.51609769,\n", "       -28.11193468, -28.86306891, -28.31281288, -27.33795127,\n", "       -26.8588739 , -31.25211165, -30.90222216, -30.31936557,\n", "       -31.45021427, -33.21332998, -32.39329656, -36.76373844,\n", "       -36.98720325, -35.04851322, -37.79615132, -39.11075097,\n", "       -37.02919986, -38.6090223 , -38.13685426, -38.17580888,\n", "       -37.84779205, -38.98337291, -39.90748423, -38.95253181,\n", "       -38.94083972, -36.81347773, -36.40988473, -33.87741347,\n", "       -34.81583973, -34.16201476, -35.1626782 , -34.26592892,\n", "       -33.42803852, -30.96339641, -31.3937572 , -32.64621818,\n", "       -32.4245565 , -32.19528986, -32.53295426, -33.51060957,\n", "       -33.4637745 , -34.77957571, -36.78225174, -35.15056826,\n", "       -33.94317614, -31.46131554, -29.99592658, -29.57380302,\n", "       -29.13287504, -31.84147731, -33.10063473, -31.68014311,\n", "       -28.98513011, -27.56298594, -27.1502905 , -28.60208788,\n", "       -27.20108985, -27.20377123, -28.16709076, -28.17572675,\n", "       -27.85235587, -28.0720338 , -29.82599176, -29.25980809,\n", "       -30.1252948 , -33.54988814, -32.47854891, -30.17929609,\n", "       -30.25668577, -28.57866527, -28.44419764, -29.12536477,\n", "       -28.85906556, -27.00899848, -27.0022821 , -27.16696195,\n", "       -26.32468279, -27.40861421, -28.92093527, -28.80764854,\n", "       -28.66333561, -29.18621172, -29.01954253, -27.7215476 ,\n", "       -27.36083399, -26.89271574, -26.30146225, -25.05999588,\n", "       -24.21346899, -24.68581544, -25.57555864, -27.80275924,\n", "       -28.05503836, -26.84525087, -25.39674715, -24.43472835,\n", "       -26.10230839, -25.81022643, -25.32821142, -24.71458743,\n", "       -24.51107801, -25.70248978, -24.70802732, -25.22514472,\n", "       -25.20847085, -25.67441418, -26.68423792, -26.67462822,\n", "       -25.42109026, -25.22854116, -24.80638876, -25.05712289,\n", "       -24.64164838, -23.99731388, -23.28734793, -24.51735639,\n", "       -25.37920562, -26.33031733, -28.02659712, -29.26202061,\n", "       -29.71740938, -30.3026952 , -31.16678061, -31.06180681,\n", "       -30.58754868, -30.48420801, -29.12596801, -29.7860844 ,\n", "       -31.07050056, -32.806529  , -33.25165251, -33.8263358 ,\n", "       -34.07706906, -34.69995736, -34.27295463, -33.72764821,\n", "       -33.52107328, -34.58948854, -34.24869359, -36.41491757,\n", "       -37.37484775, -38.76798288, -38.28962113, -38.71245458,\n", "       -39.81597408, -39.83121074, -40.42662352, -41.01798532,\n", "       -41.72523661, -40.66076209, -43.17019796, -41.6403442 ,\n", "       -40.87398545, -40.86366683, -42.87014181, -44.05749435,\n", "       -43.4242815 , -42.99080761, -42.67990559, -41.83103738,\n", "       -42.1007475 , -42.74167108, -41.95935557, -39.20675099,\n", "       -40.20400324, -38.57712633, -38.60766186, -37.82227443,\n", "       -37.62523182, -39.32424846, -38.96666325, -38.97044663,\n", "       -38.77216565, -39.43049957, -39.79324225, -41.45858684,\n", "       -42.5983267 , -44.00699736, -44.47979264, -44.01530814,\n", "       -46.28998965, -48.70458405, -47.24029331, -49.02079216,\n", "       -49.02451801, -49.81425386, -47.90270528, -50.00973057,\n", "       -51.21405072, -51.34250802, -50.99704422, -54.12002522,\n", "       -54.66076265, -51.08506703, -49.87259414, -50.1234448 ,\n", "       -50.14383917, -49.48794432, -50.14858563, -50.49223229,\n", "       -50.50037441, -48.50565153, -48.83226716, -48.71054255,\n", "       -47.77926778, -48.99019949, -48.05999726, -47.09930249,\n", "       -44.84275889, -46.05588781, -46.99925233, -46.87191525,\n", "       -50.75405372, -48.31269107, -47.0077234 , -47.02012465,\n", "       -48.38117529, -48.04950739, -47.27258348, -45.91141679])"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 59, "id": "dcfd174e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'mu': -41.903462879086554,\n", " 'theta': 0.012031721102278336,\n", " 'sigma': 1.2817224818416226,\n", " 'halflife': 57.60997738126513,\n", " 'stdev': 8.26256950971771}"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["OU_process(data)"]}, {"cell_type": "code", "execution_count": null, "id": "35034c84", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "80511408", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "412d085f-9432-41c3-bd9e-58d0b920a9f1", "metadata": {}, "outputs": [], "source": ["def check_mean_reversion(i, data1,data2, carry1,carry2, position_ratio, position_size, swap_start_date, curve_name, current_PL, trade_num, trade_enter_date,slippage,lag_status):\n", "    global trade_count, profitable_trades, loss_trades\n", "    global total_profitable_pnl, total_loss_pnl, total_profitable_holding_days, total_loss_holding_days\n", "    \n", "    # Precompute values to avoid redundant calculations\n", "    model_start_index = find_closest_next_date(d_dates[i] - relativedelta(years=num_years, months=num_months))\n", "    \n", "    \n", "    new_position, exit_reason, todays_PL = position_size, \"\", 0\n", "    \n", "    if position_size != 0:\n", "\n", "        data = data2*position_ratio - data1\n", "        carry = carry2*position_ratio - carry1\n", "        results = OU_process(np.asarray(data))\n", "        last, mu, theta, sigma = data[-1], results['mu'], results['theta'], results['sigma']\n", "        halflife, stdev = results['halflife'], results['stdev']\n", "        sigma_x = np.sqrt(sigma**2 / (2 * theta))\n", "        carry_vol_ratio = np.abs(carry[-1]) * 12 / sigma_x\n", "        pvalue = adfuller(data)[1]\n", "    \n", "        # Reduce redundant date calculations\n", "        spot_date, fwd_date = d_dates[i], d_dates[i] + relativedelta(months=1)\n", "        fwd_date = d_dates[min(find_closest_next_date(fwd_date), len(d_dates) - 1)]\n", "        \n", "        if(fwd_date>spot_date):\n", "            interp_factor = (swap_start_date - spot_date) / (fwd_date - spot_date)\n", "        else:\n", "            interp_factor = 0\n", "        interp_swap_rate = last + interp_factor * carry[-1]\n", "        if(d_dates[min(find_closest_next_date(d_dates[i - 1] + relativedelta(months=1)), len(d_dates) - 1)] > d_dates[i - 1]):\n", "            yest_interp_factor = (swap_start_date - d_dates[i - 1]) / (d_dates[min(find_closest_next_date(d_dates[i - 1] + relativedelta(months=1)), len(d_dates) - 1)] - d_dates[i - 1])\n", "        else:\n", "            yest_interp_factor =0\n", "        \n", "        \n", "        yest_interp_swap_rate = data[-2] + yest_interp_factor * carry[-2]\n", "        \n", "        todays_PL = position_size * (yest_interp_swap_rate - interp_swap_rate)\n", "        \n", "        # Exit conditions optimization\n", "        exit_condition = (position_size > 0 and (pvalue > 0.1 or halflife > halflife_exit_threshold or last < mu or last > mu + stoploss_threshold * sigma_x)) or \\\n", "                         (position_size < 0 and (pvalue > 0.1 or halflife > halflife_exit_threshold or last > mu or last < mu - stoploss_threshold * sigma_x))\n", "\n", "        if exit_condition:\n", "            exit_reason = \" \".join(filter(None, [\n", "                \"Stoploss\" if last > mu + stoploss_threshold * sigma_x or last < mu - stoploss_threshold * sigma_x else \"\",\n", "                \"Take Profit\" if (last < mu and position_size > 0) or (last > mu and position_size < 0) else \"\",\n", "                \"Non Stationary\" if pvalue > 0.1 else \"\",\n", "                \"High Halflife\" if halflife > halflife_exit_threshold else \"\"\n", "            ]))\n", "            if exit_reason == \"Take Profit\":\n", "                if(lag_status == -1): #### We had take profit signal yesterday\n", "                    todays_PL -= np.abs(position_size) * slippage\n", "                    new_position = 0\n", "                    lag_status = 0\n", "                    print(trade_num, curve_name,\"1:\"+str(round(position_ratio,2)), \"Exit\", \"recd\" if position_size > 0 else \"paid\", round(interp_swap_rate, 2), \"Pnl\", round(current_PL + todays_PL), \"Size\", round(position_size), \"Half Life\", round(halflife), exit_reason, \"Holding Days\",i - trade_enter_date)\n", "                    if current_PL + todays_PL > 0:\n", "                        profitable_trades += 1\n", "                        total_profitable_pnl += current_PL + todays_PL\n", "                        total_profitable_holding_days += i - trade_enter_date\n", "                    else:\n", "                        loss_trades += 1\n", "                        total_loss_pnl += current_PL + todays_PL\n", "                        total_loss_holding_days += i - trade_enter_date\n", "                else:\n", "                    lag_status = -1\n", "            else:\n", "                todays_PL -= np.abs(position_size) * slippage\n", "                new_position = 0\n", "                lag_status = 0\n", "                print(trade_num, curve_name,\"1:\"+str(round(position_ratio,2)), \"Exit\", \"recd\" if position_size > 0 else \"paid\", round(interp_swap_rate, 2), \"Pnl\", round(current_PL + todays_PL), \"Size\", round(position_size), \"Half Life\", round(halflife), exit_reason, \"Holding Days\",i - trade_enter_date)\n", "                if current_PL + todays_PL > 0:\n", "                    profitable_trades += 1\n", "                    total_profitable_pnl += current_PL + todays_PL\n", "                    total_profitable_holding_days += i - trade_enter_date\n", "                else:\n", "                    loss_trades += 1\n", "                    total_loss_pnl += current_PL + todays_PL\n", "                    total_loss_holding_days += i - trade_enter_date\n", "            \n", "            \n", "            \n", "        elif swap_start_date <= d_dates[i]:\n", "            swap_start_date = d_dates[min(find_closest_next_date(d_dates[i] + relativedelta(months=1)), len(d_dates) - 1)]\n", "            print(trade_num, curve_name,\"1:\"+str(round(position_ratio,2)), \"Roll Swap\", \"recd\" if position_size > 0 else \"paid\", \"spot\", round(data[-1], 3), \"New Rate\", round(data[-1] + carry[-1], 3), \"Pnl\", round(todays_PL + current_PL), \"Size\", round(position_size), \"Half Life\", round(halflife))\n", "    \n", "    else: \n", "        \n", "        position_ratio, _ = np.polyfit(data1, data2, 1)\n", "        data = data2*position_ratio - data1\n", "        carry = carry2*position_ratio - carry1\n", "        vol_ratio = np.std(data1[-93:])/np.std(data2[-93:]*position_ratio)\n", "        slippage = slippage*max(1,position_ratio)\n", "\n", "        results = OU_process(np.asarray(data))\n", "        last, mu, theta, sigma = data[-1], results['mu'], results['theta'], results['sigma']\n", "        halflife, stdev = results['halflife'], results['stdev']\n", "        sigma_x = np.sqrt(sigma**2 / (2 * theta))\n", "        carry_vol_ratio = np.abs(carry[-1]) * 12 / sigma_x\n", "        pvalue = adfuller(data)[1]\n", "        \n", "        if vol_ratio_threshold > vol_ratio > 1 / vol_ratio_threshold and pvalue < 0.05 and halflife < halflife_entry_threshold and sigma_x>slippage*target_slippage_ratio:\n", "            if abs(last - mu) > entry_threshold * sigma_x:\n", "                if(lag_status == 1):\n", "                    position_multiplier = np.sign(last - mu)\n", "                    lag_status = 0\n", "                else:\n", "                    position_multiplier = 0\n", "                    lag_status = 1\n", "            else:\n", "                position_multiplier = 0\n", "    \n", "            if(sigma_x>0):\n", "                new_position = position_multiplier * (250000 / sigma_x)\n", "            else:\n", "                new_position = 0\n", "    \n", "            if new_position != 0 and check_correlation(data, model_start_index, i + 1) == 1:\n", "                new_position = 0\n", "    \n", "            if new_position != 0:\n", "                todays_PL -= abs(new_position) * slippage\n", "                swap_start_date = d_dates[i] + relativedelta(months=1)\n", "                swap_level = data[-1] + carry[-1]\n", "                trade_count += 1\n", "                trade_num = trade_count\n", "        \n", "                position_type = \"recd\" if new_position > 0 else \"paid\"\n", "                print(\n", "                    trade_count, curve_name,\"1:\"+str(round(position_ratio,2)), f\"Enter {position_type} @ {round(swap_level, 2)}\",\n", "                    \"Start Date\", swap_start_date, \"Carry\", round(carry[-1], 3),\n", "                    \"Size\", f\"{round(new_position):,.0f}\", \"Halflife\", round(halflife, 0),\n", "                    \"Carry/Vol Ratio\", round(carry_vol_ratio, 2), \"adf\", round(pvalue * 100, 2),\"Vol Ratio\",round(vol_ratio,2)\n", "                )\n", "\n", "    if lag_status == -1:  # Exit signal today or earlier, but not triggered yet\n", "        threshold = mu + 0.5 * sigma_x if position_size > 0 else mu - 0.5 * sigma_x\n", "        lag_status = -1 if (position_size > 0 and last < threshold) or (position_size <= 0 and last > threshold) else 0\n", "\n", "    elif lag_status == 1:  # Entry signal today or earlier, but not triggered yet\n", "        lag_status = 1 if abs(last - mu) > (entry_threshold - 0.5) * sigma_x else 0\n", "    \n", "    return todays_PL,new_position,swap_start_date,trade_num,exit_reason,lag_status,position_ratio"]}, {"cell_type": "code", "execution_count": 13, "id": "91007844", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 38.7136,  40.9238,  41.8207, ..., 412.994 , 411.365 , 403.039 ])"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["spot_data[1][0]"]}, {"cell_type": "code", "execution_count": 12, "id": "a9632ac7", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       0.387136\n", "1       0.409238\n", "2       0.418207\n", "3       0.437391\n", "4       0.433431\n", "          ...   \n", "2944    4.103980\n", "2945    4.146370\n", "2946    4.129940\n", "2947    4.113650\n", "2948    4.030390\n", "Name: RATES.OIS.USD_SOFR.PAR.2Y - CLOSE, Length: 2949, dtype: float64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["citi_data[\"RATES.OIS.USD_SOFR.PAR.2Y - CLOSE\"]"]}, {"cell_type": "code", "execution_count": null, "id": "3ca38727", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2013-03-04 0 0\n"]}], "source": ["\n", "for i in range(start_index, len(d_dates)):\n", "    todays_total_PL = 0\n", "    current_total_PL = sum(daily_PL)\n", "    print(d_dates[i], f\"{current_total_PL:,.0f}\",f\"{sum(currency_slippage):,.0f}\")\n", "    \n", "    max_pnl = max(max_pnl, current_total_PL)\n", "    drawdown = max(drawdown, max_pnl - current_total_PL)\n", "    \n", "    for c in range(0,len(curr)):\n", "        model_start_index = find_closest_next_date( d_dates[i] - relativedelta(years = num_years, months=num_months))\n", "        if(d_dates[i]>=curr_start_date[c] and d_dates[i]<=curr_end_date[c]):\n", "            for x in range(0,len(liquid_points[c])):\n", "                for y in range(x+1,len(liquid_points[c])):\n", "                    \n", "                    \n", "                    #### We define the data points d_x & d_y which we are checking and d_xy is the curve\n", "                    d_x, d_y = spot_data[c][x][model_start_index:i+1], spot_data[c][y][model_start_index:i+1]\n", "                    c_x, c_y = spot_carry[c][x][model_start_index:i+1], spot_carry[c][y][model_start_index:i+1]\n", "                    d_xy,c_xy = d_y - d_x, c_y - c_x\n", "                    \n", "                    curve_name = f\"{curr[c]} {liquid_points[c][x]}s{liquid_points[c][y]}s\"\n", "                    vol_ratio = np.std(d_y[-93:] - d_y[-94:-1])/np.std(d_x[-93:] - d_x[-94:-1])\n", "                    carry_1m = c_y - c_x\n", "                    todays_PL,new_position,swap_start_date,trade_num,exit_reason,lag_status,position_ratio = check_mean_reversion(i,d_x,d_y,c_x,c_y,position_ratio_spot_curve[c][x][y],position_size_spot_curve[c][x][y],swap_start_date_spot_curve[c][x][y],curve_name,position_pnl_spot_curve[c][x][y],position_number_spot_curve[c][x][y],position_enter_date_spot_curve[c][x][y],spot_slippage[c],lag_status_spot_curve[c][x][y])\n", "                    position_pnl_spot_curve[c][x][y]+= todays_PL\n", "                    swap_start_date_spot_curve[c][x][y] = swap_start_date\n", "                    lag_status_spot_curve[c][x][y] = lag_status\n", "                                       \n", "                        \n", "                    if(position_size_spot_curve[c][x][y] != new_position):\n", "                        ### Enter or exit trade\n", "                        currency_slippage[c] += spot_slippage[c]*max(abs(position_size_spot_curve[c][x][y])*max(1,position_ratio_spot_curve[c][x][y]),abs(new_position)*max(1,position_ratio))\n", "                        if(new_position != 0): ## enter new trade\n", "                            position_enter_date_spot_curve[c][x][y] = i\n", "                            #model_data.append(d_y*position_ratio - d_x)\n", "                            #model_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_enter_index.append(i)\n", "                            trade_ratio.append(position_ratio)\n", "                            #trade_data.append(d_y*position_ratio - d_x)\n", "                            #trade_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_text.append(curve_name)\n", "                            trade_size.append(round(new_position))\n", "                            #post_trade_data.append(d_y*position_ratio - d_x)\n", "                            #post_trade_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_pnl.append(0)\n", "                            trade_text2.append(\"Enter \"+ \"Rec @ \" if new_position>0 else \"Pay @ \" + str(round(d_y[-1]*position_ratio - d_x[-1],2))+\" on \"+str(d_dates[i]) + \", Carry = \"+str(round(c_y[-1]*position_ratio - c_x[-1],2)))\n", "                            trade_text3.append(\"\")\n", "                            trade_currency.append(curr[c])\n", "                            trade_type.append(\"Spot Curve\")\n", "                            trade_max_pnl.append(0)\n", "                            trade_min_pnl.append(0)\n", "                            trade_holding_days.append(0)\n", "                        else:\n", "                            #trade_data[trade_num] = d_y[int(trade_enter_index[trade_num])-i-1:]*position_ratio_spot_curve[c][x][y] - d_x[int(trade_enter_index[trade_num])-i-1:]\n", "                            #trade_dates[trade_num] = d_dates[int(trade_enter_index[trade_num]):i+1]\n", "                            trade_holding_days[trade_num] = i - trade_enter_index[trade_num]\n", "\n", "                            end_date = find_closest_next_date( d_dates[i] + relativedelta(months=3))\n", "                            #post_trade_data[trade_num] = spot_data[c][y][i:end_date]*position_ratio_spot_curve[c][x][y] - spot_data[c][x][i:end_date]\n", "                            #post_trade_dates[trade_num] = d_dates[i:end_date]\n", "                            trade_pnl[trade_num] = position_pnl_spot_curve[c][x][y]\n", "                            position_pnl_spot_curve[c][x][y] = 0\n", "                            trade_text3[trade_num] = \"Exit on \"+str(d_dates[i])+\" at \"+str(round(d_y[-1]*position_ratio_spot_curve[c][x][y] - d_x[-1],2))+\" \"+exit_reason\n", "                        \n", "                        position_size_spot_curve[c][x][y] = new_position\n", "                        position_number_spot_curve[c][x][y] = trade_num\n", "                        position_ratio_spot_curve[c][x][y] = position_ratio\n", "                    \n", "                    todays_total_PL += todays_PL\n", "                    currency_PL[c][i] += todays_PL\n", "                    if(position_size_spot_curve[c][x][y] != 0 or new_position !=0):\n", "                        trade_max_pnl[trade_num] = max(trade_max_pnl[trade_num],position_pnl_spot_curve[c][x][y])\n", "                        trade_max_pnl[trade_num] = min(trade_min_pnl[trade_num],position_pnl_spot_curve[c][x][y])\n", "                    \n", "                    for z in range(y+1,len(liquid_points[c])):\n", "                        \n", "                        d_z = spot_data[c][z][model_start_index:i+1]\n", "                        c_z = spot_carry[c][z][model_start_index:i+1]\n", "                        d_yz,c_yz = (d_z - d_y), (c_z - c_y)\n", "                        #d_xyz = (2*d_y - d_x - d_z)\n", "                        curve_name = curr[c]+\" \"+str(liquid_points[c][x])+\"s\"+str(liquid_points[c][y])+\"s\"+str(liquid_points[c][z])+\"s\"\n", "                        #vol_ratio = np.std(d_yz[-93:] - d_yz[-94:-1])/np.std(d_xy[-93:] - d_xy[-94:-1])\n", "                        #carry_1m = (spot_carry[c][y][model_start_index:i+1]*2 - spot_carry[c][x][model_start_index:i+1] -spot_carry[c][z][model_start_index:i+1])\n", "                        todays_PL,new_position,swap_start_date,trade_num,exit_reason,lag_status,position_ratio =check_mean_reversion(i,d_xy,d_yz,c_xy,c_yz,position_ratio_spot_fly[c][x][y][z],position_size_spot_fly[c][x][y][z],swap_start_date_spot_fly[c][x][y][z],curve_name,position_pnl_spot_fly[c][x][y][z],position_number_spot_fly[c][x][y][z],position_enter_date_spot_fly[c][x][y][z],spot_slippage[c],lag_status_spot_fly[c][x][y][z])\n", "                        position_pnl_spot_fly[c][x][y][z]+= todays_PL\n", "                        swap_start_date_spot_fly[c][x][y][z] = swap_start_date\n", "                        lag_status_spot_fly[c][x][y][z] = lag_status\n", "                        \n", "                    \n", "                        if(position_size_spot_fly[c][x][y][z] != new_position):\n", "                        ### Enter or exit trade\n", "                            currency_slippage[c] += spot_slippage[c]*max(abs(position_size_spot_fly[c][x][y][z])*max(1,position_ratio_spot_fly[c][x][y][z]),abs(new_position)*max(1,position_ratio))\n", "                            if(new_position != 0): ## enter new trade\n", "                                position_enter_date_spot_fly[c][x][y][z] = i\n", "                                #model_data.append(d_yz*position_ratio - d_xy)\n", "                                #model_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_enter_index.append(i)\n", "                                trade_ratio.append(position_ratio)\n", "                                #trade_data.append(d_yz*position_ratio - d_xy)\n", "                                #trade_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_text.append(curve_name)\n", "                                trade_size.append(round(new_position))\n", "                                #post_trade_data.append(d_yz*position_ratio - d_xy)\n", "                                #post_trade_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_pnl.append(0)\n", "                                trade_text2.append(\"Enter \"+ \"Rec @ \" if new_position>0 else \"Pay @ \" + str(round(d_yz[-1]*position_ratio - d_xy[-1],2))+\" on \"+str(d_dates[i]) + \", Carry = \"+str(round(c_yz[-1]*position_ratio - c_xy[-1],2)))\n", "                                trade_text3.append(\"\")\n", "                                trade_currency.append(curr[c])\n", "                                trade_type.append(\"Spot Fly\")\n", "                                trade_max_pnl.append(0)\n", "                                trade_min_pnl.append(0)\n", "                                trade_holding_days.append(0)\n", "                            else:\n", "                                #trade_data[trade_num] = d_yz[int(trade_enter_index[trade_num])-i-1:]*position_ratio_spot_fly[c][x][y][z] - d_xy[int(trade_enter_index[trade_num])-i-1:]\n", "                                #trade_dates[trade_num] = d_dates[int(trade_enter_index[trade_num]):i+1]\n", "                                trade_holding_days[trade_num] = i - trade_enter_index[trade_num]\n", "\n", "                                end_date = find_closest_next_date( d_dates[i] + relativedelta(months=3))\n", "                                #post_trade_data[trade_num] = (spot[c][z][i:end_date]- spot_data[c][y][i:end_date])*position_ratio_spot_fly[c][x][y][z] - (spot_data[c][y][i:end_date] - spot_data[c][x][i:end_date])\n", "                                #post_trade_dates[trade_num] = d_dates[i:end_date]\n", "                                trade_pnl[trade_num] = position_pnl_spot_fly[c][x][y][z]\n", "                                position_pnl_spot_fly[c][x][y][z] = 0\n", "                                trade_text3[trade_num] = \"Exit on \"+str(d_dates[i])+\" at \"+str(round(d_yz[-1]*position_ratio_spot_fly[c][x][y][z] - d_xy[-1],2))+\" \"+exit_reason\n", "                        \n", "                            position_size_spot_fly[c][x][y][z] = new_position\n", "                            position_number_spot_fly[c][x][y][z] = trade_num\n", "                            position_ratio_spot_fly[c][x][y][z] = position_ratio\n", "                        \n", "                        todays_total_PL += todays_PL\n", "                        currency_PL[c][i] += todays_PL\n", "                        if(position_size_spot_fly[c][x][y][z] != 0 or new_position !=0):\n", "                            trade_max_pnl[trade_num] = max(trade_max_pnl[trade_num],position_pnl_spot_fly[c][x][y][z])\n", "                            trade_max_pnl[trade_num] = min(trade_min_pnl[trade_num],position_pnl_spot_fly[c][x][y][z])\n", "                        \n", "        if(d_dates[i]>=curr_start_date[c] and d_dates[i]<=curr_end_date[c]):\n", "            for x in range(0,len(forward_points[c])):\n", "                for y in range(x+1,len(forward_points[c])):\n", "                    #print(curr[c],forward_points[c][x],forward_points[c][y])\n", "                    #### We define the data points d_x & d_y which we are checking and d_xy is the curve\n", "                    d_x = forward_data[c][x][model_start_index:i+1]\n", "                    d_y = forward_data[c][y][model_start_index:i+1]\n", "                    c_x = forward_carry[c][x][model_start_index:i+1]\n", "                    c_y = forward_carry[c][y][model_start_index:i+1]\n", "                    d_xy,c_xy = (d_y - d_x),(c_y - c_x)\n", "                    vol_ratio = np.std(d_y[-93:] - d_y[-94:-1])/np.std(d_x[-93:] - d_x[-94:-1])\n", "                    carry_1m = (forward_carry[c][y][model_start_index:i+1] - forward_carry[c][x][model_start_index:i+1])\n", "                    curve_name = curr[c]+\" \"+str(forward_points[c][x])+\"-\"+str(forward_points[c][y])\n", "                    \n", "                    ####this part of the code ensures that the 2nd forward starts after 1st forward ends (so we dont get 5y5y 7y3y for eg)\n", "                    parts = forward_points[c][x].split('y')\n", "                    i_all = int(parts[0])+int(parts[1])\n", "                    parts = forward_points[c][y].split('y')\n", "                    j_start = int(parts[0])\n", "                    j_all = int(parts[0])+int(parts[1])\n", "                    if (j_start<i_all):\n", "                        continue\n", "                \n", "                    todays_PL,new_position,swap_start_date,trade_num,exit_reason,lag_status,position_ratio = check_mean_reversion(i,d_x,d_y,c_x,c_y,position_ratio_forward_curve[c][x][y],position_size_forward_curve[c][x][y],swap_start_date_forward_curve[c][x][y],curve_name,position_pnl_forward_curve[c][x][y],position_number_forward_curve[c][x][y],position_enter_date_forward_curve[c][x][y],forward_slippage[c],lag_status_forward_curve[c][x][y])\n", "                    position_pnl_forward_curve[c][x][y]+= todays_PL\n", "                    swap_start_date_forward_curve[c][x][y] = swap_start_date\n", "                    lag_status_forward_curve[c][x][y] = lag_status\n", "\n", "                    \n", "                    if(position_size_forward_curve[c][x][y] != new_position):\n", "                        currency_slippage[c] += forward_slippage[c]*max(abs(position_size_forward_curve[c][x][y])*max(1,position_ratio_forward_curve[c][x][y]),abs(new_position)*max(1,position_ratio))\n", "                        ### Enter or exit trade\n", "                        if(new_position != 0): ## enter new trade\n", "                            position_enter_date_forward_curve[c][x][y] = i\n", "                            #model_data.append(d_y*position_ratio - d_x)\n", "                            #model_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_enter_index.append(i)\n", "                            trade_ratio.append(position_ratio)\n", "                            #trade_data.append(d_y*position_ratio - d_x)\n", "                            #trade_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_text.append(curve_name)\n", "                            trade_size.append(round(new_position))\n", "                            #post_trade_data.append(d_y*position_ratio - d_x)\n", "                            #post_trade_dates.append(d_dates[model_start_index:i+1])\n", "                            trade_pnl.append(0)\n", "                            trade_text2.append(\"Enter \"+ \"Rec @ \" if new_position>0 else \"Pay @ \" + str(round(d_y[-1]*position_ratio - d_x[-1],2))+\" on \"+str(d_dates[i]) + \", Carry = \"+str(round(c_y[-1]*position_ratio - c_x[-1],2)))\n", "                            trade_text3.append(\"\")\n", "                            trade_currency.append(curr[c])\n", "                            trade_type.append(\"Forward Curve\")\n", "                            trade_max_pnl.append(0)\n", "                            trade_min_pnl.append(0)\n", "                            trade_holding_days.append(0)\n", "\n", "                        else:\n", "                            #trade_data[trade_num] = d_y[int(trade_enter_index[trade_num])-i-1:]*position_ratio_forward_curve[c][x][y] - d_x[int(trade_enter_index[trade_num])-i-1:]\n", "                            #trade_dates[trade_num] = d_dates[int(trade_enter_index[trade_num]):i+1]\n", "                            trade_holding_days[trade_num] = i - trade_enter_index[trade_num]\n", "                            \n", "                            end_date = find_closest_next_date(d_dates[i] + relativedelta(months=3))\n", "                            #post_trade_data[trade_num] = forward_data[c][y][i:end_date]*position_ratio_forward_curve[c][x][y] - forward_data[c][x][i:end_date]\n", "                            #post_trade_dates[trade_num] = d_dates[i:end_date]\n", "                            trade_pnl[trade_num] = position_pnl_forward_curve[c][x][y]\n", "                            position_pnl_forward_curve[c][x][y] = 0\n", "                            trade_text3[trade_num] = \"Exit on \"+str(d_dates[i])+\" at \"+str(round(d_y[-1]*position_ratio_forward_curve[c][x][y] - d_x[-1],2))+\" \"+exit_reason\n", "                        \n", "                        position_size_forward_curve[c][x][y] = new_position\n", "                        position_number_forward_curve[c][x][y] = trade_num\n", "                        position_ratio_forward_curve[c][x][y] = position_ratio\n", "                    \n", "                    todays_total_PL += todays_PL\n", "                    currency_PL[c][i] += todays_PL\n", "                    if(position_size_forward_curve[c][x][y] != 0 or new_position !=0):\n", "                        trade_max_pnl[trade_num] = max(trade_max_pnl[trade_num],position_pnl_forward_curve[c][x][y])\n", "                        trade_max_pnl[trade_num] = min(trade_min_pnl[trade_num],position_pnl_forward_curve[c][x][y])\n", "                    \n", "                \n", "                    \n", "                    for z in range(y+1,len(forward_points[c])):\n", "                        d_z = forward_data[c][z][model_start_index:i+1]\n", "                        c_z = forward_carry[c][z][model_start_index:i+1]\n", "                        d_yz = (d_z - d_y)\n", "                        c_yz = (c_z - c_y)\n", "                        #d_xyz = (2*d_y - d_x - d_z)\n", "                        vol_ratio = np.std(d_yz[-93:] - d_yz[-94:-1])/np.std(d_xy[-93:] - d_xy[-94:-1])\n", "                        carry_1m = (forward_carry[c][y][model_start_index:i+1]*2 - forward_carry[c][x][model_start_index:i+1]- forward_carry[c][z][model_start_index:i+1])\n", "                        curve_name = curr[c]+\" \"+str(forward_points[c][x])+\"-\"+str(forward_points[c][y])+\"-\"+str(forward_points[c][z])\n", "                        \n", "                        parts = forward_points[c][z].split('y')\n", "                        k_start = int(parts[0])\n", "                        if (j_start<i_all) or (k_start<j_all):\n", "                            continue\n", "                \n", "                        todays_PL,new_position,swap_start_date,trade_num,exit_reason,lag_status,position_ratio = check_mean_reversion(i,d_xy,d_yz,c_xy,c_yz,position_ratio_forward_fly[c][x][y][z],position_size_forward_fly[c][x][y][z],swap_start_date_forward_fly[c][x][y][z],curve_name,position_pnl_forward_fly[c][x][y][z],position_number_forward_fly[c][x][y][z],position_enter_date_forward_fly[c][x][y][z],forward_slippage[c],lag_status_forward_fly[c][x][y][z])\n", "                        position_pnl_forward_fly[c][x][y][z]+= todays_PL\n", "                        swap_start_date_forward_fly[c][x][y][z] = swap_start_date\n", "                        lag_status_forward_fly[c][x][y][z] = lag_status\n", "\n", "                        \n", "                        if(position_size_forward_fly[c][x][y][z] != new_position):\n", "                        ### Enter or exit trade\n", "                            currency_slippage[c] += forward_slippage[c]*max(abs(position_size_forward_fly[c][x][y][z])*max(1,position_ratio_forward_fly[c][x][y][z]),abs(new_position)*max(1,position_ratio))\n", "                            if(new_position != 0): ## enter new trade\n", "                                position_enter_date_forward_fly[c][x][y][z] = i\n", "                                #model_data.append(d_yz*position_ratio - d_xy)\n", "                                #model_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_enter_index.append(i)\n", "                                trade_ratio.append(position_ratio)\n", "                                #trade_data.append(d_yz*position_ratio - d_xy)\n", "                                #trade_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_text.append(curve_name)\n", "                                trade_size.append(round(new_position))\n", "                                #post_trade_data.append(d_yz*position_ratio - d_xy)\n", "                                #post_trade_dates.append(d_dates[model_start_index:i+1])\n", "                                trade_pnl.append(0)\n", "                                trade_text2.append(\"Enter \"+ \"Rec @ \" if new_position>0 else \"Pay @ \" + str(round(d_yz[-1]*position_ratio - d_xy[-1],2))+\" on \"+str(d_dates[i]) + \", Carry = \"+str(round(d_yz[-1]*position_ratio - d_xy[-1],2)))\n", "                                trade_text3.append(\"\")\n", "                                trade_currency.append(curr[c])\n", "                                trade_type.append(\"Forward Fly\")\n", "                                trade_max_pnl.append(0)\n", "                                trade_min_pnl.append(0)\n", "                                trade_holding_days.append(0)\n", "                            else:\n", "                                #trade_data[trade_num] = d_yz[int(trade_enter_index[trade_num])-i-1:]*position_ratio - d_xy[int(trade_enter_index[trade_num])-i-1:]\n", "                                #trade_dates[trade_num] = d_dates[int(trade_enter_index[trade_num]):i+1]\n", "                                trade_holding_days[trade_num] = i - trade_enter_index[trade_num]\n", "                                end_date = find_closest_next_date(d_dates[i] + relativedelta(months=3))\n", "                                #post_trade_data[trade_num] = (forward_data[c][z][i:end_date]- forward_data[c][y][i:end_date])*position_ratio_forward_fly[c][x][y][z] - (forward_data[c][y][i:end_date] - forward_data[c][x][i:end_date])\n", "                                #post_trade_dates[trade_num] = d_dates[i:end_date]\n", "                                trade_pnl[trade_num] = position_pnl_forward_fly[c][x][y][z]\n", "                                position_pnl_forward_fly[c][x][y][z] = 0\n", "                                trade_text3[trade_num] = \"Exit on \"+str(d_dates[i])+\" at \"+str(round(d_yz[-1]*position_ratio_forward_fly[c][x][y][z] - d_xy[-1],2))+\" \"+exit_reason\n", "                        \n", "                            position_size_forward_fly[c][x][y][z] = new_position\n", "                            position_number_forward_fly[c][x][y][z] = trade_num\n", "                            position_ratio_forward_fly[c][x][y][z] = position_ratio\n", "                    \n", "                        todays_total_PL += todays_PL\n", "                        currency_PL[c][i] += todays_PL\n", "                        if(position_size_forward_fly[c][x][y][z] != 0 or new_position !=0):\n", "                            trade_max_pnl[trade_num] = max(trade_max_pnl[trade_num],position_pnl_forward_fly[c][x][y][z])\n", "                            trade_max_pnl[trade_num] = min(trade_min_pnl[trade_num],position_pnl_forward_fly[c][x][y][z])\n", "                    \n", "#### Algo to handle last day of currency - TODO\n", "#        elif d_dates[i]==curr_end_date[c]:\n", "#            for x in range(0,len(liquid_points[c])):\n", "#                for y in range(x+1,len(liquid_points[c])):\n", "#                    if(position_size_spot_curve[c][x][y] != 0):\n", "#                        #ignore 1 day carry & slippage for pnl\n", "#                        pnl = position_size_spot_curve[c][x][y]*(spot_data[c][y][i-1] - spot_data[c][y][i] - spot_data[c][x][i-1] + spot_data[c][x][i])\n", "#                        position_number_spot_curve[c][x][y] += pnl\n", "#                        trade_num = position_number_spot_curve[c][x][y]\n", "#                        trade_data[trade_num] = (spot_data[c][y] - spot_data[c][x])[int(trade_enter_index[trade_num])-i-1:]\n", "#                        trade_dates[trade_num] = d_dates[int(trade_enter_index[trade_num]):i+1]\n", "                      \n", "                        \n", "    #print(\"GBP 5s10s25s size\",position_size_spot_fly[3][2][4][8])\n", "    daily_PL.append(todays_total_PL)                 \n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "9dc14ee6", "metadata": {}, "outputs": [], "source": ["print(\"1yr Mean Reversion, 5 day half life threshold, 10 day exit threshold, 2 stop loss\")\n", "day = []\n", "\n", "total_pnl = []\n", "\n", "\n", "for i in range (50,len(daily_PL)):\n", "    day.append(d_dates[i+start_index])\n", "    total_pnl.append(sum(daily_PL[0:i]))\n", "\n", "stdev = np.std(daily_PL)*np.sqrt(252)\n", "pl_years = len(daily_PL)/252\n", "sharpe = (total_pnl[-1]/(pl_years))/stdev\n", "hit_ratio = str(round(profitable_trades*100/(profitable_trades+loss_trades)))+\"%\"\n", "avg_holding_days = (total_profitable_holding_days+total_loss_holding_days)/(profitable_trades+loss_trades)\n", "\n", "print(\"Total P&L = \",\"{:,.0f}\".format(sum(daily_PL)),\"Hit Ratio = \",hit_ratio, \"Sharpe Ratio = \",round(sharpe,3))\n", "print(\"Avg Holding Days = \",round(avg_holding_days,1),\"Max Drawdown =\",\"{:,.0f}\".format(drawdown))\n", "print(\"Hit Trades Avg P&L = \", \"{:,.0f}\".format(round(total_profitable_pnl/profitable_trades)),\"Avg Holding Days = \", round(total_profitable_holding_days/profitable_trades,1))\n", "print(\"Loss Trades Avg P&L = \", \"{:,.0f}\".format(round(total_loss_pnl/loss_trades)),\"Avg Holding Days = \", round(total_loss_holding_days/loss_trades,1))\n", "\n", "\n", "\n", "\n", "print(\"Sharpe=\",round(sharpe,3),)\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(day, total_pnl,label=\"P&L\", linewidth=2)\n", "\n", "\n", "# Adding title and labels\n", "plt.title(f'Strategy PnL Over the Last {round(pl_years)} Years', fontsize=16)\n", "plt.xlabel('Days', fontsize=12)\n", "plt.ylabel('Total PnL (in Millions)', fontsize=12)\n", "\n", "# Format y-axis to show numbers in millions\n", "formatter = mticker.FuncFormatter(lambda x, pos: f'{x / 1e6:.1f}M')\n", "plt.gca().yaxis.set_major_formatter(formatter)\n", "\n", "# Add gridlines for better readability\n", "plt.grid(True, which='both', linestyle='--', linewidth=0.5)\n", "\n", "# Optionally, adjust the ticks on the x-axis\n", "plt.xticks(rotation=45)\n", "\n", "plt.legend(fontsize=12)\n", "\n", "# Show the plot\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "556539a0", "metadata": {}, "outputs": [], "source": ["pio.renderers.default = \"jupyterlab\"\n", "fig = go.Figure()\n", "num_traces_per_trade = 6  # Now each trade has 3 traces: model, trade, and horizontal line\n", "\n", "for i in range(len(model_data)):\n", "    # Scatter for model data\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=model_dates[i],\n", "            y=model_data[i],\n", "            mode=\"lines\",\n", "            name=trade_text[i],\n", "            marker=dict(size=4, opacity=0.8, color=\"blue\"),  # Fix color name\n", "            visible=False if i != 0 else True  # Show only first trade by default\n", "        )\n", "    )\n", "\n", "    # Scatter for trade data\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=trade_dates[i],\n", "            y=trade_data[i],\n", "            mode=\"lines\",\n", "            name=\"Size: \" + f\"{trade_size[i]:,}\",\n", "            marker=dict(size=4, opacity=0.8, color=\"red\"),\n", "            visible=False if i != 0 else True\n", "        )\n", "    )\n", "\n", "    # Scatter for trade data\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x=post_trade_dates[i],\n", "            y=post_trade_data[i],\n", "            mode=\"lines\",\n", "            name=\"P&L: \" + f\"{round(trade_pnl[i]):,}\",\n", "            marker=dict(size=4, opacity=0.8, color=\"green\"),\n", "            visible=False if i != 0 else True\n", "        )\n", "    )\n", "\n", "    # Get OU process results for the current trade\n", "    results = OU_process(model_data[i])\n", "    sigma_x = np.sqrt(results[\"sigma\"]**2 / (2 * results[\"theta\"]))\n", "    # Horizontal line as a <PERSON>att<PERSON> trace instead of a shape\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x = [min(model_dates[i]), max(post_trade_dates[i]) if len(post_trade_dates[i]) > 0 else max(trade_dates[i])],\n", "            y=[results[\"mu\"], results[\"mu\"]],  # Keep y constant\n", "            mode=\"lines\",\n", "            name=trade_text2[i],\n", "            line=dict(color=\"black\", width=2),\n", "            visible=False if i != 0 else True  # Show only the first trade by default\n", "        )\n", "    )\n", "\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x = [min(model_dates[i]), max(post_trade_dates[i]) if len(post_trade_dates[i]) > 0 else max(trade_dates[i])],\n", "            y=[results[\"mu\"]+entry_threshold*sigma_x, results[\"mu\"]+entry_threshold*sigma_x],  # Keep y constant\n", "            mode=\"lines\",\n", "            name=trade_text3[i],\n", "            line=dict(color=\"black\", width=2, dash=\"dash\"),\n", "            visible=False if i != 0 else True,  # Show only the first trade by default\n", "        )\n", "    )\n", "\n", "    fig.add_trace(\n", "        <PERSON><PERSON>(\n", "            x = [min(model_dates[i]), max(post_trade_dates[i]) if len(post_trade_dates[i]) > 0 else max(trade_dates[i])],\n", "            y=[results[\"mu\"]-entry_threshold*sigma_x, results[\"mu\"]-entry_threshold*sigma_x],  # Keep y constant\n", "            mode=\"lines\",\n", "            #name=f\"Mean: {results['mu']:.2f}\",\n", "            line=dict(color=\"black\", width=2, dash=\"dash\"),\n", "            visible=False if i != 0 else True,  # Show only the first trade by default\n", "            showlegend=False\n", "        )\n", "    )\n", "\n", "# Add dropdown menu for navigation\n", "buttons = []\n", "for i in range(len(model_data)):\n", "    visibility = [False] * len(model_data) * num_traces_per_trade  # Adjust for 3 traces per trade\n", "    start_idx = i * num_traces_per_trade\n", "    visibility[start_idx : start_idx + num_traces_per_trade] = [True] * num_traces_per_trade  # Show relevant trade\n", "    buttons.append(\n", "        dict(\n", "            label=f\"Trade {i+1}\",\n", "            method=\"update\",\n", "            args=[{\"visible\": visibility}]\n", "        )\n", "    )\n", "\n", "# Update layout with dropdown\n", "fig.update_layout(\n", "    updatemenus=[\n", "        dict(\n", "            active=0,\n", "            buttons=buttons,\n", "            showactive=True,\n", "            direction=\"down\",\n", "            x=0.1,\n", "            y=1.15\n", "        )\n", "    ],\n", "    xaxis_title=\"X\",\n", "    yaxis_title=\"Y\",\n", "    height=700,\n", "    width=900,\n", "    plot_bgcolor=\"white\",\n", "    paper_bgcolor=\"white\"\n", ")\n", "\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 1, "id": "37526a94", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# Create a DataFrame\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame({\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mholding_days\u001b[39m\u001b[38;5;124m\"\u001b[39m: trade_holding_days, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtrade_pnl\u001b[39m\u001b[38;5;124m\"\u001b[39m: trade_pnl})\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# Define holding period bins and labels\u001b[39;00m\n\u001b[0;32m      5\u001b[0m bins \u001b[38;5;241m=\u001b[39m [\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m5\u001b[39m, \u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m20\u001b[39m, np\u001b[38;5;241m.\u001b[39minf]\n", "\u001b[1;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["# Create a DataFrame\n", "df = pd.DataFrame({\"holding_days\": trade_holding_days, \"trade_pnl\": trade_pnl})\n", "\n", "# Define holding period bins and labels\n", "bins = [0, 5, 10, 20, np.inf]\n", "labels = [\"<5\", \"5-10\", \"10-20\", \"20+\"]\n", "\n", "# Categorize trades into buckets\n", "df[\"holding_bucket\"] = pd.cut(df[\"holding_days\"], bins=bins, labels=labels, right=False)\n", "\n", "# Compute average P&L, count of trades, and hit ratio for each bucket\n", "bucket_stats = df.groupby(\"holding_bucket\", observed=False).agg(\n", "    average_pnl=(\"trade_pnl\", \"mean\"),\n", "    trade_count=(\"trade_pnl\", \"count\"),\n", "    positive_trades=(\"trade_pnl\", lambda x: (x > 0).sum())  # Count trades with +ve P&L\n", ")\n", "\n", "# Compute hit ratio (percentage of positive P&L trades per bucket)\n", "bucket_stats[\"hit_ratio\"] = bucket_stats[\"positive_trades\"]*100 / bucket_stats[\"trade_count\"]\n", "\n", "# Plot the histogram\n", "fig, ax1 = plt.subplots(figsize=(10, 5))\n", "\n", "# Bar chart for Average P&L\n", "ax1.bar(bucket_stats.index, bucket_stats[\"average_pnl\"], color='blue', alpha=0.7, label=\"Average P&L\", edgecolor='black')\n", "\n", "# Secondary axis for Trade Count\n", "ax2 = ax1.twinx()\n", "ax2.plot(bucket_stats.index, bucket_stats[\"trade_count\"], color='red', marker='o', linestyle='dashed', label=\"Trade Count\")\n", "\n", "# Third axis for Hit Ratio\n", "ax3 = ax1.twinx()\n", "ax3.spines[\"right\"].set_position((\"outward\", 60))  # Move third axis outward\n", "ax3.plot(bucket_stats.index, bucket_stats[\"hit_ratio\"], color='black', marker='s', linestyle='dotted', label=\"Hit Ratio\")\n", "\n", "# Formatting\n", "ax1.set_xlabel(\"Holding Period (Days)\")\n", "ax1.set_ylabel(\"Average Trade P&L\", color=\"blue\")\n", "ax2.set_ylabel(\"Number of Trades\", color=\"red\")\n", "ax3.set_ylabel(\"Hit Ratio\", color=\"black\")\n", "ax1.set_title(\"Average Trade P&L, Number of Trades, and Hit Ratio by Holding Period\")\n", "ax1.grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "ax2.set_ylim(0, max(bucket_stats[\"trade_count\"]) * 1.1)\n", "ax3.set_ylim(0, 100)  # Hit ratio is between 0 and 1\n", "\n", "# Legends\n", "ax1.legend(loc=\"upper left\")\n", "ax2.legend(loc=\"upper right\")\n", "ax3.legend(loc=\"lower right\")\n", "\n", "# Show the plot\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 10, "id": "77b99253", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Sum P&L per currency\n", "total_pnl = [sum(pl) for pl in currency_PL]\n", "\n", "# Create a DataFrame\n", "df = pd.DataFrame({\"Currency\": curr, \"P&L\": total_pnl, \"Slippage\": currency_slippage})\n", "\n", "\n", "# Mapping to combine related currencies\n", "currency_mapping = {\n", "    \"USD Libor\": \"USD\",\n", "    \"USD SOFR\": \"USD\",\n", "    \"EUR\": \"EUR\",\n", "    \"GBP\": \"GBP\",\n", "    \"CAD Libor\": \"CAD\",\n", "    \"CAD CORRA\": \"CAD\",\n", "    \"JPY Libor\": \"JPY\",\n", "    \"JPY TONAR\": \"JPY\",\n", "    \"SEK\": \"SEK\",\n", "    \"AUD\": \"AUD\"\n", "}\n", "\n", "# Initialize aggregated data\n", "aggregated_pnl = {}\n", "aggregated_slippage = {}\n", "aggregated_trades = {}\n", "\n", "# Count trades and aggregate P&L per major currency\n", "for i, instrument in enumerate(trade_currency):\n", "    main_currency = currency_mapping[instrument]  # Get the mapped currency\n", "\n", "    # Aggregate P&L\n", "    aggregated_pnl[main_currency] = aggregated_pnl.get(main_currency, 0) + trade_pnl[i]\n", "\n", "    # Count trades per major currency\n", "    aggregated_trades[main_currency] = aggregated_trades.get(main_currency, 0) + 1\n", "\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame({\n", "    \"Currency\": aggregated_pnl.keys(),\n", "    \"P&L\": aggregated_pnl.values(),\n", "    \"Trade Count\": aggregated_trades.values()\n", "})\n", "\n", "# Set bar width\n", "bar_width = 0.3\n", "\n", "# X locations for the groups\n", "x = np.arange(len(df[\"Currency\"]))\n", "\n", "# Plot histogram (bar chart)\n", "fig, ax1 = plt.subplots(figsize=(10, 5))\n", "\n", "# P&L and Slippage bars\n", "ax1.bar(x - bar_width/2, df[\"P&L\"], bar_width, color='blue', alpha=0.7, label=\"P&L\", edgecolor='black')\n", "\n", "# Secondary axis for Trade Count as a line plot\n", "ax2 = ax1.twinx()\n", "ax2.plot(x, df[\"Trade Count\"], color='red', marker='o', linestyle='dashed', label=\"Trade Count\")\n", "\n", "# Formatting\n", "ax1.set_xlabel(\"Currency\")\n", "ax1.set_ylabel(\"P&L and Slippage\")\n", "ax2.set_ylabel(\"Trade Count\", color=\"red\")\n", "ax1.set_title(\"P&L and Trade Count by <PERSON><PERSON><PERSON><PERSON> (Grouped)\")\n", "ax1.set_xticks(x)\n", "ax1.set_xticklabels(df[\"Currency\"])\n", "ax1.grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "ax2.set_ylim(0, max(df[\"Trade Count\"]) * 1.1)\n", "\n", "# Legends\n", "ax1.legend(loc=\"upper left\")\n", "ax2.legend(loc=\"upper right\")\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "6fc803c2-3c8a-4405-8499-378cd509eebd", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create DataFrame\n", "df = pd.DataFrame({\"Trade Type\": trade_type, \"Holding Days\": trade_holding_days, \"Trade P&L\": trade_pnl})\n", "\n", "# Compute average holding days, average P&L, and trade count\n", "trade_stats = df.groupby(\"Trade Type\", observed=False).agg(\n", "    average_holding_days=(\"Holding Days\", \"mean\"),\n", "    average_pnl=(\"Trade P&L\", \"mean\"),\n", "    trade_count=(\"Trade P&L\", \"count\")  # Count number of trades per trade type\n", ")\n", "\n", "# Plot side-by-side bar charts\n", "fig, ax = plt.subplots(1, 3, figsize=(15, 5))\n", "\n", "# Histogram for Average Holding Days\n", "ax[0].bar(trade_stats.index, trade_stats[\"average_holding_days\"], color='blue', alpha=0.7, edgecolor='black')\n", "ax[0].set_xlabel(\"Trade Type\")\n", "ax[0].set_ylabel(\"Average Holding Days\")\n", "ax[0].set_title(\"Average Holding Days by Trade Type\")\n", "ax[0].tick_params(axis='x', rotation=30)\n", "ax[0].grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "\n", "# Histogram for Average P&L\n", "ax[1].bar(trade_stats.index, trade_stats[\"average_pnl\"], color='red', alpha=0.7, edgecolor='black')\n", "ax[1].set_xlabel(\"Trade Type\")\n", "ax[1].set_ylabel(\"Average Trade P&L\")\n", "ax[1].set_title(\"Average Trade P&L by Trade Type\")\n", "ax[1].tick_params(axis='x', rotation=30)\n", "ax[1].grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "\n", "# Histogram for Trade Count\n", "ax[2].bar(trade_stats.index, trade_stats[\"trade_count\"], color='green', alpha=0.7, edgecolor='black')\n", "ax[2].set_xlabel(\"Trade Type\")\n", "ax[2].set_ylabel(\"Number of Trades\")\n", "ax[2].set_title(\"Trade Count by Trade Type\")\n", "ax[2].tick_params(axis='x', rotation=30)\n", "ax[2].grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "\n", "# Show the plots\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "86d25e95-fdd5-4d63-858d-529e77bb7a69", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0oAAAHUCAYAAAAEKdj3AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAaohJREFUeJzt3Xl8U3X2//H3TUr3ltIKLUiBtqwiDAoO2yigAwouOMzXDcd9QXFEFkUdHQFFcAMZddxmQfQng+M2o46CjCzqIIpSFAsiW6FACxVaUkppafL5/VETmjQtCbZNWl/Px6OPR3Puzb3n3PvJzT29ya1ljDECAAAAAHjYQp0AAAAAAIQbGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAGgklmUF9LNixYoGWf+1116rTp06Nciya3P06FG98MILOuOMM5ScnKzY2Fh17NhRo0eP1ttvv+2Zb8+ePZo+fbrWrVvXIHmsWrVK06dPV3Fxcb0vO9DtOnToUK/9HBMTo1/84heaN2+eXC5Xvefltnr1akVERGjKlCl+p8+aNUuWZWnx4sUNlgMANEU0SgDQSD777DOvn1GjRikmJqZG/PTTTw91qvXmqquu0u23365hw4bp//2//6d3331X999/vyIiIrRkyRLPfHv27NGMGTMatFGaMWNGgzRKwcjMzPTs59dee00nn3yyJk2apHvvvbfB1jlgwADdfffdmjdvnj799FOvad9++61mzJihcePG6bzzzmuwHACgKYoIdQIA8HMxYMAAr8etW7eWzWarEfd1+PBhxcbGNmRqDWL79u167bXX9MADD2jGjBme+DnnnKObbrqpQa+iuJWVlSk6OrrB1xOomJgYr/09cuRIde/eXc8884xmzpypFi1anPCynU6nKisrFRUVVWPatGnT9J///EfXXnutvvnmG8XGxqqyslLXXnut2rdvryeeeOKE1xuMpjqWAfw8cUUJAMLI0KFDdeqpp+rjjz/WoEGDFBsbq+uvv16S9Nprr2nEiBFq27atYmJi1KNHD91zzz0qLS2tsZyXXnpJ3bp1U1RUlHr06KGXX37Z7/oqKio0c+ZMde/eXVFRUWrdurWuu+46FRYWes23bNkyDR06VCkpKYqJiVGHDh3029/+VocPH661lv3790uS2rZt63e6zVb1FrRixQqdccYZkqTrrrvO89G06dOnS5K+/PJLXX755erUqZNiYmLUqVMnXXHFFdqxY0eNmi3L0ocffqjrr79erVu3VmxsrO69917dddddkqSMjAy/H3F87bXXNHDgQMXFxSk+Pl7nnnuusrOzT3i7BqpFixbq27evDh8+7NnmBQUFGjdunNq3b6/IyEhlZGRoxowZqqys9DwvNzdXlmXpscce08yZM5WRkaGoqCgtX77c73oiIyP18ssvKy8vT3fffbckafbs2crOztZLL72k+Pj4gMdCoOPw2muvVXx8vNavX68RI0YoISFB55xzzk/aXgDQmLiiBABhJj8/X7/73e80depUzZo1y9NQbN68WaNGjdLEiRMVFxen7777To8++qi++OILLVu2zPP8l156Sdddd51Gjx6tOXPm6ODBg5o+fbrKy8s9y5Ikl8ul0aNH65NPPtHUqVM1aNAg7dixQ9OmTdPQoUP15ZdfKiYmRrm5uTr//PN15pln6u9//7uSkpK0e/duLV68WBUVFbVeIejRo4eSkpI0Y8YM2Ww2jRgxwu93eU4//XTNnz9f1113ne6//36df/75kqT27dtLqmoKunXrpssvv1zJycnKz8/Xc889pzPOOEMbNmzQSSed5LW866+/Xueff75eeeUVlZaWql+/fjp8+LCefvppvfXWW57G7ZRTTpFU9R2d+++/37P+iooKPf744zrzzDP1xRdfeOYLdLsGa+vWrYqIiFCrVq1UUFCgX/7yl7LZbHrggQeUlZWlzz77TDNnzlRubq7mz5/v9dynnnpKXbt21RNPPKHExER16dKl1vX07t1bM2bM0B/+8Ad17txZDz30kCZPnqwzzzwz4LEgBT4OpapG/KKLLtK4ceN0zz33eDV7ABD2DAAgJK655hoTFxfnFRsyZIiRZD766KM6n+tyuczRo0fNypUrjSTz9ddfG2OMcTqdpl27dub00083LpfLM39ubq5p0aKF6dixoyf2j3/8w0gyb775ptey16xZYySZZ5991hhjzBtvvGEkmXXr1gVd43/+8x9z0kknGUlGkklJSTGXXHKJeeedd/yuc/78+cddZmVlpTl06JCJi4szf/rTnzzx+fPnG0nm6quvrvGcxx9/3Egy27dv94rv3LnTREREmNtvv90rXlJSYtLS0syll15qjAluu9ZmyJAhpmfPnubo0aPm6NGjZs+ePeaee+4xkswll1xijDFm3LhxJj4+3uzYscPruU888YSRZHJycowxxmzfvt1IMllZWaaiouK463arrKw0AwcONJJMz549zZEjR4wxgY8FX7WNQ2Oqxrck8/e//z3g/AAgnPDROwAIM61atdLZZ59dI75t2zaNHTtWaWlpstvtatGihYYMGSJJ2rhxoyRp06ZN2rNnj8aOHSvLsjzP7dixowYNGuS1vPfee09JSUm68MILVVlZ6fnp06eP0tLSPB9N69OnjyIjI3XzzTdrwYIF2rZtW8C1jBo1Sjt37tTbb7+tO++8Uz179tS//vUvXXTRRfr9738f0DIOHTqku+++W507d1ZERIQiIiIUHx+v0tJST93V/fa3vw04vyVLlqiyslJXX3211zaIjo7WkCFDPNsgmO1al5ycHLVo0UItWrRQu3btNGfOHF155ZX6y1/+IqlqnwwbNkzt2rXzymfkyJGSpJUrV3ot76KLLgrqe012u13Tpk2TJP3hD3/wfJ8p0LEgBTYOqwtmfwBAOOGjdwAQZvx9p+fQoUM688wzFR0drZkzZ6pr166KjY1VXl6exowZo7KyMknHvheUlpZWYxlpaWnKzc31PN67d6+Ki4sVGRnpN48ffvhBkpSVlaX//ve/euyxx3TbbbeptLRUmZmZmjBhgu64447j1hMTE6OLL75YF198sSRp586dGjlypP785z/r1ltvVc+ePet8/tixY/XRRx/pj3/8o8444wwlJibKsiyNGjXKU3d1tX0nyp+9e/dKkuc7Ur7cH6kLZrvWJSsrS4sWLZJlWYqOjlZGRobXRxf37t2rd999t9bmx71P3IKp1c3dHFXf74GOhUDHoVtsbKwSExODzhEAwgGNEgCEmepXLNyWLVumPXv2aMWKFZ6/3kuqcbvrlJQUSVU3BPDlGzvppJOUkpJS6//PSUhI8Px+5pln6swzz5TT6dSXX36pp59+WhMnTlRqaqouv/zygGuTpA4dOujmm2/WxIkTlZOTU2ejdPDgQb333nuaNm2a7rnnHk+8vLxcBw4c8Pscf9uvNu7vN73xxhvq2LFjrfMFs13rEh0drX79+tWZT+/evfXwww/7nd6uXTuvx8HUWpdAx0Kg47C+8wOAUKBRAoAmwH3C6Xvr5xdeeMHrcbdu3dS2bVv94x//0OTJkz3P27Fjh1atWuV1on3BBRdo0aJFcjqd6t+/f0B52O129e/fX927d9err76qtWvX1toolZSUyLIsxcfH15jm/oiWOx93Xb5XJCzLkjGmRt1//etf5XQ6A8q5ruWfe+65ioiI0NatW+v8iFgw2/WnuOCCC/T+++8rKytLrVq1qpdlBrreQMZCoOMQAJoDGiUAaAIGDRqkVq1a6ZZbbtG0adPUokULvfrqq/r666+95rPZbHrooYd044036je/+Y1uuukmFRcXa/r06TU+Nnb55Zfr1Vdf1ahRo3THHXfol7/8pVq0aKFdu3Zp+fLlGj16tH7zm9/o+eef17Jly3T++eerQ4cOOnLkiP7+979Lkn7961/XmvOmTZt07rnn6vLLL9eQIUPUtm1bFRUV6T//+Y9efPFFDR061PP9nqysLMXExOjVV19Vjx49FB8fr3bt2qldu3Y666yz9Pjjj+ukk05Sp06dtHLlSv3tb39TUlJSwNuvV69ekqQ//elPuuaaa9SiRQt169ZNnTp10oMPPqj77rtP27Zt03nnnadWrVpp7969+uKLLxQXF+e5a1+g2/WnePDBB7V06VINGjRIEyZMULdu3XTkyBHl5ubq/fff1/PPP++5G2B9CnQsBDoOAaBZCPXdJADg56q2u9717NnT7/yrVq0yAwcONLGxsaZ169bmxhtvNGvXrvV7t7i//vWvpkuXLiYyMtJ07drV/P3vfzfXXHNNjbuzHT161DzxxBPmF7/4hYmOjjbx8fGme/fuZty4cWbz5s3GGGM+++wz85vf/MZ07NjRREVFmZSUFDNkyJAad67zVVRUZGbOnGnOPvtsc/LJJ5vIyEgTFxdn+vTpY2bOnGkOHz7sNf8//vEP0717d9OiRQsjyUybNs0YY8yuXbvMb3/7W9OqVSuTkJBgzjvvPPPtt9+ajh07mmuuucbzfPdd79asWeM3n3vvvde0a9fO2Gw2I8ksX77cM+1f//qXGTZsmElMTDRRUVGmY8eO5v/+7//Mf//73xParv7UtW+rKywsNBMmTDAZGRmmRYsWJjk52fTt29fcd9995tChQ8aYY3e9e/zxx4+7PF/Lly83kszrr7/uFQ9kLBgT+Dj0N74BoCmxjDEmhH0aAAAAAIQdbg8OAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfDT7fzjrcrm0Z88eJSQkeP6jOAAAAICfH2OMSkpK1K5dO9lsdV8zavaN0p49e5Senh7qNAAAAACEiby8PLVv377OeZp9o5SQkCCpamMkJiaGOBsAAAAAoeJwOJSenu7pEerS7Bsl98ftEhMTaZQAAAAABPSVHG7mAAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB8RIQ6gZ+bwsJCORyOUKcRsMTERLVu3TrUaQAAAACNikapERUWFmrs2Fu1f395qFMJWEpKlBYufI5mCQAAAD8rNEqNyOFwaP/+ckVFTVFMTHqo0zmusrI87d8/Rw6Hg0YJAAAAPys0SiEQE5OuuLisUKcRkPKmc/ELAAAAqDfczAEAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8BHyRmn37t363e9+p5SUFMXGxqpPnz766quvPNONMZo+fbratWunmJgYDR06VDk5OSHMGAAAAEBzF9JGqaioSIMHD1aLFi30wQcfaMOGDZozZ46SkpI88zz22GOaO3eunnnmGa1Zs0ZpaWkaPny4SkpKQpc4AAAAgGYtIpQrf/TRR5Wenq758+d7Yp06dfL8bozRvHnzdN9992nMmDGSpAULFig1NVULFy7UuHHjaiyzvLxc5eXlnscOh0OS5HQ65XQ6JUmWZclms8nlcskY45nXHXfPd7y4zWaTZVl+45Lkcrm84u512e0u2e3HnuN02iUZ2e2uavNKLpddluWSzWaOG3e5LBljqzVuszllWTpu3Om0SbJktzt/zNPutZ18a6qtVrvdLmOM37jvdq8tHqr9RE3URE3URE3U9HOq6YcfflBJSYksy5IxpkZNlmXVWPaJxCXVyKW2uM1mq5GLO56QkKCUlJTj1trc9hM11U9NvtPrEtJG6Z133tG5556rSy65RCtXrtTJJ5+s8ePH66abbpIkbd++XQUFBRoxYoTnOVFRURoyZIhWrVrlt1GaPXu2ZsyYUSOek5Oj+Ph4SVJycrI6dOigXbt26cCBA5550tLSlJaWptzcXK8rVunp6UpJSdHmzZt15MgRTzwzM1OJiYnasGGD10bv1q2bIiMjtX79eq8c4uLiFB8fqyFD8hURUSqpqklavbqXkpJK1LPnNs+8ZWXRWru2u9q0KVLnznmeeHFxgnJyspSevk/p6QWe+N69ydqypYOysnYpNfVYTXl5adq5M009euQqKelYTVu2pGvv3hT16bNZMTHHasrJyVRxcaLOOGODjClSRcUA5efnq3379n5r6tWrlyoqKrRp0yZPzG63q1evXiopKdG2bcdqio6OVvfu3VVUVKS8vGM1JSQkKCsrS/v27VNBwbGaQrWfqImaqImaqImafi41HTlyRB999Ik2b96jb77Zot69O6tDh1TP/N9/n6fvv9+p/v17qnXrJE/866+3KC9vr4YMOV0JCTGe+Oef56iwsFjnnTdAERF2T3zFimyVlZVr5MgBXjV98MFqxcREaejQ0zyxykqnFi9erdatk9S/f09PvKSkTCtXrtWpp7bX+PHXKDo6+mezn6ip/mo6dOiQAmUZ31avEbkH+OTJk3XJJZfoiy++0MSJE/XCCy/o6quv1qpVqzR48GDt3r1b7dq18zzv5ptv1o4dO7RkyZIay/R3RSk9PV0HDhxQYmKipNB149u3b9ell05SSspcxcVleuLhekWptHSbiovv0sKFj6tz585+a2pOf2GgJmqiJmqiJmr6udW0bds2jR17lyIj71BUVAfZbEaW5XseYXmdo9QVdzotVZ1H+ItLdrsJMG5T1bmRd/zQod2qqJirRYvmKDPz2LlUc99P1FR/NTkcDiUnJ+vgwYOe3qA2Ib2i5HK51K9fP82aNUuSdNpppyknJ0fPPfecrr76as987suybsaYGjG3qKgoRUVF1Yjb7XbZ7XavmHvD+Zu3IeLunJ1O24/NkddUPzHJGJv8XSEMNu5y+c+xtrjTaf8xT6dn4EnBbQPLsvzGa9vuwcYbaj/VFacmaqqvHIONUxM11VeOwcapqXnX5D7hjIrqoLi4LL/zhhOn06byciObzRbweV1z2E+Bxqnp+PHapvvNKeA5G0Dbtm11yimneMV69OihnTt3Sqq65CbJ6/KcJO3bt0+pqakCAAAAgIYQ0kZp8ODBXp91lKTvv/9eHTt2lCRlZGQoLS1NS5cu9UyvqKjQypUrNWjQoEbNFQAAAMDPR0g/ejdp0iQNGjRIs2bN0qWXXqovvvhCL774ol588UVJVZfyJk6cqFmzZqlLly7q0qWLZs2apdjYWI0dOzaUqQMAAABoxkLaKJ1xxhl6++23de+99+rBBx9URkaG5s2bpyuvvNIzz9SpU1VWVqbx48erqKhI/fv314cffqiEhIQQZg4AAACgOQtpoyRJF1xwgS644IJap1uWpenTp2v69OmNlxQAAACAn7WQfkcJAAAAAMIRjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB8RoU4AAAAAaAiFhYVyOByhTiNgiYmJat26dajTwI9olAAAANDsFBYWauzYW7V/f3moUwlYSkqUFi58jmYpTNAoAQAAoNlxOBzav79cUVFTFBOTHup0jqusLE/798+Rw+GgUQoTNEoAAABotmJi0hUXlxXqNAJS3nQufv0scDMHAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB8hbZSmT58uy7K8ftLS0jzTjTGaPn262rVrp5iYGA0dOlQ5OTkhzBgAAADAz0HIryj17NlT+fn5np/169d7pj322GOaO3eunnnmGa1Zs0ZpaWkaPny4SkpKQpgxAAAAgOYuIpCZHA5HwAtMTEwMLoGICK+rSG7GGM2bN0/33XefxowZI0lasGCBUlNTtXDhQo0bNy6o9QAAAABAoAJqlJKSkmRZVkALdDqdQSWwefNmtWvXTlFRUerfv79mzZqlzMxMbd++XQUFBRoxYoRn3qioKA0ZMkSrVq2qtVEqLy9XeXm557G7yXM6nZ7cLMuSzWaTy+WSMcYzrzvuW0NtcZvNJsuy/MYlyeVyecXd67LbXbLbjz3H6bRLMrLbXdXmlVwuuyzLJZvNHDfuclkyxlZr3GZzqvourC3udNokWbLbnT/maffaTr411Var3W6XMcZv3He71xYP1X6iJmqiJmqiJmr6udTkclW917vPHerzPKK6qri8znXqjvs/N6rK0ZLL5fLabv5qdf9usxmvfMKtJvd5XfXzLqfT2ezHXqhqCqZXCahRWr58uef33Nxc3XPPPbr22ms1cOBASdJnn32mBQsWaPbs2QGvWJL69++vl19+WV27dtXevXs1c+ZMDRo0SDk5OSooKJAkpaamej0nNTVVO3bsqHWZs2fP1owZM2rEc3JyFB8fL0lKTk5Whw4dtGvXLh04cMAzT1pamtLS0pSbm+v18b709HSlpKRo8+bNOnLkiCeemZmpxMREbdiwwWujd+vWTZGRkV4fI5SkuLg4xcfHasiQfEVElEqqetGsXt1LSUkl6tlzm2fesrJorV3bXW3aFKlz5zxPvLg4QTk5WUpP36f09AJPfO/eZG3Z0kFZWbuUmnqspry8NO3cmaYePXKVlHSspi1b0rV3b4r69NmsmJhjNeXkZKq4OFFnnLFBxhSpomKA8vPz1b59e7819erVSxUVFdq0aZMnZrfb1atXL5WUlGjbtmM1RUdHq3v37ioqKlJe3rGaEhISlJWVpX379nn2eyj3EzVREzVREzVR08+lJofDoZEjB+iHH4qVl6d6PY+o3lhkZ3dTeXmkBgzwrmn16l6KiqrQaacdq6muc6NPPmmh9u3bKD8/X6WlpXXup8rKSklS167F6tDh2HrDrSb3+V6nTvme8y6bzdbsx16oajp06JACZRnfVu84zjnnHN1444264oorvOILFy7Uiy++qBUrVgSzOC+lpaXKysrS1KlTNWDAAA0ePFh79uxR27ZtPfPcdNNNysvL0+LFi/0uw98VpfT0dB04cMDzscBQdePbt2/XpZdOUkrKXMXFZXri4XpFqbR0m4qL79LChY+rc+fOfmtqTn9hoCZqoiZqoiZq+rnVtG3bNo0de5datnxCMTGdw/6KUklJrg4enKRFi+YoM/PYuZS/Wt3nXcnJTyo+PiNsa3Kf15WVbfWcd2VmZjb7sReqmhwOh5KTk3Xw4MHjfmUooCtK1X322Wd6/vnna8T79eunG2+8MdjFeYmLi1OvXr20efNmXXzxxZKkgoICr0Zp3759Na4yVRcVFaWoqKgacbvdLrvd7hVzbzh/8zZE3P3xRafT9uOLxWuqn5hkjE3+rhAGG3e5/OdYW9zptP+Yp9Mz8KTgtoFlWX7jtW33YOMNtZ/qilMTNdVXjsHGqYma6ivHYOPU1Lxrcp9wulxV7/P1eR7x0+O1nRsZ2Wy2457Xuc9dXC7/ywmvmmxe513u2prz2As0Xt+51zbdb04Bz/mj9PR0v43SCy+8oPT09GAX56W8vFwbN25U27ZtlZGRobS0NC1dutQzvaKiQitXrtSgQYN+0noAAAAAoC5BX1F68skn9dvf/lZLlizRgAEDJEmrV6/W1q1b9eabbwa1rDvvvFMXXnihOnTooH379mnmzJlyOBy65pprZFmWJk6cqFmzZqlLly7q0qWLZs2apdjYWI0dOzbYtAEAAAAgYEE3SqNGjdL333+v5557Tt99952MMRo9erRuueWWoK8o7dq1S1dccYV++OEHtW7dWgMGDNDq1avVsWNHSdLUqVNVVlam8ePHq6ioSP3799eHH36ohISEYNMGAAAAgIAF3ShJVR+/mzVr1k9e+aJFi+qcblmWpk+frunTp//kdQEAAABAoIL+jpIkffLJJ/rd736nQYMGaffu3ZKkV155RZ9++mm9JgcAAAAAoRB0o/Tmm2/q3HPPVUxMjNauXeu5FXdJSUm9XGUCAAAAgFALulGaOXOmnn/+ef3lL39RixYtPPFBgwZp7dq19ZocAAAAAIRC0I3Spk2bdNZZZ9WIJyYmqri4uD5yAgAAAICQCrpRatu2rbZs2VIj/umnn3r9h2QAAAAAaKqCbpTGjRunO+64Q59//rksy9KePXv06quv6s4779T48eMbIkcAAAAAaFRB3x586tSpOnjwoIYNG6YjR47orLPOUlRUlO688079/ve/b4gcAQAAAKBRndD/UXr44Yd13333acOGDXK5XDrllFMUHx9f37kBAAAAQEgE/dG766+/XiUlJYqNjVW/fv30y1/+UvHx8SotLdX111/fEDkCAAAAQKMKulFasGCBysrKasTLysr08ssv10tSAAAAABBKAX/0zuFwyBgjY4xKSkoUHR3tmeZ0OvX++++rTZs2DZIkAAAAADSmgBulpKQkWZYly7LUtWvXGtMty9KMGTPqNTkAAAAACIWAG6Xly5fLGKOzzz5bb775ppKTkz3TIiMj1bFjR7Vr165BkgQAAACAxhRwozRkyBBVVlbq6quvVr9+/ZSent6QeQEAAABAyAR1M4eIiAi9+eabcjqdDZUPAAAAAIRc0He9O+ecc7RixYoGSAUAAAAAwkPQ/3B25MiRuvfee/Xtt9+qb9++iouL85p+0UUX1VtyAAAAABAKQTdKt956qyRp7ty5NaZZlsXH8gAAAAA0eUE3Si6XqyHyAAAAAICwEfR3lAAAAACguQv6ipIklZaWauXKldq5c6cqKiq8pk2YMKFeEgMAAACAUAm6UcrOztaoUaN0+PBhlZaWKjk5WT/88INiY2PVpk0bGiUAAAAATV7QH72bNGmSLrzwQh04cEAxMTFavXq1duzYob59++qJJ55oiBwBAAAAoFEF3SitW7dOU6ZMkd1ul91uV3l5udLT0/XYY4/pD3/4Q0PkCAAAAACNKuhGqUWLFrIsS5KUmpqqnTt3SpJatmzp+R0AAAAAmrKgv6N02mmn6csvv1TXrl01bNgwPfDAA/rhhx/0yiuvqFevXg2RIwAAAAA0qqCvKM2aNUtt27aVJD300ENKSUnRrbfeqn379umFF16o9wQBAAAAoLEFfUWpX79+nt9bt26t999/v14TAgAAAIBQC/qK0tlnn63i4uIacYfDobPPPrs+cgIAAACAkAq6UVqxYkWNfzIrSUeOHNEnn3xSL0kBAAAAQCgF/NG7b775xvP7hg0bVFBQ4HnsdDq1ePFinXzyyfWbHQAAAACEQMCNUp8+fWRZlizL8vsRu5iYGD399NP1mhwAAAAAhELAjdL27dtljFFmZqa++OILtW7d2jMtMjJSbdq0kd1ub5AkAQAAAKAxBdwodezYUZLkcrkaLBkAAAAACAcB38xhy5Yt+uqrr7xiH330kYYNG6Zf/vKXmjVrVr0nBwAAAAChEHCjdNddd+lf//qX5/H27dt14YUXKjIyUgMHDtTs2bM1b968BkgRAAAAABpXwB+9+/LLLzV16lTP41dffVVdu3bVkiVLJEm9e/fW008/rYkTJ9Z7kgAAAADQmAK+ovTDDz+offv2nsfLly/XhRde6Hk8dOhQ5ebm1mtyAAAAABAKATdKycnJys/Pl1R1Q4cvv/xS/fv390yvqKiQMab+MwQAAACARhZwozRkyBA99NBDysvL07x58+RyuTRs2DDP9A0bNqhTp04NkSMAAAAANKqAv6P08MMPa/jw4erUqZNsNpueeuopxcXFeaa/8sorfv8RLQAAAAA0NQE3ShkZGdq4caM2bNig1q1bq127dl7TZ8yY4fUdJgAAAABoqgJulCSpRYsW+sUvfuF3Wm1xAAAAAGhqAv6OEgAAAAD8XNAoAQAAAICPsGmUZs+eLcuyvP5hrTFG06dPV7t27RQTE6OhQ4cqJycndEkCAAAA+FkIi0ZpzZo1evHFF9W7d2+v+GOPPaa5c+fqmWee0Zo1a5SWlqbhw4erpKQkRJkCAAAA+DkIulFavHixPv30U8/jP//5z+rTp4/Gjh2roqKioBM4dOiQrrzySv3lL39Rq1atPHFjjObNm6f77rtPY8aM0amnnqoFCxbo8OHDWrhwYdDrAQAAAIBABXXXO0m666679Oijj0qS1q9frylTpmjy5MlatmyZJk+erPnz5we1vNtuu03nn3++fv3rX2vmzJme+Pbt21VQUKARI0Z4YlFRURoyZIhWrVqlcePG+V1eeXm5ysvLPY8dDockyel0yul0SpIsy5LNZpPL5ZIxxjOvO+6e73hxm80my7L8xiXJ5XJ5xd3rsttdstuPPcfptEsysttd1eaVXC67LMslm80cN+5yWTLGVmvcZnPKsnTcuNNpk2TJbnf+mKfdazv51lRbrXa7XcYYv3Hf7V5bPFT7iZqoiZqoiZqoyTdeWFjoOaewLEuWZckYU6Mmy7JqLPtE4pJq5FJb3Gaz1cglMTFRqampx91PLlfVe7373KE+zyOqq4rL61yn7rj/cyP3dnC5XF5jwd/+c/9usxmvfMKtJvd5XfXzLqfT2axfT6GsyXd6XYJulLZv365TTjlFkvTmm2/qggsu0KxZs7R27VqNGjUqqGUtWrRIa9eu1Zo1a2pMKygokCSlpqZ6xVNTU7Vjx45alzl79mzNmDGjRjwnJ0fx8fGSpOTkZHXo0EG7du3SgQMHPPOkpaUpLS1Nubm5Xh/vS09PV0pKijZv3qwjR4544pmZmUpMTNSGDRu8Nnq3bt0UGRmp9evXe+UQFxen+PhYDRmSr4iIUklVL5rVq3spKalEPXtu88xbVhattWu7q02bInXunOeJFxcnKCcnS+np+5SeXuCJ792brC1bOigra5dSU4/VlJeXpp0709SjR66Sko7VtGVLuvbuTVGfPpsVE3OsppycTBUXJ+qMMzbImCJVVAxQfn6+2rdv77emXr16qaKiQps2bfLE7Ha7evXqpZKSEm3bdqym6Ohode/eXUVFRcrLO1ZTQkKCsrKytG/fPs9+D+V+oiZqoiZqoiZqql7T5s2b9cYb76i8vGq9O3fu1TffbFHv3p3VocOx85Tvv8/T99/vVP/+PdW6dZIn/vXXW5SXt1dDhpyuhIQYT/zzz3NUWFis884boIgIuye+YkW2ysrKNXLkAK+aPvhgtWJiojR06GmeWGWlU4sXr1br1knq37+nJ+50VmjixFtls9nq3E8Oh0MjRw7QDz8UKy9P9XoeUb2xyM7upvLySA0Y4L2fVq/upaioCp122rH9VNe50SeftFD79m2Un5+v0tJSvzUd2zaVkqSuXYvVocOx9YZbTe7zvU6d8j3nXTabrdm+nkJd06FDhxQoy/i2eseRnJysTz/9VKeccop+9atf6eqrr9bNN9+s3NxcnXLKKTp8+HBAy8nLy1O/fv304Ycfev4H09ChQ9WnTx/NmzdPq1at0uDBg7Vnzx61bdvW87ybbrpJeXl5Wrx4sd/l+ruilJ6ergMHDigxMbGq6BB149u3b9ell05SSspcxcVleuLhekWptHSbiovv0sKFj6tz585+a2pOf2GgJmqiJmqiJmryV9OWLVt0xRV3KirqDsXEtJcxllwuSzabkWX5vudaNa4k1BZ3Oi1Vvef6i0t2uwkwblPVeURVvKxsl8rL52nRornKyMiocz9t27ZNY8fepZYtn1BMTOewv6JUUpKrgwcnadGiOcrMPHYu5W//uc+7kpOfVHx8RtjW5D6vKyvb6jnvyszMbLavp1DX5HA4lJycrIMHD3p6g9oEfUXpV7/6lSZPnqzBgwfriy++0GuvvSZJ+v7779W+ffuAl/PVV19p37596tu3ryfmdDr18ccf65lnnvF0rAUFBV6N0r59+2pcZaouKipKUVFRNeJ2u112u90r5t5w/uZtiLj7krnTafvxxeI11U9MMsYmf1cIg427XP5zrC3udNp/zNPpGXhScNvAsiy/8dq2e7DxhtpPdcWpiZrqK8dg49RETfWVY7BxapLn5CsysoOio7P8zhNOnE6bDh+uOik83v5wn3C6XO6P9dXfecRPj9d2bmRks9mOe17nPndxufwvJ7xqsnmdd7lra46vp1DXVNt0vzkFPOePnnnmGUVEROiNN97Qc889p5NPPlmS9MEHH+i8884LeDnnnHOO1q9fr3Xr1nl++vXrpyuvvFLr1q1TZmam0tLStHTpUs9zKioqtHLlSg0aNCjYtAEAAAAgYEFfUerQoYPee++9GvEnn3wyqOUkJCTo1FNP9YrFxcUpJSXFE584caJmzZqlLl26qEuXLpo1a5ZiY2M1duzYYNMGAAAAgIAF3ShJ0tatWzV//nxt3bpVf/rTn9SmTRstXrxY6enp6tmz5/EXEKCpU6eqrKxM48ePV1FRkfr3768PP/xQCQkJ9bYOAAAAAPAV9EfvVq5cqV69eunzzz/XW2+95blzxDfffKNp06b9pGRWrFihefPmeR5blqXp06crPz9fR44c0cqVK2tchQIAAACA+hZ0o3TPPfdo5syZWrp0qSIjIz3xYcOG6bPPPqvX5AAAAAAgFIJulNavX6/f/OY3NeKtW7fW/v376yUpAAAAAAiloBulpKQk5efn14hnZ2d77oAHAAAAAE1Z0I3S2LFjdffdd6ugoECWZcnlcul///uf7rzzTl199dUNkSMAAAAANKqgG6WHH35YHTp00Mknn6xDhw7plFNO0VlnnaVBgwbp/vvvb4gcAQAAAKBRBX178BYtWujVV1/Vgw8+qOzsbLlcLp122mnq0qVLQ+QHAAAAAI3uhP6PkiRlZWUpKyurPnMBAAAAgLAQUKM0efLkgBc4d+7cE04GAAAAAMJBQI1Sdna21+OvvvpKTqdT3bp1kyR9//33stvt6tu3b/1nCAAAAACNLKBGafny5Z7f586dq4SEBC1YsECtWrWSJBUVFem6667TmWee2TBZAgAAAEAjCvqud3PmzNHs2bM9TZIktWrVSjNnztScOXPqNTkAAAAACIWgGyWHw6G9e/fWiO/bt08lJSX1khQAAAAAhFLQjdJvfvMbXXfddXrjjTe0a9cu7dq1S2+88YZuuOEGjRkzpiFyBAAAAIBGFfTtwZ9//nndeeed+t3vfqejR49WLSQiQjfccIMef/zxek8QAAAAABpb0I1SbGysnn32WT3++OPaunWrjDHq3Lmz4uLiGiI/AAAAAGh0J/wPZ+Pi4tS7d+/6zAUAAAAAwsIJNUpr1qzR66+/rp07d6qiosJr2ltvvVUviQEAAABAqAR9M4dFixZp8ODB2rBhg95++20dPXpUGzZs0LJly9SyZcuGyBEAAAAAGlXQjdKsWbP05JNP6r333lNkZKT+9Kc/aePGjbr00kvVoUOHhsgRAAAAABpV0I3S1q1bdf7550uSoqKiVFpaKsuyNGnSJL344ov1niAAAAAANLagG6Xk5GTPP5Y9+eST9e2330qSiouLdfjw4frNDgAAAABCIOibOZx55plaunSpevXqpUsvvVR33HGHli1bpqVLl+qcc85piBwBAAAAoFEF3Sg988wzOnLkiCTp3nvvVYsWLfTpp59qzJgx+uMf/1jvCQIAAABAYwuqUaqsrNS7776rc889V5Jks9k0depUTZ06tUGSAwAAAIBQCOo7ShEREbr11ltVXl7eUPkAAAAAQMgFfTOH/v37Kzs7uyFyAQAAAICwEPR3lMaPH68pU6Zo165d6tu3r+Li4rym9+7du96SAwAAAIBQCLhRuv766zVv3jxddtllkqQJEyZ4plmWJWOMLMuS0+ms/ywBAAAAoBEF3CgtWLBAjzzyiLZv396Q+QAAAABAyAXcKBljJEkdO3ZssGQAAAAAIBwE9R0ly7IaKg8AIVJYWCiHwxHqNAKWmJio1q1bhzoNAADQzAXVKHXt2vW4zdKBAwd+UkIAGk9hYaHGjr1V+/c3nVv+p6REaeHC52iWAABAgwqqUZoxY4ZatmzZULkAaGQOh0P795crKmqKYmLSQ53OcZWV5Wn//jlyOBw0SgAAoEEF1ShdfvnlatOmTUPlAiBEYmLSFReXFeo0AsL/uwYAAI0h4H84y/eTAAAAAPxcBNwoue96BwAAAADNXcAfvXO5XA2ZBwAAAACEjYCvKAEAAADAzwWNEgAAAAD4oFECAAAAAB8BNUqnn366ioqKJEkPPvigDh8+3KBJAQAAAEAoBdQobdy4UaWlpZKq/unsoUOHGjQpAAAAAAilgO5616dPH1133XX61a9+JWOMnnjiCcXHx/ud94EHHqjXBAEAAACgsQXUKL300kuaNm2a3nvvPVmWpQ8++EARETWfalkWjRIAAACAJi+gj95169ZNixYt0po1a2SM0UcffaTs7OwaP2vXrg1q5c8995x69+6txMREJSYmauDAgfrggw88040xmj59utq1a6eYmBgNHTpUOTk5wVUIAAAAAEEK+q53LpdLbdq0qZeVt2/fXo888oi+/PJLffnllzr77LM1evRoTzP02GOPae7cuXrmmWe0Zs0apaWlafjw4SopKamX9QMAAACAPyd0e/CtW7fq9ttv169//WsNHz5cEyZM0NatW4NezoUXXqhRo0apa9eu6tq1qx5++GHFx8dr9erVMsZo3rx5uu+++zRmzBideuqpWrBggQ4fPqyFCxeeSNoAAAAAEJCAvqNU3ZIlS3TRRRepT58+Gjx4sIwxWrVqlXr27Kl3331Xw4cPP6FEnE6nXn/9dZWWlmrgwIHavn27CgoKNGLECM88UVFRGjJkiFatWqVx48b5XU55ebnKy8s9jx0Oh2f5TqdTUtV3qWw2m1wul4wxnnndcfd8x4vbbDZZluU3LlVdfavOvS673SW7/dhznE67JCO73VVtXsnlssuyXLLZzHHjLpclY2y1xm02pyxLx407nTZJlux254952r22k29NtdVqt9tljPEb993utcVDtZ/8xX/44QcdOnRIxpgaudtstoDjlmXJsqxa4765nEhckhISEnTSSScdt1a36mMyHMZedVVxd47HH5PNbexREzVRU+hrMsbIbrd7jpWNedwLLO59HlGV57H669pPLlfVcdVdR7jWJFW9D1XlWPX+V30s+Nt/7t9tNuOVT7jV5H5vrf4e53Q6m+3rKdQ1+U6vS9CN0j333KNJkybpkUceqRG/++67g26U1q9fr4EDB+rIkSOKj4/X22+/rVNOOUWrVq2SJKWmpnrNn5qaqh07dtS6vNmzZ2vGjBk14jk5OZ479SUnJ6tDhw7atWuXDhw44JknLS1NaWlpys3N9fp4X3p6ulJSUrR582YdOXLEE8/MzFRiYqI2bNjgtdG7deumyMhIrV+/3iuHuLg4xcfHasiQfEVEVN1u3em0a/XqXkpKKlHPnts885aVRWvt2u5q06ZInTvneeLFxQnKyclSevo+pacXeOJ79yZry5YOysrapdTUYzXl5aVp58409eiRq6SkYzVt2ZKuvXtT1KfPZsXEHKspJydTxcWJOuOMDTKmSBUVA5Sfn6/27dv7ralXr16qqKjQpk2bPDG73a5evXqppKRE27Ydqyk6Olrdu3dXUVGR8vKO1ZSQkKCsrCzt27dPBQXHagrVfvKt6ciRI/rwwxX617/+p9atk9S/f0/PvCUlZVq5cq3S01P1i1909sQLC4v1+ec56tq1g7p2TffEd+7cq2++2aLevTurQ4djY/v77/P0/fc71b9/T7VuneSJf/31FuXl7dWQIacrISHGE//88xwVFhbrvPMGKCLC7omvWJGtsrJyXXzxYJ1zzpmKjo6ucz/FxsaqdesknXnmsTEZDmOv+ptRdnY3lZdHasCA9aqsdHjGZGZmZrMfe9RETdQUHjWVlJRo5MgBioysOlY25nGvutWreykqqkKnnXasJn/nEZWVDu3f30eSjrufHA6HRo4coB9+KFZensK2Jqnq/emTT1qoffs2ys/P9/zrmtrGXmVlpSSpa9didehwbL3hVpP7PbdTp3zPe5zNZmu2r6dQ1xTMvzmyjG+rdxzR0dFav369unTp4hX//vvv1bt3b6+EA1FRUaGdO3equLhYb775pv76179q5cqVKi4u1uDBg7Vnzx61bdvWM/9NN92kvLw8LV682O/y/F1RSk9P14EDB5SYmCgpdN349u3bdemlk5SSMldxcZmeeLheUSot3abi4ru0cOHj6ty5s9+amtNfGGqLb9u2TWPH3qWIiImKiWkvu907d6fTJssyXtu9trgxllwuSzabkWX57ierxl+faos7nZaq9lPNeFlZnior/6SFCx9XZmZmnbXm5ubqkksmeo3JcBh7vttRqvrLXCBjsjmNPWqiJmoKj5q2bNmiK664U0lJjysuLjPsryhVHSvv1KJFc5WRkVHnfnK/x7Vs+YRiYjqHbU1S1ftQSUmuDh6cpEWL5nje43xrcnOfdyUnP6n4+Iywrcn93lpWttXzHpeZmdlsX0+hrsnhcCg5OVkHDx709Aa1CfqKUuvWrbVu3boajdK6detO6CYPkZGRnhOefv36ac2aNfrTn/6ku+++W5JUUFDg1Sjt27evxlWm6qKiohQVFVUjXnXJ3O4Vc284f/M2RNz9sSin0/bji8Vrqp+YZIxN/q4QBht3ufznWFvc6bT/mKfTM/Ck4LaBZVl+47Vt92DjDbWffOPuF2NCQrri4rL8zhteLBUXV+0339pqq9XfmAzl2KstHuiYbC5jrzpqoqb6yjHYODXJc/Lle6xsjONe4PFj5xFVeVadFB5vf7jf41yuqmNquNZUnTHG73ucb63u9wmXy/9ywqsmm9d7XPVzEH+a8usp1DXVNt2foBulm266STfffLO2bdumQYMGybIsffrpp3r00Uc1ZcqUYBdXgzFG5eXlysjIUFpampYuXarTTjtNUtXVp5UrV+rRRx/9yesBAAAAgNoE3Sj98Y9/VEJCgubMmaN7771XktSuXTtNnz5dEyZMCGpZf/jDHzRy5Eilp6erpKREixYt0ooVK7R48WJZlqWJEydq1qxZ6tKli7p06aJZs2YpNjZWY8eODTZtAAAAAAhY0I2SZVmaNGmSJk2a5PliVUJCwgmtfO/evbrqqquUn5+vli1bqnfv3lq8eLHnhhBTp05VWVmZxo8fr6KiIvXv318ffvjhCa8PAAAAAAIRdKNU3U9tWP72t7/VOd2yLE2fPl3Tp0//SesBAAAAgGCc0D+cBQAAAIDmjEYJAAAAAHzQKAEAAACAj6AapaNHj2rYsGH6/vvvGyofAAAAAAi5oBqlFi1a6Ntvv/X8Ay8AAAAAaI6C/ujd1Vdffdy71QEAAABAUxb07cErKir017/+VUuXLlW/fv0UFxfnNX3u3Ln1lhwAAAAAhELQjdK3336r008/XZJqfFeJj+QBAAAAaA6CbpSWL1/eEHkAAAAAQNg44duDb9myRUuWLFFZWZkkyRhTb0kBAAAAQCgF3Sjt379f55xzjrp27apRo0YpPz9fknTjjTdqypQp9Z4gAAAAADS2oBulSZMmqUWLFtq5c6diY2M98csuu0yLFy+u1+QAAAAAIBSC/o7Shx9+qCVLlqh9+/Ze8S5dumjHjh31lhgAAAAAhErQV5RKS0u9riS5/fDDD4qKiqqXpAAAAAAglIJulM466yy9/PLLnseWZcnlcunxxx/XsGHD6jU5AAAAAAiFoD969/jjj2vo0KH68ssvVVFRoalTpyonJ0cHDhzQ//73v4bIEQAAAAAaVdBXlE455RR98803+uUvf6nhw4ertLRUY8aMUXZ2trKyshoiRwAAAABoVEFfUZKktLQ0zZgxo75zAQAAAICwcEKNUlFRkf72t79p48aNsixLPXr00HXXXafk5OT6zg8AAAAAGl3QH71buXKlMjIy9NRTT6moqEgHDhzQU089pYyMDK1cubIhcgQAAACARhX0FaXbbrtNl156qZ577jnZ7XZJktPp1Pjx43Xbbbfp22+/rfckAQAAAKAxBX1FaevWrZoyZYqnSZIku92uyZMna+vWrfWaHAAAAACEQtCN0umnn66NGzfWiG/cuFF9+vSpj5wAAAAAIKQC+ujdN9984/l9woQJuuOOO7RlyxYNGDBAkrR69Wr9+c9/1iOPPNIwWQIAAABAIwqoUerTp48sy5IxxhObOnVqjfnGjh2ryy67rP6yAwAAAIAQCKhR2r59e0PnAQAAAABhI6BGqWPHjg2dBwAAAACEjRP6h7O7d+/W//73P+3bt08ul8tr2oQJE+olMQAAAAAIlaAbpfnz5+uWW25RZGSkUlJSZFmWZ5plWTRKAAAAAJq8oBulBx54QA888IDuvfde2WxB310cAAAAAMJe0J3O4cOHdfnll9MkAQAAAGi2gu52brjhBr3++usNkQsAAAAAhIWgP3o3e/ZsXXDBBVq8eLF69eqlFi1aeE2fO3duvSUHAAAAAKEQdKM0a9YsLVmyRN26dZOkGjdzAAAAAICmLuhGae7cufr73/+ua6+9tgHSAQAAAIDQC/o7SlFRURo8eHBD5AIAAAAAYSHoRumOO+7Q008/3RC5AAAAAEBYCPqjd1988YWWLVum9957Tz179qxxM4e33nqr3pIDgJ+isLBQDocj1GkELDExUa1btw51GsAJ4zUHoDkJulFKSkrSmDFjGiIXAKg3hYWFGjv2Vu3fXx7qVAKWkhKlhQuf48QNTRKvOQDNTdCN0vz58xsiDwCoVw6HQ/v3lysqaopiYtJDnc5xlZXlaf/+OXI4HJy0oUniNQeguQm6UQKApiQmJl1xcVmhTiMg5U3nD/FArXjNAWgugm6UMjIy6vx/Sdu2bftJCQEAAABAqAXdKE2cONHr8dGjR5Wdna3Fixfrrrvuqq+80ATxJV4AAAA0F0E3SnfccYff+J///Gd9+eWXPzkhNE18iRcAAADNSb19R2nkyJG69957udnDzxRf4gUAAEBzUm+N0htvvKHk5OSgnjN79my99dZb+u677xQTE6NBgwbp0UcfVbdu3TzzGGM0Y8YMvfjiiyoqKlL//v315z//WT179qyv1FGP+BIvAAAAmoOgG6XTTjvN62YOxhgVFBSosLBQzz77bFDLWrlypW677TadccYZqqys1H333acRI0Zow4YNiouLkyQ99thjmjt3rl566SV17dpVM2fO1PDhw7Vp0yYlJCQEmz4AAAAAHFfQjdLFF1/s9dhms6l169YaOnSounfvHtSyFi9e7PV4/vz5atOmjb766iudddZZMsZo3rx5uu+++zz/5HbBggVKTU3VwoULNW7cuGDTBwAAAIDjCrpRmjZtWkPkIUk6ePCgJHk+wrd9+3YVFBRoxIgRnnmioqI0ZMgQrVq1ym+jVF5ervJqn6ly34XN6XTK6XRKkizLks1mk8vlkjHGM6877p7veHGbzSbLsvzGJcnlcnnF3euy212y2489x+m0SzKy213V5pVcLrssyyWbzRw37nJZMsZWa9xmc6r6Xd1rizudNkmW7Hbnj3navbaTb03Va7Xb7Z7awrUmN3dexpjj7j93bT9mG7Y1HYsbz35z11bbmKy+PdzLCs+a3Dkef0za7XYZY7zGpM3mDNuaqu+DqpxqjsnqNfnGfY9jtcVDddyrLU5Nza8m9zyWZbzGfCheT3XHj70/VT9W1rWfjDFe73HhXJN7ut1+bJ/WNfbcx0p3HeFak1R1zK7K0fJ6j/Ot6dj8Vb/bbP7HZLjU5H4fCmRMNuVjRG3xxq7Jd3pdwuYfzhpjNHnyZP3qV7/SqaeeKkkqKCiQJKWmpnrNm5qaqh07dvhdzuzZszVjxowa8ZycHMXHx0uqasQ6dOigXbt26cCBA5550tLSlJaWptzcXJWUlHji6enpSklJ0ebNm3XkyBFPPDMzU4mJidqwYYPXRu/WrZsiIyO1fv16rxzi4uIUHx+rIUPyFRFRKqnqRbN6dS8lJZWoZ89j/4OqrCxaa9d2V5s2RercOc8TLy5OUE5OltLT9yk9vcAT37s3WVu2dFBW1i6lph6rKS8vTTt3pqlHj1wlJR2racuWdO3dm6I+fTYrJuZYTTk5mSouTtQZZ2yQMUWqqBig/Px8tW/f3m9NvXr1UkVFhfLz8zVy5ABFRubLso6EbU3ug1xlpUMffhgrY0ytNW3atElSVbM9fPgZWr1aYV2TJGVnd9ORI0YjR1btt9LSUr81SVUHq9jYWLVunaQzzzw2JsOxpvLySA0YsF6VlQ7PmMzMzPRbU69evVRSUuI1Jo8edYZtTW6VlQ79+992VVZWeo3J6jVV/z910dHR6t69u4qKipSXd6ymhIQEZWVlad++fZ5jqBS6415tY4+aml9N7j9SdupUoi5dji0/FK8nSVq9upeioip02mnHaqr+ntutW77neOJ0OuvcTyUlJZ7jSUREadjW5H5/qqx0aP/+PpJ03LHncDg0cuQA/fBDsfLyFLY1SVXvT5980kLt27fxeo+r7fVUWVkpSeratVgdOhxbb7jV5H5/6tTp2Ji02WzN7hgRLse9Q4cOKVCW8W31auHu0upcmGV5BmWwbrvtNv3nP//Rp59+qvbt20uSVq1apcGDB2vPnj1q27atZ96bbrpJeXl5NT66J/m/opSenq4DBw4oMTHRk2couvHt27fr0ksnKSVlruLiMj3xcL36Ulq6TcXFd2nhwsfVuXNnvzW5a92yZYvGjr1LSUmPKy4uM2xrcist3ab9+yfrn/98UhkZGX5rcte6bds2jR17lxIS5iguLjNsa3LHS0u3qqTkTi1c+LgyMzP91uSWm5urSy6Z6DUmw7Emqeovc4GMSfdft6qPydjYzLCtya2uMdkU/2J3vDg1Nb+atm3bpssum6xWrZ5UQsKxMRyuV1+OHNniOZ5kZmbWuZ+2bNmiK6640/MeF641ueNVx8o7tWjRXGVkZNQ59tzvcS1bPqGYmM5hW5NUdcwuKcnVwYOTtGjRHM97nG9Nbu7zruTkJxUfX3NMhktN7vehsrKtxx2TTfkYUVu8sWtyOBxKTk7WwYMHPb1BbQK+ovT222/XOm3VqlV6+umna2yMQN1+++1655139PHHH3uaJKmqk5SqrixVb5T27dtX4yqTW1RUlKKiomrEqy6Z271i7g3nb96GiLsbTafT9uOLxWuqn5hkjE3+rhAGG3e5/OdYW9zptP+Yp9OrSa6tVveA9a4t/Go69nvVvrcs67j7z/vFGL41HWN59ptvbbXV6m9MhldNwY3J6gdRp9PmWVc41nTs97rHZG3x2o5jwcYb6rhXV5yamldN7sfGBHecbIjX0/HjltfxpPrx3h/3yZfvsTL8arL/ON0mp7PqpPB4Y8x9rHS5qo6p4VpTdcYYv+9xvrW63ydcLv/LCa+abEGNyaZ4jDjReH3nXtt0fwJulEaPHl0j9t133+nee+/Vu+++qyuvvFIPPfRQwCuWqgb67bffrrffflsrVqyo8VfUjIwMpaWlaenSpTrttNMkSRUVFVq5cqUeffTRoNYFAAAAAIE6oe8o7dmzR9OmTdOCBQt07rnnat26dZ7vFQXjtttu08KFC/Xvf/9bCQkJns8rtmzZUjExMbIsSxMnTtSsWbPUpUsXdenSRbNmzVJsbKzGjh17IqkDAJqAwsJCz814moLExET+eTUANDNBNUoHDx7UrFmz9PTTT6tPnz766KOPdOaZZ57wyp977jlJ0tChQ73i8+fP17XXXitJmjp1qsrKyjR+/HjPP5z98MMP+R9KANBMFRYWauzYW7V/f9P5r9ApKVFauPA5miUAaEYCbpQee+wxPfroo0pLS9M//vEPvx/FC1Yg32myLEvTp0/X9OnTf/L6AADhz+FwaP/+ckVFTVFMTHqo0zmusrI87d8/Rw6Hg0YJAJqRgBule+65RzExMercubMWLFigBQsW+J3vrbfeqrfkAAA/XzEx6YqLywp1GgEpbzoXvwAAAQq4Ubr66quPe3twAAAAAGgOAm6UXnrppQZMAwAAAADCh/8blgMAAADAzxiNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD4iQp0AAAA/J4WFhXI4HKFOI2CJiYlq3bp1qNMAgEZHowQAQCMpLCzU2LG3av/+8lCnErCUlCgtXPgczRKAnx0aJQAAGonD4dD+/eWKipqimJj0UKdzXGVledq/f44cDgeNEoCfHRolAAAaWUxMuuLiskKdRkDKm87FLwCoV9zMAQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMBHSBuljz/+WBdeeKHatWsny7L0r3/9y2u6MUbTp09Xu3btFBMTo6FDhyonJyc0yQIAAAD42Qhpo1RaWqpf/OIXeuaZZ/xOf+yxxzR37lw988wzWrNmjdLS0jR8+HCVlJQ0cqYAAAAAfk4iQrnykSNHauTIkX6nGWM0b9483XfffRozZowkacGCBUpNTdXChQs1bty4xkwVAAAAwM9ISBulumzfvl0FBQUaMWKEJxYVFaUhQ4Zo1apVtTZK5eXlKi8v9zx2OBySJKfTKafTKUmyLEs2m00ul0vGGM+87rh7vuPFbTabLMvyG5ckl8vlFXevy253yW4/9hyn0y7JyG53VZtXcrnssiyXbDZz3LjLZckYW61xm80py9Jx406nTZIlu935Y552r+3kW1P1Wu12u6e2cK3JzZ2XMea4+89d24/Zhm1Nx+LGs9/ctdU2JqtvD/eywrMmd47HH5N2u13GGK8xabM5w7am6vugKqeaY7J6Tb5x3+NYbfFQHfdqi9dVkzHG63gSTvvJX9w9LiUddz9VH5eSwrYmX9WPJ741VZ+nqibjtd5wq6n6+1P1Y2VdryffMRnONbmn2+3HXnt1HSPcY9JdR7jWJFUds6tytAIak+7fbTb/YzJcanIf3wIZk9WP5YWFhZ7zXMuyPNuluhOJV992x4vbbDYZYwKKJyYmKjU1NeTvT77T6xK2jVJBQYEkKTU11SuempqqHTt21Pq82bNna8aMGTXiOTk5io+PlyQlJyerQ4cO2rVrlw4cOOCZJy0tTWlpacrNzfX6eF96erpSUlK0efNmHTlyxBPPzMxUYmKiNmzY4LXRu3XrpsjISK1fv94rh7i4OMXHx2rIkHxFRJRKqnrRrF7dS0lJJerZc5tn3rKyaK1d211t2hSpc+c8T7y4OEE5OVlKT9+n9PQCT3zv3mRt2dJBWVm7lJp6rKa8vDTt3JmmHj1ylZR0rKYtW9K1d2+K+vTZrJiYYzXl5GSquDhRZ5yxQcYUqaJigPLz89W+fXu/NfXq1UsVFRXKz8/XyJEDFBmZL8s6ErY1uQ9ylZUOffhhrIwxtda0adMmSVXN9vDhZ2j1aoV1TZKUnd1NR44YjRxZtd9KS0v91iRVvanExsaqdesknXnmsTEZjjWVl0dqwID1qqx0eMZkZmam35p69eqlkpISrzF59KgzbGtyq6x06N//tquystJrTFavadu2Y2MvOjpa3bt3V1FRkfLyjtWUkJCgrKws7du3z3MclUJ33Ktt7NVV0+HDhz37LiKiNKz2kyStXt1LUVEVOu20qpoqKx06fPgMSTrufnI4HBo5coAOHTqgzZsVtjVJVe9PH31UdYyofjypbey5/0jZqVOJunQ5tvxwq8n9/tStW77neOJ0Out8PZWUlHiNyXCtyf3+VFnp0P79fSTpuMcI95j84Ydi5eUpbGuSqt6fPvmkhdq3b+M1Jms77lVWVkqSunYtVocOx9YbbjW53586dTo2Jm02W53H8o0bN+pf/3pf5eVV+Xz/fZ6+/36n+vfvqdatkzzzf/31FuXl7dWQIacrISHGE//88xwVFhbrvPMGKCLC7omvWJGtsrJyjRw5wKumDz5YrZiYKA0delq17evU4sWr1bp1kvr37+mJl5SUaeXKtUpPT9UvftHZEy8tPaR77rlDTqczpO9Phw4dUqAs49sChohlWXr77bd18cUXS5JWrVqlwYMHa8+ePWrbtq1nvptuukl5eXlavHix3+X4u6KUnp6uAwcOKDEx0bOuUPxldfv27br00klKSZmruLhMTzxcr76Ulm5TcfFdWrjwcXXu3NlvTe5at2zZorFj71JS0uOKi8sM25rcSku3af/+yfrnP59URkaG35rctW7btk1jx96lhIQ5iovLDNua3PHS0q0qKblTCxc+rszMTL81ueXm5uqSSyZ6jclwrEmq+stcIGPS/dff6mMyNjYzbGtyq2tM/tyuKG3ZskVXXHGn53gSTvvJX9w9LhctmqPMzMw695P7eJKU9Liio7uEbU1uDkeuiosn6rXX5nqOJ741uW3btk2XXTZZrVo9qYSEY2M43Gpyvz8dObLFczzJzMys8/XkOybDtSbvMXmnFi2aq4yMjDqPEe4x2bLlE4qJ6Ry2NUlVx+ySklwdPDjJ83rzV5Ob+7wrOflJxcfXHJPhUpP7+FZWtvW4Y9J9bHaPyaioOxQT0/7H3K0a66wt7nRaP9bkLy7Z7SbAuPuTLDXjlmU8Y6msbJfKy+f5HZON/f7kcDiUnJysgwcPenqD2oTtFaW0tDRJVVeWqjdK+/btq3GVqbqoqChFRUXViFddMrd7xdwbzt+8DRF3X7Z0Om0/vli8pvqJScbY5O8KYbBxl8t/jrXFnU77j3k6PQNPqr1W94D1ri38ajr2e9W+tyzruPvP+8UYvjUdY3n2m29ttdXqb0yGV03BjcnqB1Gn0+ZZVzjWdOz3usdkbfHajmPBxhvquFdXvK5aax5PwmM/+Yu7x6U797r2U/VxWVfuoa7Jl7/jie9Ycj82JrjjZGhqsryOJ9WP9/4EOyZDV1P1MVl1Uni8Y4F7TLpc7o9WhWdN1RljAhqT7vcJl8v/csKrJtsJjcnIyA6Kjs7yO084cTptOnw4sDHpq77fh2qb7jengOdsZBkZGUpLS9PSpUs9sYqKCq1cuVKDBg0KYWYAAAAAmruQXlE6dOiQtmzZ4nm8fft2rVu3zvNZxYkTJ2rWrFnq0qWLunTpolmzZik2NlZjx44NYdYAAAAAmruQNkpffvmlhg0b5nk8efJkSdI111yjl156SVOnTlVZWZnGjx+voqIi9e/fXx9++KESEhJClTIAAACAn4GQNkpDhw6t8QXD6izL0vTp0zV9+vTGSwoAAADAz17YfkcJAAAAAEKFRgkAAAAAfNAoAQAAAICPsP0/SgCA2hUWFsrhcIQ6jaAkJiaqdevWoU4DAICA0CgBQBNTWFiosWNv1f795aFOJSgpKVFauPA5miUAQJNAowQATYzD4dD+/eWKipqimJj0UKcTkLKyPO3fP0cOh4NGCQDQJNAoAUATFROTrri4rFCnEbDypnUBDADwM8fNHAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4INGCQAAAAB80CgBAAAAgA8aJQAAAADwQaMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACAjybRKD377LPKyMhQdHS0+vbtq08++STUKQEAAABoxsK+UXrttdc0ceJE3XfffcrOztaZZ56pkSNHaufOnaFODQAAAEAzFfaN0ty5c3XDDTfoxhtvVI8ePTRv3jylp6frueeeC3VqAAAAAJqpiFAnUJeKigp99dVXuueee7ziI0aM0KpVq/w+p7y8XOXl5Z7HBw8elCQVFRXJ6XRKkizLks1mk8vlkjHGM6877p7veHGbzSbLsvzGJcnlcnnFHQ6HnM6jOnx4o4xxeOJOpyXJyG73rsXptGRZRjbb8ePGSC6XJZvNyLKOxV0uyRhLdrvxWkZt8apSquJlZbslueRwODzb0bcmd60Oh0OSy1NbuNbkVla2W07nUTkcDhUVFfmtyV2ru7aSku9UWekI25rc8er7zV1bbWOypKSk1jEZTjVJkt2ugMak3W6XMcbvmAzHmtzqGpPumrzHpFMlJd/J6XSEbU3V4xUV3uPSt6bqtfruu3CtyR13j8uSkhIdPHjQb03u95vqtVVWloRtTW6HDu3xOy6r1+Tmfo87dMj7eBJuNbnfn/yNSd+a3LX6jslwrcl7TDpVUlKi4uJivzX5jsnS0qoxGa41uR06tEcuV2VQY7K01HtMhltN7uNbefnxx6T7nNR3TIZrTe7jW11jsrHPy6u2nWq8LvwyYWz37t1Gkvnf//7nFX/44YdN165d/T5n2rRpRhI//PDDDz/88MMPP/zww4/fn7y8vOP2ImF9RcnNqv6nDUnGmBoxt3vvvVeTJ0/2PHa5XDpw4IBSUlJqfU5T53A4lJ6erry8PCUmJoY6nXpFbU0TtTVNzbk2qXnXR21NE7U1TdTWtBljVFJSonbt2h133rBulE466STZ7XYVFBR4xfft26fU1FS/z4mKilJUVJRXLCkpqaFSDCuJiYnNdlBTW9NEbU1Tc65Nat71UVvTRG1NE7U1XS1btgxovrC+mUNkZKT69u2rpUuXesWXLl2qQYMGhSgrAAAAAM1dWF9RkqTJkyfrqquuUr9+/TRw4EC9+OKL2rlzp2655ZZQpwYAAACgmQr7Rumyyy7T/v379eCDDyo/P1+nnnqq3n//fXXs2DHUqYWNqKgoTZs2rcZHDpsDamuaqK1pas61Sc27PmprmqitaaK2nw/LmEDujQcAAAAAPx9h/R0lAAAAAAgFGiUAAAAA8EGjBAAAAAA+aJQAAAAAwAeNUpiYPXu2zjjjDCUkJKhNmza6+OKLtWnTJq95jDGaPn262rVrp5iYGA0dOlQ5OTle87z44osaOnSoEhMTZVmWiouLa6zroosuUocOHRQdHa22bdvqqquu0p49e5pFbW7l5eXq06ePLMvSunXrGqCqKo1ZW6dOnWRZltfPPffc0yxqk6T//Oc/6t+/v2JiYnTSSSdpzJgxDVVao9W2YsWKGvvM/bNmzZomXZskff/99xo9erROOukkJSYmavDgwVq+fHmD1NXYta1du1bDhw9XUlKSUlJSdPPNN+vQoUNhXduBAwd0++23q1u3boqNjVWHDh00YcIEHTx40Gs5RUVFuuqqq9SyZUu1bNlSV111VZ3H06ZU28MPP6xBgwYpNja2Uf7ZfGPVlpubqxtuuEEZGRmKiYlRVlaWpk2bpoqKiiZfm9Q0z0sCrc2tKZ2XBFpbY5+XhIRBWDj33HPN/PnzzbfffmvWrVtnzj//fNOhQwdz6NAhzzyPPPKISUhIMG+++aZZv369ueyyy0zbtm2Nw+HwzPPkk0+a2bNnm9mzZxtJpqioqMa65s6daz777DOTm5tr/ve//5mBAweagQMHNova3CZMmGBGjhxpJJns7OxmUVvHjh3Ngw8+aPLz8z0/JSUlzaK2N954w7Rq1co899xzZtOmTea7774zr7/+epOvrby83Gt/5efnmxtvvNF06tTJuFyuJl2bMcZ07tzZjBo1ynz99dfm+++/N+PHjzexsbEmPz+/Sde2e/du06pVK3PLLbeY7777znzxxRdm0KBB5re//W2D1FVfta1fv96MGTPGvPPOO2bLli3mo48+Ml26dKmR93nnnWdOPfVUs2rVKrNq1Spz6qmnmgsuuKBZ1PbAAw+YuXPnmsmTJ5uWLVs2WE2NXdsHH3xgrr32WrNkyRKzdetW8+9//9u0adPGTJkypcnXZkzTPC8JtDa3pnReEmhtjX1eEgo0SmFq3759RpJZuXKlMcYYl8tl0tLSzCOPPOKZ58iRI6Zly5bm+eefr/H85cuXH7eZcPv3v/9tLMsyFRUV9ZZ/XRq6tvfff990797d5OTkNPgByVdD1taxY0fz5JNPNlTqx9VQtR09etScfPLJ5q9//WuD5l+Xxnq9VVRUmDZt2pgHH3ywXvOvS0PVVlhYaCSZjz/+2BNzOBxGkvnvf//bMMX4aKjaXnjhBdOmTRvjdDo9sezsbCPJbN68uWGK8fFTa3P75z//aSIjI83Ro0eNMcZs2LDBSDKrV6/2zPPZZ58ZSea7775roGq8NVRt1c2fP79RGiVfjVGb22OPPWYyMjLqL/njaMzamtp5iVtttTXl8xI3f7WF+rykMfDRuzDlvryZnJwsSdq+fbsKCgo0YsQIzzxRUVEaMmSIVq1adcLrOXDggF599VUNGjRILVq0+GlJB6gha9u7d69uuukmvfLKK4qNja2/pAPU0Pvt0UcfVUpKivr06aOHH364QT924auhalu7dq12794tm82m0047TW3bttXIkSNrfFyqITXW6+2dd97RDz/8oGuvvfYn5RuMhqotJSVFPXr00Msvv6zS0lJVVlbqhRdeUGpqqvr27Vu/RdSioWorLy9XZGSkbLZjb5ExMTGSpE8//bQ+Uj+u+qrt4MGDSkxMVERE1f+X/+yzz9SyZUv179/fM8+AAQPUsmXLnzS2g9FQtYWDxqzt4MGDnvU0hsaqrSmfl/irrbmcl9S230J5XtIYaJTCkDFGkydP1q9+9SudeuqpkqSCggJJUmpqqte8qampnmnBuPvuuxUXF6eUlBTt3LlT//73v3964gFoyNqMMbr22mt1yy23qF+/fvWXdBDrb8j9dscdd2jRokVavny5fv/732vevHkaP358/SR/HA1Z27Zt2yRJ06dP1/3336/33ntPrVq10pAhQ3TgwIF6qqB2jfF6c/vb3/6mc889V+np6SeecBAasjbLsrR06VJlZ2crISFB0dHRevLJJ7V48eJG+W5IQ9Z29tlnq6CgQI8//rgqKipUVFSkP/zhD5Kk/Pz8eqqgdvVV2/79+/XQQw9p3LhxnlhBQYHatGlTY942bdr8pLEdqIasLdQas7atW7fq6aef1i233FJP2detMWpr6ucl/mprLuclte23UJ6XNBYapTD0+9//Xt98843+8Y9/1JhmWZbXY2NMjVgg7rrrLmVnZ+vDDz+U3W7X1VdfLWPMCeccqIas7emnn5bD4dC99977k/M8EQ293yZNmqQhQ4aod+/euvHGG/X888/rb3/7m/bv3/+T8g5EQ9bmcrkkSffdd59++9vfqm/fvpo/f74sy9Lrr7/+0xIPQGO83iRp165dWrJkiW644YYTev6JaMjajDEaP3682rRpo08++URffPGFRo8erQsuuKBRmomGrK1nz55asGCB5syZo9jYWKWlpSkzM1Opqamy2+0/OffjqY/aHA6Hzj//fJ1yyimaNm1ancuoazn1raFrC6XGqm3Pnj0677zzdMkll+jGG2+sn+SPozFqa8rnJbXV1hzOS+rab6E8L2ksNEph5vbbb9c777yj5cuXq3379p54WlqaJNXo9vft21fjrwKBOOmkk9S1a1cNHz5cixYt0vvvv6/Vq1f/tOSPo6FrW7ZsmVavXq2oqChFRESoc+fOkqR+/frpmmuuqYcKatdY+626AQMGSJK2bNnyk5ZzPA1dW9u2bSVJp5xyiicWFRWlzMxM7dy586ekflyNud/mz5+vlJQUXXTRRSeecBAa4/X23nvvadGiRRo8eLBOP/10Pfvss4qJidGCBQvqp4haNMZ+Gzt2rAoKCrR7927t379f06dPV2FhoTIyMn56AXWoj9pKSkp03nnnKT4+Xm+//bbXx5fS0tK0d+/eGustLCz8ycek42no2kKpsWrbs2ePhg0bpoEDB+rFF19sgEpqaqzamup5SV21NfXzkmBfb411XtKYaJTChDFGv//97/XWW29p2bJlNd6MMzIylJaWpqVLl3piFRUVWrlypQYNGvST1y1VfS6/ITRWbU899ZS+/vprrVu3TuvWrdP7778vSXrttdf08MMP108xPkK537KzsyUdazTqW2PV1rdvX0VFRXndvvTo0aPKzc1Vx44df3ohfjT2fjPGaP78+br66qsb/MSusWo7fPiwJHl9j8f92H2VsL6F4vWWmpqq+Ph4vfbaa4qOjtbw4cN/Ug21qa/aHA6HRowYocjISL3zzjuKjo72Ws7AgQN18OBBffHFF57Y559/roMHD/7kY1KoawuFxqxt9+7dGjp0qE4//XTNnz+/xmuvvoVyvzWV85Lj1daUz0tOZL819HlJSNT//SFwIm699VbTsmVLs2LFCq/bLB4+fNgzzyOPPGJatmxp3nrrLbN+/XpzxRVX1LjtbX5+vsnOzjZ/+ctfPHekys7ONvv37zfGGPP555+bp59+2mRnZ5vc3FyzbNky86tf/cpkZWWZI0eONOnafG3fvr3B7y7TWLWtWrXKzJ0712RnZ5tt27aZ1157zbRr185cdNFFTb42Y4y54447zMknn2yWLFlivvvuO3PDDTeYNm3amAMHDjT52owx5r///a+RZDZs2NAg9YSitsLCQpOSkmLGjBlj1q1bZzZt2mTuvPNO06JFC7Nu3bomXZsxxjz99NPmq6++Mps2bTLPPPOMiYmJMX/6058apK76qs3hcJj+/fubXr16mS1btngtp7Ky0rOc8847z/Tu3dt89tln5rPPPjO9evVq0NuDN2ZtO3bsMNnZ2WbGjBkmPj7eZGdnm+zs7Aa7ZXFj1bZ7927TuXNnc/bZZ5tdu3Z5zdNQGqu2pnpeEuiYrK6pnJcEUlsozktCgUYpTEjy+zN//nzPPC6Xy0ybNs2kpaWZqKgoc9ZZZ5n169d7LWfatGl1Luebb74xw4YNM8nJySYqKsp06tTJ3HLLLWbXrl1NvjZfjXFAaqzavvrqK9O/f3/TsmVLEx0dbbp162amTZtmSktLm3xtxlTdNnvKlCmmTZs2JiEhwfz617823377bbOozRhjrrjiCjNo0KAGq6e6xqxtzZo1ZsSIESY5OdkkJCSYAQMGmPfff79Z1HbVVVeZ5ORkExkZaXr37m1efvnlBqurvmpz3+7c38/27ds98+3fv99ceeWVJiEhwSQkJJgrr7wyoH8l0RRqu+aaa/zOs3z58iZd2/z582udp6E0Vm1N9bwk0DFZXVM5LwmktlCcl4SCZUwjfFMOAAAAAJoQvqMEAAAAAD5olAAAAADAB40SAAAAAPigUQIAAAAAHzRKAAAAAOCDRgkAAAAAfNAoAQAAAIAPGiUAAAAA8EGjBABodC+99JKSkpIafD0rVqyQZVkqLi5u8HUBAJoXGiUAQFAKCgp0++23KzMzU1FRUUpPT9eFF16ojz76KNSpNZobbrhBvXr1UkVFhVf8/fffV4sWLfTll1+GKDMAQH2hUQIABCw3N1d9+/bVsmXL9Nhjj2n9+vVavHixhg0bpttuuy3U6TUI32ZIkubNm6eSkhJNmzbNEysuLtbNN9+s++67T/369av3PI4ePVrvywQA1I5GCQAQsPHjx8uyLH3xxRf6v//7P3Xt2lU9e/bU5MmTtXr1as98c+fOVa9evRQXF6f09HSNHz9ehw4dqnPZ7777rvr27avo6GhlZmZqxowZqqys9Ey3LEt//etf9Zvf/EaxsbHq0qWL3nnnHa9lvP/+++ratatiYmI0bNgw5ebmek3fv3+/rrjiCrVv316xsbHq1auX/vGPf3jNM3ToUP3+97/X5MmTddJJJ2n48OE1ck1ISNBLL72kOXPm6PPPP5ckTZw4UW3bttX999+v3bt367LLLlOrVq2UkpKi0aNHe+WyZs0aDR8+XCeddJJatmypIUOGaO3atV7rsCxLzz//vEaPHq24uDjNnDmzzu0HAKhfNEoAgIAcOHBAixcv1m233aa4uLga06t/58hms+mpp57St99+qwULFmjZsmWaOnVqrctesmSJfve732nChAnasGGDXnjhBb300kt6+OGHveabMWOGLr30Un3zzTcaNWqUrrzySh04cECSlJeXpzFjxmjUqFFat26dbrzxRt1zzz1ezz9y5Ij69u2r9957T99++61uvvlmXXXVVZ5mx23BggWKiIjQ//73P73wwgt+cx46dKjGjx+va665Rq+//rr++c9/6uWXX1ZFRYWGDRum+Ph4ffzxx/r0008VHx+v8847z3N1qqSkRNdcc40++eQTrV69Wl26dNGoUaNUUlLitY5p06Zp9OjRWr9+va6//vpatx8AoAEYAAAC8PnnnxtJ5q233gr6uf/85z9NSkqK5/H8+fNNy5YtPY/PPPNMM2vWLK/nvPLKK6Zt27aex5LM/fff73l86NAhY1mW+eCDD4wxxtx7772mR48exuVyeea5++67jSRTVFRUa26jRo0yU6ZM8TweMmSI6dOnT0B1HT582HTv3t3YbDbz5JNPGmOM+dvf/ma6devmlUd5ebmJiYkxS5Ys8bucyspKk5CQYN59912veidOnBhQHgCA+hcR2jYNANBUGGMkVX0k7HiWL1+uWbNmacOGDXI4HKqsrNSRI0dUWlrq92rUV199pTVr1nhdQXI6nTpy5IgOHz6s2NhYSVLv3r090+Pi4pSQkKB9+/ZJkjZu3KgBAwZ45Tdw4ECv9TidTj3yyCN67bXXtHv3bpWXl6u8vLxGToF+xygmJkZTpkzRpEmTdMcdd3hq2bJlixISErzmPXLkiLZu3SpJ2rdvnx544AEtW7ZMe/fuldPp1OHDh7Vz584TygMAUP9olAAAAenSpYssy9LGjRt18cUX1zrfjh07NGrUKN1yyy166KGHlJycrE8//VQ33HBDrTckcLlcmjFjhsaMGVNjWnR0tOf3Fi1aeE2zLEsul0vSsUauLnPmzNGTTz6pefPmeb5DNXHixBo3bPDXzNUmIiJCdrvd06C5XC717dtXr776ao15W7duLUm69tprVVhYqHnz5qljx46KiorSwIEDf1IeAID6RaMEAAhIcnKyzj33XP35z3/WhAkTapzEFxcXKykpSV9++aUqKys1Z84c2WxVX4X95z//WeeyTz/9dG3atEmdO3c+4fxOOeUU/etf//KKVb/BhCR98sknGj16tH73u99JqmpqNm/erB49epzwen2dfvrpeu2119SmTRslJib6neeTTz7Rs88+q1GjRkmq+n7VDz/8UG85AAB+Om7mAAAI2LPPPiun06lf/vKXevPNN7V582Zt3LhRTz31lOdjbllZWaqsrNTTTz+tbdu26ZVXXtHzzz9f53IfeOABvfzyy5o+fbpycnK0ceNGvfbaa7r//vsDzu2WW27R1q1bNXnyZG3atEkLFy7USy+95DVP586dtXTpUq1atUobN27UuHHjVFBQEPR2qMuVV16pk046SaNHj9Ynn3yi7du3a+XKlbrjjju0a9cuTx6vvPKKNm7cqM8//1xXXnmlYmJi6jUPAMBPQ6MEAAhYRkaG1q5dq2HDhmnKlCk69dRTNXz4cH300Ud67rnnJEl9+vTR3Llz9eijj+rUU0/Vq6++qtmzZ9e53HPPPVfvvfeeli5dqjPOOEMDBgzQ3Llz1bFjx4Bz69Chg9588029++67+sUvfqHnn39es2bN8prnj3/8o04//XSde+65Gjp0qNLS0ur8GOGJiI2N1ccff6wOHTpozJgx6tGjh66//nqVlZV5rjD9/e9/V1FRkU477TRdddVVmjBhgtq0aVOveQAAfhrLBPKhbgAAAAD4GeGKEgAAAAD4oFECAAAAAB80SgAAAADgg0YJAAAAAHzQKAEAAACADxolAAAAAPBBowQAAAAAPmiUAAAAAMAHjRIAAAAA+KBRAgAAAAAfNEoAAAAA4OP/A+LZfNxseBPPAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Extract the year from trade entry dates\n", "trade_years = [d_dates[i].year for i in trade_enter_index]\n", "\n", "# Convert to DataFrame and count trades per year\n", "df = pd.DataFrame({\"Year\": trade_years})\n", "trade_counts = df[\"Year\"].value_counts().sort_index()\n", "\n", "# Plot the histogram\n", "plt.figure(figsize=(10, 5))\n", "plt.bar(trade_counts.index, trade_counts.values, color='blue', alpha=0.7, edgecolor='black')\n", "\n", "# Formatting\n", "plt.xlabel(\"Calendar Year\")\n", "plt.ylabel(\"Number of Trades Started\")\n", "plt.title(\"Trades Started Per Year\")\n", "plt.xticks(trade_counts.index)  # Ensure all years are shown on the x-axis\n", "plt.grid(axis=\"y\", linestyle=\"--\", alpha=0.6)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "dae52ed3-9d6a-494f-9291-f7d7ff14214b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "abd4f825-3a4d-4473-a5b2-de699ffc564e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}