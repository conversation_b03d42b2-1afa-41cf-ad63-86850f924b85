{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "import numpy as np\n", "import pandas as pd\n", "from xbbg import blp\n", "import QuantLib as ql\n", "from loader.ir_future_option_data_loader import interestRateFutureOptionLoader\n", "from pricer.ir_future_option_pricer import interestRateFutureOptionPricer\n", "pd.options.plotting.backend = \"plotly\"\n", "pd.set_option('display.max_rows', 200)  # Default is 60\n", "pd.set_option('display.max_columns', 200)  # Default is 20"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.<PERSON><PERSON><PERSON>(days=94)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dt.date(2017,12,22) - dt.date(2017,9,19)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from loader.ir_future_option_data_scenario_analyzer import interestRateFutureOptionScenarioAnalyzer\n", "live_loader = interestRateFutureOptionScenarioAnalyzer(\"TY\", debug_mode=False)\n", "live_loader_debug = interestRateFutureOptionScenarioAnalyzer(\"TY\", debug_mode=True)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["from addin.excel_addin import calculate_rolldown_vol"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>option_tickers</th>\n", "      <th>ir_option_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1MK5C 110 Comdty</td>\n", "      <td>TY</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3MJ5P 15</td>\n", "      <td>TY</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     option_tickers ir_option_name\n", "0  1MK5C 110 Comdty             TY\n", "1          3MJ5P 15             TY"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["calculate_rolldown_vol(ql.Date(14,4,2024), [\"1MK5C 110 Comdty\",\"3MJ5P 15\"], [\"TUM5 Comdty\"], [110])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code option_expiry_date tte  tte_bus_days\n", "0     3MJ5   April 17th, 2025   3             3\n", "1     4MJ5   April 25th, 2025  11             8\n", "2     1MK5      May 2nd, 2025  18            13\n", "3     2MK5      May 9th, 2025  25            18\n", "4     3MK5     May 16th, 2025  32            23\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>imm_code</th>\n", "      <th>option_expiry_date</th>\n", "      <th>tte</th>\n", "      <th>tte_bus_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3MJ5</td>\n", "      <td>April 17th, 2025</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4MJ5</td>\n", "      <td>April 25th, 2025</td>\n", "      <td>11</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1MK5</td>\n", "      <td>May 2nd, 2025</td>\n", "      <td>18</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2MK5</td>\n", "      <td>May 9th, 2025</td>\n", "      <td>25</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3MK5</td>\n", "      <td>May 16th, 2025</td>\n", "      <td>32</td>\n", "      <td>23</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  imm_code option_expiry_date tte  tte_bus_days\n", "0     3MJ5   April 17th, 2025   3             3\n", "1     4MJ5   April 25th, 2025  11             8\n", "2     1MK5      May 2nd, 2025  18            13\n", "3     2MK5      May 9th, 2025  25            18\n", "4     3MK5     May 16th, 2025  32            23"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["live_loader_debug.prepare_weeklies_candidates(ql.Date(14,4,2025), \"M\", 5)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code   option_expiry_date tte  tte_bus_days\n", "0     3MG5  February 21st, 2025   3             3\n", "1     4MG5  February 28th, 2025  10             8\n", "2     1MH5      March 7th, 2025  17            13\n", "  imm_code   option_expiry_date  tte  tte_bus_days\n", "0       H5  February 21st, 2025    3             3\n", "1     4MG5  February 28th, 2025   10             8\n", "2     1MH5      March 7th, 2025   17            13\n", "3       J5     March 21st, 2025   31            23\n", "4       K5     April 25th, 2025   66            47\n", "5       M5       May 23rd, 2025   94            67\n", "6       N5      June 20th, 2025  122            85\n", "7       U5    August 22nd, 2025  185           129\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>imm_code</th>\n", "      <th>option_expiry_date</th>\n", "      <th>tte</th>\n", "      <th>tte_bus_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>H5</td>\n", "      <td>February 21st, 2025</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4MG5</td>\n", "      <td>February 28th, 2025</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1MH5</td>\n", "      <td>March 7th, 2025</td>\n", "      <td>17</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>J5</td>\n", "      <td>March 21st, 2025</td>\n", "      <td>31</td>\n", "      <td>23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>K5</td>\n", "      <td>April 25th, 2025</td>\n", "      <td>66</td>\n", "      <td>47</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>M5</td>\n", "      <td>May 23rd, 2025</td>\n", "      <td>94</td>\n", "      <td>67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>N5</td>\n", "      <td>June 20th, 2025</td>\n", "      <td>122</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>U5</td>\n", "      <td>August 22nd, 2025</td>\n", "      <td>185</td>\n", "      <td>129</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  imm_code   option_expiry_date  tte  tte_bus_days\n", "0       H5  February 21st, 2025    3             3\n", "1     4MG5  February 28th, 2025   10             8\n", "2     1MH5      March 7th, 2025   17            13\n", "3       J5     March 21st, 2025   31            23\n", "4       K5     April 25th, 2025   66            47\n", "5       M5       May 23rd, 2025   94            67\n", "6       N5      June 20th, 2025  122            85\n", "7       U5    August 22nd, 2025  185           129"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["live_loader_debug.prepare_option_candidates(ql.Date(18,2,2025))"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code   option_expiry_date tte  tte_bus_days\n", "0     3MG5  February 21st, 2025   1             1\n", "1     4MG5  February 28th, 2025   8             6\n", "2     1MH5      March 7th, 2025  15            11\n", "  imm_code   option_expiry_date  tte  tte_bus_days\n", "0       H5  February 21st, 2025    1             1\n", "1     4MG5  February 28th, 2025    8             6\n", "2     1MH5      March 7th, 2025   15            11\n", "3       J5     March 21st, 2025   29            21\n", "4       K5     April 25th, 2025   64            45\n", "5       M5       May 23rd, 2025   92            65\n", "6       N5      June 20th, 2025  120            83\n", "7       U5    August 22nd, 2025  183           127\n", "Futures tickers: {'TYM5 Comdty', 'TYU5 Comdty'}\n", "OIS tickers: {'USOSFRC Curncy'}\n", "Fetched close data:                  price\n", "ticker               \n", "USOSFRC Curncy  4.322\n", "Fetched close data:                    price\n", "ticker                 \n", "TYM5 Comdty  109.171875\n", "TYU5 Comdty  109.171875\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "ATM tickers: {'TYM5C 109 Comdty', 'TYN5C 109 Comdty', 'TYN5P 109 Comdty', 'TYM5P 109 Comdty'}\n", "Fetched close data:                       price\n", "ticker                    \n", "TYM5C 109 Comdty  1.328125\n", "TYN5C 109 Comdty  1.531250\n", "TYN5P 109 Comdty  1.359375\n", "TYM5P 109 Comdty  1.156250\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "Skew tickers: ['TYM5C 111 Comdty', 'TYM5C 111.5 Comdty', 'TYM5C 112.5 Comdty', 'TYM5C 113 Comdty', 'TYN5C 111.5 Comdty', 'TYN5C 112 Comdty', 'TYN5C 113 Comdty', 'TYN5C 113.5 Comdty']\n", "Fetched close data:                         price\n", "ticker                      \n", "TYN5C 112 Comdty    0.484375\n", "TYM5C 113 Comdty    0.234375\n", "TYN5C 111.5 Comdty  0.593750\n", "TYM5C 111.5 Comdty  0.453125\n", "TYN5C 113 Comdty    0.312500\n", "TYM5C 112.5 Comdty  0.296875\n", "TYM5C 111 Comdty    0.578125\n", "TYN5C 113.5 Comdty  0.250000\n"]}], "source": ["scenarios_debug_close = live_loader_debug.load_and_calculate_rolldown_vol(dt.date(2025,2,20), [\" tym5c 109 comdty\", \"  tym5c 110.5 comdty\"], [106.953125], [0], False, \"standardized-lognormal\")"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>discount_factor</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_vol_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_vol_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>109.0</td>\n", "      <td>M5</td>\n", "      <td>65</td>\n", "      <td>106.953125</td>\n", "      <td>0</td>\n", "      <td>92</td>\n", "      <td>M5</td>\n", "      <td>92</td>\n", "      <td>65</td>\n", "      <td>N5</td>\n", "      <td>120</td>\n", "      <td>83</td>\n", "      <td>109.171875</td>\n", "      <td>0.989076</td>\n", "      <td>109.171875</td>\n", "      <td>0.985798</td>\n", "      <td>0.989076</td>\n", "      <td>111.261213</td>\n", "      <td>TYM5C 111 Comdty</td>\n", "      <td>TYM5C 111.5 Comdty</td>\n", "      <td>111.615866</td>\n", "      <td>TYN5C 111.5 Comdty</td>\n", "      <td>TYN5C 112 Comdty</td>\n", "      <td>0.05674</td>\n", "      <td>0.058642</td>\n", "      <td>0.05674</td>\n", "      <td>0.657851</td>\n", "      <td>111.0</td>\n", "      <td>111.5</td>\n", "      <td>111.5</td>\n", "      <td>112.0</td>\n", "      <td>0.578125</td>\n", "      <td>0.058245</td>\n", "      <td>0.453125</td>\n", "      <td>0.058377</td>\n", "      <td>0.058314</td>\n", "      <td>0.59375</td>\n", "      <td>0.05879</td>\n", "      <td>0.484375</td>\n", "      <td>0.059251</td>\n", "      <td>0.058897</td>\n", "      <td>0.058314</td>\n", "      <td>0.499499</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>110.5</td>\n", "      <td>M5</td>\n", "      <td>65</td>\n", "      <td>106.953125</td>\n", "      <td>0</td>\n", "      <td>92</td>\n", "      <td>M5</td>\n", "      <td>92</td>\n", "      <td>65</td>\n", "      <td>N5</td>\n", "      <td>120</td>\n", "      <td>83</td>\n", "      <td>109.171875</td>\n", "      <td>0.989076</td>\n", "      <td>109.171875</td>\n", "      <td>0.985798</td>\n", "      <td>0.989076</td>\n", "      <td>112.792330</td>\n", "      <td>TYM5C 112.5 Comdty</td>\n", "      <td>TYM5C 113 Comdty</td>\n", "      <td>113.411789</td>\n", "      <td>TYN5C 113 Comdty</td>\n", "      <td>TYN5C 113.5 Comdty</td>\n", "      <td>0.05674</td>\n", "      <td>0.058642</td>\n", "      <td>0.05674</td>\n", "      <td>1.132144</td>\n", "      <td>112.5</td>\n", "      <td>113.0</td>\n", "      <td>113.0</td>\n", "      <td>113.5</td>\n", "      <td>0.296875</td>\n", "      <td>0.060787</td>\n", "      <td>0.234375</td>\n", "      <td>0.061543</td>\n", "      <td>0.061230</td>\n", "      <td>0.31250</td>\n", "      <td>0.05993</td>\n", "      <td>0.250000</td>\n", "      <td>0.060398</td>\n", "      <td>0.060315</td>\n", "      <td>0.061230</td>\n", "      <td>0.253458</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump tte  \\\n", "0        C   109.0       M5            65   106.953125        0  92   \n", "1        C   110.5       M5            65   106.953125        0  92   \n", "\n", "  imm_code_1  tte_1  tte_bus_days_1 imm_code_2  tte_2  tte_bus_days_2  \\\n", "0         M5     92              65         N5    120              83   \n", "1         M5     92              65         N5    120              83   \n", "\n", "       atmf_1  discount_factor_1      atmf_2  discount_factor_2  \\\n", "0  109.171875           0.989076  109.171875           0.985798   \n", "1  109.171875           0.989076  109.171875           0.985798   \n", "\n", "  discount_factor    strike_1          ticker_1_0          ticker_1_1  \\\n", "0        0.989076  111.261213    TYM5C 111 Comdty  TYM5C 111.5 Comdty   \n", "1        0.989076  112.792330  TYM5C 112.5 Comdty    TYM5C 113 Comdty   \n", "\n", "     strike_2          ticker_2_0          ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  111.615866  TYN5C 111.5 Comdty    TYN5C 112 Comdty    0.05674   0.058642   \n", "1  113.411789    TYN5C 113 Comdty  TYN5C 113.5 Comdty    0.05674   0.058642   \n", "\n", "   atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0  0.05674   0.657851       111.0       111.5       111.5       112.0   \n", "1  0.05674   1.132144       112.5       113.0       113.0       113.5   \n", "\n", "   skew_px_1_0  skew_vol_1_0  skew_px_1_1  skew_vol_1_1  skew_vol_1  \\\n", "0     0.578125      0.058245     0.453125      0.058377    0.058314   \n", "1     0.296875      0.060787     0.234375      0.061543    0.061230   \n", "\n", "   skew_px_2_0  skew_vol_2_0  skew_px_2_1  skew_vol_2_1  skew_vol_2       vol  \\\n", "0      0.59375       0.05879     0.484375      0.059251    0.058897  0.058314   \n", "1      0.31250       0.05993     0.250000      0.060398    0.060315  0.061230   \n", "\n", "   option_px  \n", "0   0.499499  \n", "1   0.253458  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios_debug_close[\"scenarios\"]"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.51562731295027,)"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.blackFormula(\n", "                        1,\n", "                        111,\n", "                        108.953125,\n", "                        0.058149 * np.sqrt(65 / 252),\n", "                        0.98906,\n", "                    ),"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.4965900628009532,)"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.blackFormula(\n", "                        1,\n", "                        109,\n", "                        106.953125,\n", "                        0.058149 * np.sqrt(65 / 252),\n", "                        0.98906,\n", "                    ),"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["np.array()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import QuantLib as ql"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(1,10,2028)"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.Date.startOfMonth(ql.Date(1,1,2025)) + ql.Period(\"45M\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["loader = interestRateFutureOptionLoader(\"SFR\")"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H4 March 20th, 2024\n", "U3 September 20th, 2023\n", "Z3 December 20th, 2023\n", "Ticker does not exist: SFRZ3 Comdty\n", "M4 June 19th, 2024\n"]}, {"data": {"text/plain": ["['SFRH4 Comdty', 'SFRU23 Comdty', 'SFRZ23 Comdty', 'SFRM4 Comdty']"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["loader.get_underlying_futures_tickers([\"SFRU3\",\"SFRZ3\", \"SFRH4\",\"SFRM4\"], \"SFR\", ql.Date(1,1,2023))"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.0003513080873245311"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["111/108.953125 - 109 / 106.953125"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code   option_expiry_date tte  tte_bus_days\n", "0     4MG5  February 28th, 2025   7             5\n", "1     1MH5      March 7th, 2025  14            10\n", "2     2MH5     March 14th, 2025  21            15\n", "  imm_code   option_expiry_date  tte  tte_bus_days\n", "0     4MG5  February 28th, 2025    7             5\n", "1     1MH5      March 7th, 2025   14            10\n", "2     2MH5     March 14th, 2025   21            15\n", "3       J5     March 21st, 2025   28            20\n", "4       K5     April 25th, 2025   63            44\n", "5       M5       May 23rd, 2025   91            64\n", "6       N5      June 20th, 2025  119            82\n", "7       U5    August 22nd, 2025  182           126\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>imm_code</th>\n", "      <th>option_expiry_date</th>\n", "      <th>tte</th>\n", "      <th>tte_bus_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4MG5</td>\n", "      <td>February 28th, 2025</td>\n", "      <td>7</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1MH5</td>\n", "      <td>March 7th, 2025</td>\n", "      <td>14</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2MH5</td>\n", "      <td>March 14th, 2025</td>\n", "      <td>21</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>J5</td>\n", "      <td>March 21st, 2025</td>\n", "      <td>28</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>K5</td>\n", "      <td>April 25th, 2025</td>\n", "      <td>63</td>\n", "      <td>44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>M5</td>\n", "      <td>May 23rd, 2025</td>\n", "      <td>91</td>\n", "      <td>64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>N5</td>\n", "      <td>June 20th, 2025</td>\n", "      <td>119</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>U5</td>\n", "      <td>August 22nd, 2025</td>\n", "      <td>182</td>\n", "      <td>126</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  imm_code   option_expiry_date  tte  tte_bus_days\n", "0     4MG5  February 28th, 2025    7             5\n", "1     1MH5      March 7th, 2025   14            10\n", "2     2MH5     March 14th, 2025   21            15\n", "3       J5     March 21st, 2025   28            20\n", "4       K5     April 25th, 2025   63            44\n", "5       M5       May 23rd, 2025   91            64\n", "6       N5      June 20th, 2025  119            82\n", "7       U5    August 22nd, 2025  182           126"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["live_loader_debug.prepare_option_candidates(ql.Date(21,2,2025))"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code   option_expiry_date  tte  tte_bus_days\n", "0        J5     March 21st, 2025   28            20\n", "1        K5     April 25th, 2025   63            44\n", "2        M5       May 23rd, 2025   91            64\n", "3        N5      June 20th, 2025  119            82\n", "4        U5    August 22nd, 2025  182           126\n", "5        Z5  November 21st, 2025  273           190\n", "6        H6  February 20th, 2026  364           250\n", "7        M6       May 22nd, 2026  455           314\n", "8        U6    August 21st, 2026  546           376\n", "9        Z6  November 20th, 2026  637           440\n", "10       H7  February 19th, 2027  728           500\n", "11       M7       May 21st, 2027  819           564\n", "Futures tickers: {'TYU5 Comdty', 'TYM5 Comdty'}\n", "OIS tickers: {'USOSFRC Curncy'}\n", "Fetched close data:                  price\n", "ticker               \n", "USOSFRC Curncy  4.322\n", "Fetching live data (PX_MID) from Bloomberg: ['TYU5 Comdty', 'TYM5 Comdty']\n", "Fetched live data:                    price\n", "TYM5 Comdty  109.226562\n", "TYU5 Comdty  109.250000\n", "Fetched close data:                    price\n", "ticker                 \n", "TYU5 Comdty  109.171875\n", "TYM5 Comdty  109.171875\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "ATM tickers: {'TYN5P 109 Comdty', 'TYM5P 109 Comdty', 'TYN5C 109 Comdty', 'TYM5C 109 Comdty'}\n", "Fetching live data (PX_MID) from Bloomberg: ['TYN5P 109 Comdty', 'TYM5P 109 Comdty', 'TYN5C 109 Comdty', 'TYM5C 109 Comdty']\n", "Fetched live data:                       price  live\n", "TYM5C 109 Comdty  1.328125  True\n", "TYM5P 109 Comdty  1.109375  True\n", "TYN5C 109 Comdty  1.531250  True\n", "TYN5P 109 Comdty  1.296875  True\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "min_tick_size: 0.5\n", "Skew tickers: ['TYM5C 108.5 Comdty', 'TYM5C 110 Comdty', 'TYM5C 110.5 Comdty', 'TYN5C 108.5 Comdty', 'TYN5C 110.5 Comdty', 'TYN5C 111 Comdty']\n", "Fetching live data (PX_MID) from Bloomberg: ['TYM5C 108.5 Comdty', 'TYM5C 110.5 Comdty', 'TYM5C 110 Comdty', 'TYN5C 111 Comdty', 'TYN5C 110.5 Comdty', 'TYN5C 108.5 Comdty']\n", "Fetched live data:                         price  live\n", "TYM5C 108.5 Comdty  1.609375  True\n", "TYM5C 110 Comdty    0.882812  True\n", "TYM5C 110.5 Comdty  0.710938  True\n", "TYN5C 108.5 Comdty  1.796875  True\n", "TYN5C 110.5 Comdty  0.882812  True\n", "TYN5C 111 Comdty    0.726562  True\n"]}], "source": ["scenarios_debug = live_loader_debug.load_and_calculate_rolldown_vol(dt.date(2025,2,21), [\" tym5c 109 comdty\", \"  tym5c 110.5 comdty\"], [109.24218750], [0], True, \"standardized-lognormal\")"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>atmf_1</th>\n", "      <th>atmf_close_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>atmf_close_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>discount_factor</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>live_1_0</th>\n", "      <th>skew_vol_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>live_1_1</th>\n", "      <th>skew_vol_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>live_2_0</th>\n", "      <th>skew_vol_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>live_2_1</th>\n", "      <th>skew_vol_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>109.0</td>\n", "      <td>M5</td>\n", "      <td>64</td>\n", "      <td>109.242188</td>\n", "      <td>0</td>\n", "      <td>91</td>\n", "      <td>M5</td>\n", "      <td>91</td>\n", "      <td>64</td>\n", "      <td>N5</td>\n", "      <td>119</td>\n", "      <td>82</td>\n", "      <td>109.226562</td>\n", "      <td>109.171875</td>\n", "      <td>0.989193</td>\n", "      <td>109.25</td>\n", "      <td>109.171875</td>\n", "      <td>0.985915</td>\n", "      <td>0.989193</td>\n", "      <td>108.984410</td>\n", "      <td>TYM5C 108.5 Comdty</td>\n", "      <td>TYM5C 109 Comdty</td>\n", "      <td>108.968023</td>\n", "      <td>TYN5C 108.5 Comdty</td>\n", "      <td>TYN5C 109 Comdty</td>\n", "      <td>0.05601</td>\n", "      <td>0.057618</td>\n", "      <td>0.05601</td>\n", "      <td>-0.078630</td>\n", "      <td>108.5</td>\n", "      <td>109.0</td>\n", "      <td>108.5</td>\n", "      <td>109.0</td>\n", "      <td>1.609375</td>\n", "      <td>True</td>\n", "      <td>0.056185</td>\n", "      <td>1.328125</td>\n", "      <td>True</td>\n", "      <td>0.055886</td>\n", "      <td>0.055896</td>\n", "      <td>1.796875</td>\n", "      <td>True</td>\n", "      <td>0.057156</td>\n", "      <td>1.531250</td>\n", "      <td>True</td>\n", "      <td>0.057371</td>\n", "      <td>0.057357</td>\n", "      <td>0.055896</td>\n", "      <td>1.336527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>110.5</td>\n", "      <td>M5</td>\n", "      <td>64</td>\n", "      <td>109.242188</td>\n", "      <td>0</td>\n", "      <td>91</td>\n", "      <td>M5</td>\n", "      <td>91</td>\n", "      <td>64</td>\n", "      <td>N5</td>\n", "      <td>119</td>\n", "      <td>82</td>\n", "      <td>109.226562</td>\n", "      <td>109.171875</td>\n", "      <td>0.989193</td>\n", "      <td>109.25</td>\n", "      <td>109.171875</td>\n", "      <td>0.985915</td>\n", "      <td>0.989193</td>\n", "      <td>110.484195</td>\n", "      <td>TYM5C 110 Comdty</td>\n", "      <td>TYM5C 110.5 Comdty</td>\n", "      <td>110.716112</td>\n", "      <td>TYN5C 110.5 Comdty</td>\n", "      <td>TYN5C 111 Comdty</td>\n", "      <td>0.05601</td>\n", "      <td>0.057618</td>\n", "      <td>0.05601</td>\n", "      <td>0.405583</td>\n", "      <td>110.0</td>\n", "      <td>110.5</td>\n", "      <td>110.5</td>\n", "      <td>111.0</td>\n", "      <td>0.882812</td>\n", "      <td>True</td>\n", "      <td>0.056316</td>\n", "      <td>0.710938</td>\n", "      <td>True</td>\n", "      <td>0.056771</td>\n", "      <td>0.056757</td>\n", "      <td>0.882812</td>\n", "      <td>True</td>\n", "      <td>0.057378</td>\n", "      <td>0.726562</td>\n", "      <td>True</td>\n", "      <td>0.057730</td>\n", "      <td>0.057530</td>\n", "      <td>0.056757</td>\n", "      <td>0.716040</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump tte  \\\n", "0        C   109.0       M5            64   109.242188        0  91   \n", "1        C   110.5       M5            64   109.242188        0  91   \n", "\n", "  imm_code_1  tte_1  tte_bus_days_1 imm_code_2  tte_2  tte_bus_days_2  \\\n", "0         M5     91              64         N5    119              82   \n", "1         M5     91              64         N5    119              82   \n", "\n", "       atmf_1  atmf_close_1  discount_factor_1  atmf_2  atmf_close_2  \\\n", "0  109.226562    109.171875           0.989193  109.25    109.171875   \n", "1  109.226562    109.171875           0.989193  109.25    109.171875   \n", "\n", "   discount_factor_2 discount_factor    strike_1          ticker_1_0  \\\n", "0           0.985915        0.989193  108.984410  TYM5C 108.5 Comdty   \n", "1           0.985915        0.989193  110.484195    TYM5C 110 Comdty   \n", "\n", "           ticker_1_1    strike_2          ticker_2_0        ticker_2_1  \\\n", "0    TYM5C 109 Comdty  108.968023  TYN5C 108.5 Comdty  TYN5C 109 Comdty   \n", "1  TYM5C 110.5 Comdty  110.716112  TYN5C 110.5 Comdty  TYN5C 111 Comdty   \n", "\n", "   atm_vol_1  atm_vol_2  atm_vol  moneyness  strike_1_0  strike_1_1  \\\n", "0    0.05601   0.057618  0.05601  -0.078630       108.5       109.0   \n", "1    0.05601   0.057618  0.05601   0.405583       110.0       110.5   \n", "\n", "   strike_2_0  strike_2_1  skew_px_1_0  live_1_0  skew_vol_1_0  skew_px_1_1  \\\n", "0       108.5       109.0     1.609375      True      0.056185     1.328125   \n", "1       110.5       111.0     0.882812      True      0.056316     0.710938   \n", "\n", "   live_1_1  skew_vol_1_1  skew_vol_1  skew_px_2_0  live_2_0  skew_vol_2_0  \\\n", "0      True      0.055886    0.055896     1.796875      True      0.057156   \n", "1      True      0.056771    0.056757     0.882812      True      0.057378   \n", "\n", "   skew_px_2_1  live_2_1  skew_vol_2_1  skew_vol_2       vol  option_px  \n", "0     1.531250      True      0.057371    0.057357  0.055896   1.336527  \n", "1     0.726562      True      0.057730    0.057530  0.056757   0.716040  "]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios_debug[\"scenarios\"]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code   option_expiry_date tte  tte_bus_days\n", "0     4MG5  February 28th, 2025   4             4\n", "1     1MH5      March 7th, 2025  11             9\n", "2     2MH5     March 14th, 2025  18            14\n", "  imm_code   option_expiry_date  tte  tte_bus_days\n", "0     4MG5  February 28th, 2025    4             4\n", "1     1MH5      March 7th, 2025   11             9\n", "2     2MH5     March 14th, 2025   18            14\n", "3       J5     March 21st, 2025   25            19\n", "4       K5     April 25th, 2025   60            43\n", "5       M5       May 23rd, 2025   88            63\n", "6       N5      June 20th, 2025  116            81\n", "7       U5    August 22nd, 2025  179           125\n", "8       Z5  November 21st, 2025  270           189\n", "Futures tickers: {'TYM5 Comdty'}\n", "OIS tickers: {'USOSFR2Z Curncy'}\n", "Fetched close data:                    price\n", "ticker                 \n", "USOSFR2Z Curncy  4.3192\n", "Fetched close data:                  price\n", "ticker               \n", "TYM5 Comdty  109.6875\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "ATM tickers: {'2MH5C 109.75 Comdty', '1MH5P 109.75 Comdty', '1MH5C 109.75 Comdty', '2MH5P 109.75 Comdty'}\n", "Fetched close data:                          price\n", "ticker                       \n", "2MH5C 109.75 Comdty  0.578125\n", "1MH5P 109.75 Comdty  0.531250\n", "1MH5C 109.75 Comdty  0.468750\n", "2MH5P 109.75 Comdty  0.640625\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "min_tick_size: 0.25\n", "Skew tickers: ['1MH5C 109 Comdty', '1MH5C 109.25 Comdty', '1MH5C 110.25 Comdty', '1MH5C 110.5 Comdty', '2MH5C 108.75 Comdty', '2MH5C 109 Comdty', '2MH5C 110.5 Comdty', '2MH5C 110.75 Comdty']\n", "Fetched close data:                          price\n", "ticker                       \n", "2MH5C 110.5 Comdty   0.312500\n", "1MH5C 109 Comdty     0.890625\n", "2MH5C 108.75 Comdty  1.171875\n", "1MH5C 109.25 Comdty  0.734375\n", "2MH5C 110.75 Comdty  0.250000\n", "2MH5C 109 Comdty     1.000000\n", "1MH5C 110.25 Comdty  0.281250\n", "1MH5C 110.5 Comdty   0.218750\n"]}], "source": ["scenarios_debug = live_loader_debug.load_and_calculate_rolldown_vol(dt.date(2025,2,24), [\" tym5c 109 comdty\", \"  tym5c 110.5 comdty\"], [109.6875], [-50], False, \"standardized-lognormal\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>discount_factor</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_vol_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_vol_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>109.0</td>\n", "      <td>M5</td>\n", "      <td>13</td>\n", "      <td>109.6875</td>\n", "      <td>-50</td>\n", "      <td>17</td>\n", "      <td>1MH5</td>\n", "      <td>11</td>\n", "      <td>9</td>\n", "      <td>2MH5</td>\n", "      <td>18</td>\n", "      <td>14</td>\n", "      <td>109.6875</td>\n", "      <td>0.998682</td>\n", "      <td>109.6875</td>\n", "      <td>0.997845</td>\n", "      <td>0.997965</td>\n", "      <td>109.104354</td>\n", "      <td>1MH5C 109 Comdty</td>\n", "      <td>1MH5C 109.25 Comdty</td>\n", "      <td>108.976320</td>\n", "      <td>2MH5C 108.75 Comdty</td>\n", "      <td>2MH5C 109 Comdty</td>\n", "      <td>0.06045</td>\n", "      <td>0.059144</td>\n", "      <td>0.059326</td>\n", "      <td>-0.466619</td>\n", "      <td>109.00</td>\n", "      <td>109.25</td>\n", "      <td>108.75</td>\n", "      <td>109.00</td>\n", "      <td>0.890625</td>\n", "      <td>0.057041</td>\n", "      <td>0.734375</td>\n", "      <td>0.058833</td>\n", "      <td>0.057790</td>\n", "      <td>1.171875</td>\n", "      <td>0.057563</td>\n", "      <td>1.00</td>\n", "      <td>0.058011</td>\n", "      <td>0.057969</td>\n", "      <td>0.057944</td>\n", "      <td>0.980139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>110.5</td>\n", "      <td>M5</td>\n", "      <td>13</td>\n", "      <td>109.6875</td>\n", "      <td>-50</td>\n", "      <td>17</td>\n", "      <td>1MH5</td>\n", "      <td>11</td>\n", "      <td>9</td>\n", "      <td>2MH5</td>\n", "      <td>18</td>\n", "      <td>14</td>\n", "      <td>109.6875</td>\n", "      <td>0.998682</td>\n", "      <td>109.6875</td>\n", "      <td>0.997845</td>\n", "      <td>0.997965</td>\n", "      <td>110.375956</td>\n", "      <td>1MH5C 110.25 Comdty</td>\n", "      <td>1MH5C 110.5 Comdty</td>\n", "      <td>110.528184</td>\n", "      <td>2MH5C 110.5 Comdty</td>\n", "      <td>2MH5C 110.75 Comdty</td>\n", "      <td>0.06045</td>\n", "      <td>0.059144</td>\n", "      <td>0.059326</td>\n", "      <td>0.547703</td>\n", "      <td>110.25</td>\n", "      <td>110.50</td>\n", "      <td>110.50</td>\n", "      <td>110.75</td>\n", "      <td>0.281250</td>\n", "      <td>0.062082</td>\n", "      <td>0.218750</td>\n", "      <td>0.063730</td>\n", "      <td>0.062913</td>\n", "      <td>0.312500</td>\n", "      <td>0.061719</td>\n", "      <td>0.25</td>\n", "      <td>0.062512</td>\n", "      <td>0.061808</td>\n", "      <td>0.061962</td>\n", "      <td>0.294356</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump tte  \\\n", "0        C   109.0       M5            13     109.6875      -50  17   \n", "1        C   110.5       M5            13     109.6875      -50  17   \n", "\n", "  imm_code_1  tte_1  tte_bus_days_1 imm_code_2  tte_2  tte_bus_days_2  \\\n", "0       1MH5     11               9       2MH5     18              14   \n", "1       1MH5     11               9       2MH5     18              14   \n", "\n", "     atmf_1  discount_factor_1    atmf_2  discount_factor_2 discount_factor  \\\n", "0  109.6875           0.998682  109.6875           0.997845        0.997965   \n", "1  109.6875           0.998682  109.6875           0.997845        0.997965   \n", "\n", "     strike_1           ticker_1_0           ticker_1_1    strike_2  \\\n", "0  109.104354     1MH5C 109 Comdty  1MH5C 109.25 Comdty  108.976320   \n", "1  110.375956  1MH5C 110.25 Comdty   1MH5C 110.5 Comdty  110.528184   \n", "\n", "            ticker_2_0           ticker_2_1  atm_vol_1  atm_vol_2   atm_vol  \\\n", "0  2MH5C 108.75 Comdty     2MH5C 109 Comdty    0.06045   0.059144  0.059326   \n", "1   2MH5C 110.5 Comdty  2MH5C 110.75 Comdty    0.06045   0.059144  0.059326   \n", "\n", "   moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  skew_px_1_0  \\\n", "0  -0.466619      109.00      109.25      108.75      109.00     0.890625   \n", "1   0.547703      110.25      110.50      110.50      110.75     0.281250   \n", "\n", "   skew_vol_1_0  skew_px_1_1  skew_vol_1_1  skew_vol_1  skew_px_2_0  \\\n", "0      0.057041     0.734375      0.058833    0.057790     1.171875   \n", "1      0.062082     0.218750      0.063730    0.062913     0.312500   \n", "\n", "   skew_vol_2_0  skew_px_2_1  skew_vol_2_1  skew_vol_2       vol  option_px  \n", "0      0.057563         1.00      0.058011    0.057969  0.057944   0.980139  \n", "1      0.061719         0.25      0.062512    0.061808  0.061962   0.294356  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios_debug[\"scenarios\"]"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-11-25</th>\n", "      <th>TYH5 Comdty</th>\n", "      <td>110.718750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.323800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty</th>\n", "      <td>110.859375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.762300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-26</th>\n", "      <th>TYH5 Comdty</th>\n", "      <td>110.515625</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              price\n", "date       ticker                                  \n", "2024-11-25 TYH5 Comdty                   110.718750\n", "           TYH5 Comdty CTD_FORWARD_DV01    6.323800\n", "           TYM5 Comdty                   110.859375\n", "           TYM5 Comdty CTD_FORWARD_DV01    6.762300\n", "2024-11-26 TYH5 Comdty                   110.515625"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_parquet(r\"N:\\PMs\\FIRV\\FC\\investment\\loader\\ir_future_option_data\\TY_underlying\\TY_20241125.parquet\")\n", "data.head()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TYH5 Comdty</th>\n", "      <td>110.515625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.310300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty</th>\n", "      <td>110.656250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.748400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   price\n", "ticker                                  \n", "TYH5 Comdty                   110.515625\n", "TYH5 Comdty CTD_FORWARD_DV01    6.310300\n", "TYM5 Comdty                   110.656250\n", "TYM5 Comdty CTD_FORWARD_DV01    6.748400"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["data = data.loc[dt.date(2024,11,26)]\n", "data"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["data = data[~data.index.duplicated(keep=\"first\")]"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["is_ctd_fwd_risk = data.index.str.endswith(\"CTD_FORWARD_DV01\")\n", "data_fwd_risk = data[is_ctd_fwd_risk]\n", "data = data[~is_ctd_fwd_risk]"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TYH5 Comdty</th>\n", "      <td>110.515625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty</th>\n", "      <td>110.656250</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  price\n", "ticker                 \n", "TYH5 Comdty  110.515625\n", "TYM5 Comdty  110.656250"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["data_fwd_risk.index = data.index.str.removesuffix(\" CTD_FORWARD_DV01\")"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CTD_FORWARD_DV01</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TYH5 Comdty</th>\n", "      <td>6.3103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty</th>\n", "      <td>6.7484</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             CTD_FORWARD_DV01\n", "ticker                       \n", "TYH5 Comdty            6.3103\n", "TYM5 Comdty            6.7484"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["data_fwd_risk.rename(columns={\"price\": \"CTD_FORWARD_DV01\"})"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "      <th>CTD_FORWARD_DV01</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>TYH5 Comdty</th>\n", "      <td>110.515625</td>\n", "      <td>6.3103</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM5 Comdty</th>\n", "      <td>110.656250</td>\n", "      <td>6.7484</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  price  CTD_FORWARD_DV01\n", "ticker                                   \n", "TYH5 Comdty  110.515625            6.3103\n", "TYM5 Comdty  110.656250            6.7484"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.concat([data, data_fwd_risk.rename(columns={\"price\": \"CTD_FORWARD_DV01\"})], axis=1)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-09-23</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.347656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.687700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.773438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.711200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-09-24</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.398438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.688800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.824219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.712300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-09-25</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.332031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.687400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.757812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.710900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-09-26</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.195312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.684500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.621094</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.707900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2024-09-27</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.308594</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.686900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.710300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-30</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.121094</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.682900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.539062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.706200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.957031</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-01</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.179688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.684100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.613281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.707800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>105.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.718400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-02</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>104.136719</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.683200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.705300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.917969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.715900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-03</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.980469</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.679900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>104.328125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.701600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.917969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.715900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-04</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.535156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.670400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.859375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.691600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.449219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.705900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-07</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.667400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.707031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.688300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.296875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.702600</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-08</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.433594</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.668300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.753906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.689300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.136719</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.699200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-09</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.347656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.666400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.660156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.687300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.042969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.697200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-10</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.378906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.667100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.691406</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.688000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.074219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.697800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-11</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.476562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.669200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.781250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.689900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.164062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.699700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"3\" valign=\"top\">2024-10-14</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.359375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.664062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.046875</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-15</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.668700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.689300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.132812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.699100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-16</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.488281</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.669400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.785156</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.690000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.167969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.699800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-17</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.386719</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.667300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.691406</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.688000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.074219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.697800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-18</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.445312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.668500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.689300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>104.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.696900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-21</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.281250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.665000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.585938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.685700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>103.867188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.693400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-22</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.242188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.664200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.539062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.684700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>103.820312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.692400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-23</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.144531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.662100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.433594</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.682500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>103.714844</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.690100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-24</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.199219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.663300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.492188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.683700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>103.773438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.691400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-10-25</th>\n", "      <th>TUZ4 Comdty</th>\n", "      <td>103.128906</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.661800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty</th>\n", "      <td>103.417969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.682200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty</th>\n", "      <td>103.699219</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUM5 Comdty CTD_FORWARD_DV01</th>\n", "      <td>1.689800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              price\n", "date       ticker                                  \n", "2024-09-23 TUZ4 Comdty                   104.347656\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.687700\n", "           TUH5 Comdty                   104.773438\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.711200\n", "2024-09-24 TUZ4 Comdty                   104.398438\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.688800\n", "           TUH5 Comdty                   104.824219\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.712300\n", "2024-09-25 TUZ4 Comdty                   104.332031\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.687400\n", "           TUH5 Comdty                   104.757812\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.710900\n", "2024-09-26 TUZ4 Comdty                   104.195312\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.684500\n", "           TUH5 Comdty                   104.621094\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.707900\n", "2024-09-27 TUZ4 Comdty                   104.308594\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.686900\n", "           TUH5 Comdty                   104.734375\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.710300\n", "2024-09-30 TUZ4 Comdty                   104.121094\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.682900\n", "           TUH5 Comdty                   104.539062\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.706200\n", "           TUM5 Comdty                   104.957031\n", "2024-10-01 TUZ4 Comdty                   104.179688\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.684100\n", "           TUH5 Comdty                   104.613281\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.707800\n", "           TUM5 Comdty                   105.031250\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.718400\n", "2024-10-02 TUZ4 Comdty                   104.136719\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.683200\n", "           TUH5 Comdty                   104.500000\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.705300\n", "           TUM5 Comdty                   104.917969\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.715900\n", "2024-10-03 TUZ4 Comdty                   103.980469\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.679900\n", "           TUH5 Comdty                   104.328125\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.701600\n", "           TUM5 Comdty                   104.917969\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.715900\n", "2024-10-04 TUZ4 Comdty                   103.535156\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.670400\n", "           TUH5 Comdty                   103.859375\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.691600\n", "           TUM5 Comdty                   104.449219\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.705900\n", "2024-10-07 TUZ4 Comdty                   103.390625\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.667400\n", "           TUH5 Comdty                   103.707031\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.688300\n", "           TUM5 Comdty                   104.296875\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.702600\n", "2024-10-08 TUZ4 Comdty                   103.433594\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.668300\n", "           TUH5 Comdty                   103.753906\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.689300\n", "           TUM5 Comdty                   104.136719\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.699200\n", "2024-10-09 TUZ4 Comdty                   103.347656\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.666400\n", "           TUH5 Comdty                   103.660156\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.687300\n", "           TUM5 Comdty                   104.042969\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.697200\n", "2024-10-10 TUZ4 Comdty                   103.378906\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.667100\n", "           TUH5 Comdty                   103.691406\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.688000\n", "           TUM5 Comdty                   104.074219\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.697800\n", "2024-10-11 TUZ4 Comdty                   103.476562\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.669200\n", "           TUH5 Comdty                   103.781250\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.689900\n", "           TUM5 Comdty                   104.164062\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.699700\n", "2024-10-14 TUZ4 Comdty                   103.359375\n", "           TUH5 Comdty                   103.664062\n", "           TUM5 Comdty                   104.046875\n", "2024-10-15 TUZ4 Comdty                   103.453125\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.668700\n", "           TUH5 Comdty                   103.750000\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.689300\n", "           TUM5 Comdty                   104.132812\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.699100\n", "2024-10-16 TUZ4 Comdty                   103.488281\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.669400\n", "           TUH5 Comdty                   103.785156\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.690000\n", "           TUM5 Comdty                   104.167969\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.699800\n", "2024-10-17 TUZ4 Comdty                   103.386719\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.667300\n", "           TUH5 Comdty                   103.691406\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.688000\n", "           TUM5 Comdty                   104.074219\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.697800\n", "2024-10-18 TUZ4 Comdty                   103.445312\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.668500\n", "           TUH5 Comdty                   103.750000\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.689300\n", "           TUM5 Comdty                   104.031250\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.696900\n", "2024-10-21 TUZ4 Comdty                   103.281250\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.665000\n", "           TUH5 Comdty                   103.585938\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.685700\n", "           TUM5 Comdty                   103.867188\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.693400\n", "2024-10-22 TUZ4 Comdty                   103.242188\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.664200\n", "           TUH5 Comdty                   103.539062\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.684700\n", "           TUM5 Comdty                   103.820312\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.692400\n", "2024-10-23 TUZ4 Comdty                   103.144531\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.662100\n", "           TUH5 Comdty                   103.433594\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.682500\n", "           TUM5 Comdty                   103.714844\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.690100\n", "2024-10-24 TUZ4 Comdty                   103.199219\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.663300\n", "           TUH5 Comdty                   103.492188\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.683700\n", "           TUM5 Comdty                   103.773438\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.691400\n", "2024-10-25 TUZ4 Comdty                   103.128906\n", "           TUZ4 Comdty CTD_FORWARD_DV01    1.661800\n", "           TUH5 Comdty                   103.417969\n", "           TUH5 Comdty CTD_FORWARD_DV01    1.682200\n", "           TUM5 Comdty                   103.699219\n", "           TUM5 Comdty CTD_FORWARD_DV01    1.689800"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet(r\"N:\\PMs\\FIRV\\FC\\investment\\loader\\ir_future_option_data\\TU_underlying\\TU_20240923.parquet\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-12-26</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.437500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.236100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.718750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.355400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.953125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.023500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-12-27</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.937500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.271700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>113.328125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.395800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>114.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.057800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-12-28</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.546875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.243900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.366800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>114.062500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.031000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-12-29</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.506000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.366800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>114.140625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.036400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-02</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.464700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.296875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.327400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.515625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.993400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-03</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.234375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.480200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.515625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.341900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.009500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-04</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.671875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.442900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.984375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.306800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.187500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.970900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-05</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.424300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.718750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.289200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.906250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.951600</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-08</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.447100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.046875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.310900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.975200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-09</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.578125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.436700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.906250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.301600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.093750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.964500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-10</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.562500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.435700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.299500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.078125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.963400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-11</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.466700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.343750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.330600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.546875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.995600</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-12</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>113.265625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.482200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>112.593750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.347100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.781250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.011700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-16</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.431600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.843750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.297400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>113.015625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.959100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-17</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.400600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.421875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.269500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.546875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.926900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-18</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.796875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.385100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.203125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.255000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.910900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-19</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.381000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.249900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.906600</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-22</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>112.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.406800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.274700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.640625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.933400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-23</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.828125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.387100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.218750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.256100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.343750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.913000</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-24</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.546875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.368600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>110.968750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.239500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.140625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.899100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-25</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.906250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.392300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.262300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.923700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2024-01-26</th>\n", "      <th>TYM4 Comdty</th>\n", "      <td>111.609375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYM4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.372700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>111.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.243700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty</th>\n", "      <td>112.203125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.903400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              price\n", "date       ticker                                  \n", "2023-12-26 TYM4 Comdty                   113.437500\n", "           TYM4 Comdty CTD_FORWARD_DV01    7.236100\n", "           TYH4 Comdty                   112.718750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.355400\n", "           TYU4 Comdty                   113.953125\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.023500\n", "2023-12-27 TYM4 Comdty                   113.937500\n", "           TYM4 Comdty CTD_FORWARD_DV01    7.271700\n", "           TYH4 Comdty                   113.328125\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.395800\n", "           TYU4 Comdty                   114.453125\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.057800\n", "2023-12-28 TYM4 Comdty                   113.546875\n", "           TYM4 Comdty CTD_FORWARD_DV01    7.243900\n", "           TYH4 Comdty                   112.890625\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.366800\n", "           TYU4 Comdty                   114.062500\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.031000\n", "2023-12-29 TYM4 Comdty                   113.625000\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.506000\n", "           TYH4 Comdty                   112.890625\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.366800\n", "           TYU4 Comdty                   114.140625\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.036400\n", "2024-01-02 TYM4 Comdty                   113.000000\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.464700\n", "           TYH4 Comdty                   112.296875\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.327400\n", "           TYU4 Comdty                   113.515625\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.993400\n", "2024-01-03 TYM4 Comdty                   113.234375\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.480200\n", "           TYH4 Comdty                   112.515625\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.341900\n", "           TYU4 Comdty                   113.750000\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.009500\n", "2024-01-04 TYM4 Comdty                   112.671875\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.442900\n", "           TYH4 Comdty                   111.984375\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.306800\n", "           TYU4 Comdty                   113.187500\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.970900\n", "2024-01-05 TYM4 Comdty                   112.390625\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.424300\n", "           TYH4 Comdty                   111.718750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.289200\n", "           TYU4 Comdty                   112.906250\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.951600\n", "2024-01-08 TYM4 Comdty                   112.734375\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.447100\n", "           TYH4 Comdty                   112.046875\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.310900\n", "           TYU4 Comdty                   113.250000\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.975200\n", "2024-01-09 TYM4 Comdty                   112.578125\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.436700\n", "           TYH4 Comdty                   111.906250\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.301600\n", "           TYU4 Comdty                   113.093750\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.964500\n", "2024-01-10 TYM4 Comdty                   112.562500\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.435700\n", "           TYH4 Comdty                   111.875000\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.299500\n", "           TYU4 Comdty                   113.078125\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.963400\n", "2024-01-11 TYM4 Comdty                   113.031250\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.466700\n", "           TYH4 Comdty                   112.343750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.330600\n", "           TYU4 Comdty                   113.546875\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.995600\n", "2024-01-12 TYM4 Comdty                   113.265625\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.482200\n", "           TYH4 Comdty                   112.593750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.347100\n", "           TYU4 Comdty                   113.781250\n", "           TYU4 Comdty CTD_FORWARD_DV01    7.011700\n", "2024-01-16 TYM4 Comdty                   112.500000\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.431600\n", "           TYH4 Comdty                   111.843750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.297400\n", "           TYU4 Comdty                   113.015625\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.959100\n", "2024-01-17 TYM4 Comdty                   112.031250\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.400600\n", "           TYH4 Comdty                   111.421875\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.269500\n", "           TYU4 Comdty                   112.546875\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.926900\n", "2024-01-18 TYM4 Comdty                   111.796875\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.385100\n", "           TYH4 Comdty                   111.203125\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.255000\n", "           TYU4 Comdty                   112.312500\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.910900\n", "2024-01-19 TYM4 Comdty                   111.734375\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.381000\n", "           TYH4 Comdty                   111.125000\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.249900\n", "           TYU4 Comdty                   112.250000\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.906600\n", "2024-01-22 TYM4 Comdty                   112.125000\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.406800\n", "           TYH4 Comdty                   111.500000\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.274700\n", "           TYU4 Comdty                   112.640625\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.933400\n", "2024-01-23 TYM4 Comdty                   111.828125\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.387100\n", "           TYH4 Comdty                   111.218750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.256100\n", "           TYU4 Comdty                   112.343750\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.913000\n", "2024-01-24 TYM4 Comdty                   111.546875\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.368600\n", "           TYH4 Comdty                   110.968750\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.239500\n", "           TYU4 Comdty                   112.140625\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.899100\n", "2024-01-25 TYM4 Comdty                   111.906250\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.392300\n", "           TYH4 Comdty                   111.312500\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.262300\n", "           TYU4 Comdty                   112.500000\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.923700\n", "2024-01-26 TYM4 Comdty                   111.609375\n", "           TYM4 Comdty CTD_FORWARD_DV01    6.372700\n", "           TYH4 Comdty                   111.031250\n", "           TYH4 Comdty CTD_FORWARD_DV01    6.243700\n", "           TYU4 Comdty                   112.203125\n", "           TYU4 Comdty CTD_FORWARD_DV01    6.903400"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.read_parquet(r\"N:\\PMs\\FIRV\\FC\\investment\\loader\\ir_future_option_data\\TY_underlying\\TY_20231225.parquet\")"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-05-30</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.288900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>114.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.549800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-05-31</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.319300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>114.468750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.578800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-01</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>115.234375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.343200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>114.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.596400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-02</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.343750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.281300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.843750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.537300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-05</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.284500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.540400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-06</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.281250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.276900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.781250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.533200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-07</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.718750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.237900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.218750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.495900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-08</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.234375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.273700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.659300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-09</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.249800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.507300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-12</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.859375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.247600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.359375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.633900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-13</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.171875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.199900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>112.671875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.587400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-14</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.437500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.218300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>112.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.473200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-15</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>114.015625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.258500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.511500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-16</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.609375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.230300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.046875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.612800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-20</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.248700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.502100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-21</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.953125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.254100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.502100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.593750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.240700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-22</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.214000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>112.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.591600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.015625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.200700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-23</th>\n", "      <th>TYZ23 Comdty</th>\n", "      <td>113.687500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.235700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty</th>\n", "      <td>113.078125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU23 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.614900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.328125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.222300</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               price\n", "date       ticker                                   \n", "2023-05-30 TYZ23 Comdty                   114.453125\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.288900\n", "           TYU23 Comdty                   114.031250\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.549800\n", "2023-05-31 TYZ23 Comdty                   114.890625\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.319300\n", "           TYU23 Comdty                   114.468750\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.578800\n", "2023-06-01 TYZ23 Comdty                   115.234375\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.343200\n", "           TYU23 Comdty                   114.734375\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.596400\n", "2023-06-02 TYZ23 Comdty                   114.343750\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.281300\n", "           TYU23 Comdty                   113.843750\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.537300\n", "2023-06-05 TYZ23 Comdty                   114.390625\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.284500\n", "           TYU23 Comdty                   113.890625\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.540400\n", "2023-06-06 TYZ23 Comdty                   114.281250\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.276900\n", "           TYU23 Comdty                   113.781250\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.533200\n", "2023-06-07 TYZ23 Comdty                   113.718750\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.237900\n", "           TYU23 Comdty                   113.218750\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.495900\n", "2023-06-08 TYZ23 Comdty                   114.234375\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.273700\n", "           TYU23 Comdty                   113.734375\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.659300\n", "2023-06-09 TYZ23 Comdty                   113.890625\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.249800\n", "           TYU23 Comdty                   113.390625\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.507300\n", "2023-06-12 TYZ23 Comdty                   113.859375\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.247600\n", "           TYU23 Comdty                   113.359375\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.633900\n", "2023-06-13 TYZ23 Comdty                   113.171875\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.199900\n", "           TYU23 Comdty                   112.671875\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.587400\n", "2023-06-14 TYZ23 Comdty                   113.437500\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.218300\n", "           TYU23 Comdty                   112.875000\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.473200\n", "2023-06-15 TYZ23 Comdty                   114.015625\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.258500\n", "           TYU23 Comdty                   113.453125\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.511500\n", "2023-06-16 TYZ23 Comdty                   113.609375\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.230300\n", "           TYU23 <PERSON>mdty                   113.046875\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.612800\n", "2023-06-20 TYZ23 Comdty                   113.875000\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.248700\n", "           TYU23 Comdty                   113.312500\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.502100\n", "2023-06-21 TYZ23 Comdty                   113.953125\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.254100\n", "           TYU23 Comdty                   113.312500\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.502100\n", "           TYH4 Comdty                    114.593750\n", "           TYH4 Comdty CTD_FORWARD_DV01     7.240700\n", "2023-06-22 TYZ23 Comdty                   113.375000\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.214000\n", "           TYU23 Comdty                   112.734375\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.591600\n", "           TYH4 Comdty                    114.015625\n", "           TYH4 Comdty CTD_FORWARD_DV01     7.200700\n", "2023-06-23 TYZ23 Comdty                   113.687500\n", "           TYZ23 Comdty CTD_FORWARD_DV01    7.235700\n", "           TYU23 Comdty                   113.078125\n", "           TYU23 Comdty CTD_FORWARD_DV01    6.614900\n", "           TYH4 Comdty                    114.328125\n", "           TYH4 Comdty CTD_FORWARD_DV01     7.222300"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["udl_data = pd.read_parquet(r\"N:\\PMs\\FIRV\\FC\\investment\\loader\\ir_future_option_data\\TY_underlying\\TY_20230529.parquet\")\n", "udl_data"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-05-30</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.288900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>114.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.549800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-05-31</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.319300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>114.468750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.578800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-01</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>115.234375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.343200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>114.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.596400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-02</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.343750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.281300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.843750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.537300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-05</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.284500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.540400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-06</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.281250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.276900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.781250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.533200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-07</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.718750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.237900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.218750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.495900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-08</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.234375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.273700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.659300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-09</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.890625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.249800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.390625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.507300</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-12</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.859375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.247600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.359375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.633900</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-13</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.171875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.199900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>112.671875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.587400</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-14</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.437500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.218300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>112.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.473200</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-15</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>114.015625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.258500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.453125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.511500</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-16</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.609375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.230300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.046875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.612800</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">2023-06-20</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.248700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.502100</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-21</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.953125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.254100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.312500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.502100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.593750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.240700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-22</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.214000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>112.734375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.591600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.015625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.200700</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"6\" valign=\"top\">2023-06-23</th>\n", "      <th>TYZ3 Comdty</th>\n", "      <td>113.687500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYZ3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.235700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty</th>\n", "      <td>113.078125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYU3 Comdty CTD_FORWARD_DV01</th>\n", "      <td>6.614900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty</th>\n", "      <td>114.328125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TYH4 Comdty CTD_FORWARD_DV01</th>\n", "      <td>7.222300</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              price\n", "date       ticker                                  \n", "2023-05-30 TYZ3 Comdty                   114.453125\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.288900\n", "           TYU3 Comdty                   114.031250\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.549800\n", "2023-05-31 TYZ3 Comdty                   114.890625\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.319300\n", "           TYU3 Comdty                   114.468750\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.578800\n", "2023-06-01 TYZ3 Comdty                   115.234375\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.343200\n", "           TYU3 Comdty                   114.734375\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.596400\n", "2023-06-02 TYZ3 Comdty                   114.343750\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.281300\n", "           TYU3 Comdty                   113.843750\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.537300\n", "2023-06-05 TYZ3 Comdty                   114.390625\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.284500\n", "           TYU3 Comdty                   113.890625\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.540400\n", "2023-06-06 TYZ3 Comdty                   114.281250\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.276900\n", "           TYU3 Comdty                   113.781250\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.533200\n", "2023-06-07 TYZ3 Comdty                   113.718750\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.237900\n", "           TYU3 Comdty                   113.218750\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.495900\n", "2023-06-08 TYZ3 Comdty                   114.234375\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.273700\n", "           TYU3 Comdty                   113.734375\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.659300\n", "2023-06-09 TYZ3 Comdty                   113.890625\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.249800\n", "           TYU3 Comdty                   113.390625\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.507300\n", "2023-06-12 TYZ3 Comdty                   113.859375\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.247600\n", "           TYU3 Comdty                   113.359375\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.633900\n", "2023-06-13 TYZ3 Comdty                   113.171875\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.199900\n", "           TYU3 Comdty                   112.671875\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.587400\n", "2023-06-14 TYZ3 Comdty                   113.437500\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.218300\n", "           TYU3 Comdty                   112.875000\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.473200\n", "2023-06-15 TYZ3 Comdty                   114.015625\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.258500\n", "           TYU3 Comdty                   113.453125\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.511500\n", "2023-06-16 TYZ3 Comdty                   113.609375\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.230300\n", "           TYU3 Comdty                   113.046875\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.612800\n", "2023-06-20 TYZ3 Comdty                   113.875000\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.248700\n", "           TYU3 Comdty                   113.312500\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.502100\n", "2023-06-21 TYZ3 Comdty                   113.953125\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.254100\n", "           TYU3 Comdty                   113.312500\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.502100\n", "           TYH4 Comdty                   114.593750\n", "           TYH4 Comdty CTD_FORWARD_DV01    7.240700\n", "2023-06-22 TYZ3 Comdty                   113.375000\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.214000\n", "           TYU3 Comdty                   112.734375\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.591600\n", "           TYH4 Comdty                   114.015625\n", "           TYH4 Comdty CTD_FORWARD_DV01    7.200700\n", "2023-06-23 TYZ3 Comdty                   113.687500\n", "           TYZ3 Comdty CTD_FORWARD_DV01    7.235700\n", "           TYU3 Comdty                   113.078125\n", "           TYU3 Comdty CTD_FORWARD_DV01    6.614900\n", "           TYH4 Comdty                   114.328125\n", "           TYH4 Comdty CTD_FORWARD_DV01    7.222300"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "ty = \"TY\"\n", "fv = \"FV\"\n", "pattern = rf\"^((?:{re.escape(ty)}|{re.escape(fv)})[HMUZ])(\\d{{2,3}})(.*)$\"\n", "udl_data.rename(index=lambda t: re.sub(pattern, lambda m: m.group(1) + m.group(2)[-1] + m.group(3), t), level=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}