import sys
import os
import traceback

investment_parent_path = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.append(investment_parent_path)
import dash
import dash_bootstrap_components as dbc
from dash import dcc, html, Input, Output, State
import plotly.graph_objects as go
from curves.config import available_curves
from helpers.plot_tools import plot_curves_fig

# Start by initializing the Dash app
app = dash.Dash(name=__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])

# Layout for the dashboard
app.layout = dbc.Container(
    [
        html.H1("Interest Rate Curves Viewer", className="title"),
        html.Hr(),
        # Dropdown for selecting curves
        dbc.Row(
            [
                dbc.Col(
                    [
                        html.H3("Select Curves:", className="ms-1"),
                        dcc.Dropdown(
                            id="curves-selection",
                            options=[
                                {"label": curve, "value": curve}
                                for curve in available_curves.keys()
                            ],
                            clearable=True,
                            multi=True,
                            placeholder="Select here",
                            className="custom-dropdown",
                            value=["USD.SOFR"],
                        ),
                    ],
                    width=4,
                )
            ]
        ),
        html.Br(),
        dbc.Row(
            [
                dbc.Col(
                    [
                        html.H4("Reference Date:", className="ms-1"),
                        dbc.RadioItems(
                            id="context-key-option",
                            options=[
                                {"label": "Close", "value": "close"},
                                {"label": "Live", "value": "live"},
                            ],
                            value="close",
                            inline=True,
                        ),
                        dcc.DatePickerSingle(
                            id="reference-date-picker",
                            date="2024-10-16",  # Default date
                            display_format="YYYY-MM-DD",
                            className="date-picker",
                        ),
                    ]
                ),
            ]
        ),
        html.Br(),
        # Inputs for start, end, and forward term
        dbc.Row(
            [
                dbc.Col(
                    [
                        dbc.Label("Forward Term:"),
                        dbc.Input(
                            id="forward-term",
                            type="text",
                            value="1d",
                            debounce=True,
                            size="sm",
                            style={"width": "80px"},
                        ),
                    ],
                    width=1,
                ),
                dbc.Col(
                    [
                        dbc.Label("Start Period:"),
                        dbc.Input(
                            id="start-period",
                            type="text",
                            value="0d",
                            debounce=True,
                            size="sm",
                            style={"width": "80px"},
                        ),
                    ],
                    width=1,
                ),
                dbc.Col(
                    [
                        dbc.Label("End Period:"),
                        dbc.Input(
                            id="end-period",
                            type="text",
                            value="30y",
                            debounce=True,
                            size="sm",
                            style={"width": "80px"},
                        ),
                    ],
                    width=1,
                ),
                dbc.Col(
                    [
                        dbc.Label("Interpolation Method:"),
                        dcc.Dropdown(
                            id="interp-method-selection",
                            options={
                                "Default": "Default",
                                "PiecewiseLogMixedLinearCubicDiscount": "FlatForwardThenCubic",
                                "PiecewiseLogLinearDiscount": "LogLinearDiscount",
                                "PiecewiseFlatForward": "FlatForward",
                                "PiecewiseLinearForward": "LinearForward",
                                "PiecewiseLogCubicDiscount": "Cubic",
                            },
                            clearable=False,
                            multi=False,
                            className="custom-dropdown",
                            value="Default",
                        ),
                    ],
                    width=2,
                ),
                dbc.Col(
                    [
                        dbc.Label("Data Source:"),
                        dcc.Dropdown(
                            id="data-source",
                            options={"JPM": "JPM", "BBG": "Bloomberg"},
                            clearable=False,
                            multi=False,
                            className="custom-dropdown",
                            value="JPM",
                        ),
                    ],
                    width=2,
                ),
            ]
        ),
        html.Br(),
        # Refresh button to update the graph
        dbc.Button("Refresh", id="refresh-button", n_clicks=0),
        html.Br(),
        html.Br(),
        # Error alert component
        dbc.Alert(
            id="error-alert",
            is_open=False,
            dismissable=True,
            color="danger",
            className="mt-3",
        ),
        # Graph to display the curves
        dcc.Loading(
            id="loading-curve-plot",
            type="default",
            children=[
                dcc.Graph(
                    id="curve-plot",
                    config={"displaylogo": False, "displayModeBar": True},
                )
            ],
        ),
    ]
)


# Callback to enable/disable reference date picker based on option selected
@app.callback(
    Output("reference-date-picker", "disabled"),
    [Input("context-key-option", "value")],
)
def toggle_date_picker(context_key_option):
    return context_key_option == "live"


# Callback to update the curve plot based on user inputs
@app.callback(
    [
        Output("curve-plot", "figure"),
        Output("error-alert", "children"),
        Output("error-alert", "is_open"),
    ],
    [Input("refresh-button", "n_clicks")],
    [
        State("curves-selection", "value"),
        State("context-key-option", "value"),
        State("reference-date-picker", "date"),
        State("start-period", "value"),
        State("end-period", "value"),
        State("forward-term", "value"),
        State("interp-method-selection", "value"),
        State("data-source", "value"),
    ],
)
def update_curve_plot(
    n_clicks,
    selected_curves,
    context_key_option,
    reference_date_picker,
    start,
    end,
    forward_term,
    interp_method,
    data_source,
):
    if n_clicks == 0:
        return (
            go.Figure(),
            "",
            False,
        )  # Return empty figure, no error message, error alert closed

    try:
        reference_date = (
            "live" if context_key_option == "live" else reference_date_picker
        )
        params = {"debug_mode": True}
        if interp_method != "Default":
            params["interp_method"] = interp_method
        params["data_source"] = data_source
        curves = [
            available_curves[curve](reference_date, **params)
            for curve in selected_curves
        ]
        for curve in curves:
            curve.calibrate()

        # If successful, return the figure and close any error alerts
        figure = plot_curves_fig(curves, start, end, forward_term)
        return figure, "", False

    except Exception as e:
        # Create an empty figure when there's an error
        empty_fig = go.Figure()
        empty_fig.add_annotation(
            text="Error occurred - check error message above",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            xanchor="center",
            yanchor="middle",
            showarrow=False,
            font=dict(size=16, color="red"),
        )
        empty_fig.update_layout(title="Error in Curve Generation", showlegend=False)

        # Format error message for display with proper HTML line breaks
        traceback_str = traceback.format_exc()
        error_message = html.Div(
            [
                html.H5(
                    "Error occurred:", style={"color": "red", "margin-bottom": "10px"}
                ),
                html.P(str(e), style={"font-weight": "bold", "margin-bottom": "15px"}),
                html.H6("Full traceback:", style={"margin-bottom": "10px"}),
                html.Pre(
                    traceback_str,
                    style={
                        "background-color": "#f8f9fa",
                        "padding": "10px",
                        "border-radius": "5px",
                        "overflow-x": "auto",
                        "white-space": "pre-wrap",
                        "font-family": "monospace",
                        "font-size": "12px",
                    },
                ),
            ]
        )

        return empty_fig, error_message, True


# Run the Dash app
if __name__ == "__main__":
    app.run_server(debug=True)
