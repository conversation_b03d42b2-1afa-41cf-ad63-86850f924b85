{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "from xbbg import blp\n", "import blpapi"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import blpapi\n", "def is_bloomberg_connected():\n", "    sess_opts = blpapi.SessionOptions()\n", "    sess_opts.setNumStartAttempts(numStartAttempts=3)\n", "    sess_opts.setAutoRestartOnDisconnection(autoRestart=True)\n", "    user = blpapi.AuthUser.createWithLogonName()\n", "    auth = blpapi.AuthOptions.createWithUser(user=user)\n", "    sess_opts.setSessionIdentityOptions(authOptions=auth)\n", "    session = blpapi.Session(sess_opts)\n", "    if session.start():\n", "        session.stop()\n", "        return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>ERZ5C 98.0000 Comdty</th>\n", "      <th>ERZ5 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_SETTLE</th>\n", "      <th>PX_SETTLE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-17</th>\n", "      <td>0.16</td>\n", "      <td>97.93</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           ERZ5C 98.0000 Comdty ERZ5 Comdty\n", "                      PX_SETTLE   PX_SETTLE\n", "2025-03-17                 0.16       97.93"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh([\"ERZ5C 98.0000 Comdty\", \"ERZ5 Comdty\"], [\"PX_SETTLE\"], dt.date(2025,3,17), dt.date(2025,3,18), )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>ERZ5C 98.0000 Comdty</th>\n", "      <th>ERZ5 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_SETTLE</th>\n", "      <th>PX_SETTLE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-17</th>\n", "      <td>0.16</td>\n", "      <td>97.930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-18</th>\n", "      <td>NaN</td>\n", "      <td>97.925</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           ERZ5C 98.0000 Comdty ERZ5 Comdty\n", "                      PX_SETTLE   PX_SETTLE\n", "2025-03-17                 0.16      97.930\n", "2025-03-18                  NaN      97.925"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh([\"ERZ5C 98.0000 Comdty\", \"ERZ5 Comdty\"], [\"PX_SETTLE\"], dt.date(2025,3,17), dt.date(2025,3,18), )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">TYM5 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_SETTLE</th>\n", "      <th>CONVENTIONAL_CTD_FORWARD_FRSK</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-17</th>\n", "      <td>110.609375</td>\n", "      <td>6.1927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-18</th>\n", "      <td>NaN</td>\n", "      <td>6.1978</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           TYM5 Comdty                              \n", "             PX_SETTLE CONVENTIONAL_CTD_FORWARD_FRSK\n", "2025-03-17  110.609375                        6.1927\n", "2025-03-18         NaN                        6.1978"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh([\"TYM5 Comdty\"], [\"PX_SETTLE\", \"CONVENTIONAL_CTD_FORWARD_FRSK\"], dt.date(2025,3,17), dt.date(2025,3,18), )"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">TYM5 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_SETTLE</th>\n", "      <th>CONVENTIONAL_CTD_FORWARD_FRSK</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-03-17</th>\n", "      <td>110.609375</td>\n", "      <td>6.1927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-03-18</th>\n", "      <td>NaN</td>\n", "      <td>6.1978</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           TYM5 Comdty                              \n", "             PX_SETTLE CONVENTIONAL_CTD_FORWARD_FRSK\n", "2025-03-17  110.609375                        6.1927\n", "2025-03-18         NaN                        6.1978"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh([\"TYM5 Comdty\"], [\"PX_SETTLE\", \"CONVENTIONAL_CTD_FORWARD_FRSK\"], dt.date(2025,3,17), dt.date(2025,3,18), )"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["is_bloomberg_connected()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sess_opts = blpapi.SessionOptions()\n", "sess_opts.setApplicationIdentityKey(\"14496a7a-482b-4542-8a94-f9e9f423513b\")\n", "sess_opts.setServerHost('localhost')\n", "sess_opts.setServerPort(8194)\n", "session = blpapi.Session(sess_opts)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["session.start()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["session.openService(\"//blp/refdata\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["refDataService = session.getService(\"//blp/refdata\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["request = refDataService.createRequest(\"ReferenceDataRequest\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["tickers = [\"IBM US Equity\", \"AAPL US Equity\"]\n", "for ticker in tickers:\n", "    request.getElement(\"securities\").appendValue(ticker)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["fields = [\"Name\", \"PX_LAST\"]\n", "for field in fields:\n", "    request.getElement(\"fields\").appendValue(field)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<blpapi.internals.CorrelationId; proxy of <Swig Object of type 'blpapi_CorrelationId_t *' at 0x00000238FF038150> >"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["session.sendRequest(request)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CID: {[ valueType=AUTOGEN classId=0 value=5 ]}\n", "ServiceOpened = {\n", "    serviceName = \"//blp/refdata\"\n", "}\n", "\n", "CID: {[ valueType=AUTOGEN classId=0 value=7 ]}\n", "RequestId: 993e8d39-5f52-4b9b-bf2b-322544e58d07\n", "ReferenceDataResponse = {\n", "    responseError = {\n", "        source = \"rsfrdsvc1\"\n", "        code = -4002\n", "        category = \"LIMIT\"\n", "        message = \"Workflow review needed. [nid:19560] \"\n", "        subcategory = \"WORKFLOW_REVIEW_NEEDED\"\n", "    }\n", "}\n", "\n"]}], "source": ["while True:\n", "    # Wait up to 500ms for next event\n", "    event = session.nextEvent(500)\n", "    \n", "    for msg in event:\n", "        # Check if it's the response message and print it\n", "        print(msg)\n", "\n", "    # Break out of the loop when the final response event is received.\n", "    if event.eventType() == blpapi.Event.RESPONSE:\n", "        break"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0QZ5C 97.6250 Comdty</th>\n", "      <td>SOFR 1yr MidCurve Dec25C 97.63</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                name\n", "0QZ5C 97.6250 Comdty  SOFR 1yr MidCurve Dec25C 97.63"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdp(tickers='0QZ5C 97.6250 Comdty', flds=['Name'], sess=session, port=8194)\n", "data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0QZ5C 97.625 Comdty</th>\n", "      <td>SOFR 1yr MidCurve Dec25C 97.63</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               name\n", "0QZ5C 97.625 Comdty  SOFR 1yr MidCurve Dec25C 97.63"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdp(tickers='0QZ5C 97.625 Comdty', flds=['Name'], sess=session, port=8194)\n", "data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>0QZ5C 97.625 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-04-22</th>\n", "      <td>0.1575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-23</th>\n", "      <td>0.1350</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           0QZ5C 97.625 Comdty\n", "                       PX_LAST\n", "2025-04-22              0.1575\n", "2025-04-23              0.1350"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers='0QZ5C 97.625 Comdty', flds=['PX_LAST'], start_date=\"20250422\", end_date=\"20250423\", sess=session, port=8194)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>0QZ5C 97.6250 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-04-22</th>\n", "      <td>0.1575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-04-23</th>\n", "      <td>0.1350</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           0QZ5C 97.6250 Comdty\n", "                        PX_LAST\n", "2025-04-22               0.1575\n", "2025-04-23               0.1350"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers='0QZ5C 97.6250 Comdty', flds=['PX_LAST'], start_date=\"20250422\", end_date=\"20250423\", sess=session, port=8194)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SFRG5C 96.250 Comdty</th>\n", "      <td>3 Month SOFR Opt  Feb25C 96.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       security_name\n", "SFRG5C 96.250 Comdty  3 Month SOFR Opt  Feb25C 96.25"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdp(tickers='SFRG5C 96.250 Comdty', flds=['Security_Name'])\n", "data"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>columns</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SFRG5C 96.250 Comdty</th>\n", "      <td>3 Month SOFR Opt  Feb25C 96.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                             columns\n", "SFRG5C 96.250 Comdty  3 Month SOFR Opt  Feb25C 96.25"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["data.set_axis(['columns'], axis=1).dropna()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>SFIU2C 97.1500 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_SETTLE</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-07-18</th>\n", "      <td>0.48</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           SFIU2C 97.1500 Comdty\n", "                       PX_SETTLE\n", "2022-07-18                  0.48"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers=['SFIU2C 97.1500 Comdty'], flds='PX_SETTLE', start_date='2022-07-18',end_date='2022-07-18')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security_name</th>\n", "      <th>px_mid</th>\n", "      <th>px_last</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>EUR003M Index</th>\n", "      <td>Euribor 3 Month ACT/360</td>\n", "      <td>NaN</td>\n", "      <td>2.886</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SOFRRATE Index</th>\n", "      <td>Secured Overnight Financing</td>\n", "      <td>4.62</td>\n", "      <td>4.62</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              security_name px_mid px_last\n", "EUR003M Index       Euribor 3 Month ACT/360    NaN   2.886\n", "SOFRRATE Index  Secured Overnight Financing   4.62    4.62"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Morning at 2024.12.13 11:00\n", "blp.bdp(tickers=['SOFRRATE Index', 'EUR003M Index'], flds=['Security_Name','GICS_Sector_Name','PX_MID','PX_LAST'])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>px_last</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSO1 Curncy</th>\n", "      <td>3.69810</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSO2 Curncy</th>\n", "      <td>3.43047</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              px_last\n", "USSO1 Curncy  3.69810\n", "USSO2 Curncy  3.43047"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["d = blp.bdp(tickers=['USSO1 Curncy', 'USSO2 Curncy'], flds=['PX_LAST','PX_SETTLE'])\n", "d"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSO1 Curncy</th>\n", "      <td>3.69810</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSO2 Curncy</th>\n", "      <td>3.43047</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                price\n", "ticker               \n", "USSO1 Curncy  3.69810\n", "USSO2 Curncy  3.43047"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["d.rename_axis(index=\"ticker\").rename(\n", "            columns={\"px_last\": \"price\", \"PX_LAST\": \"price\"}\n", "        )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSO1 Curncy</th>\n", "      <th>px_last</th>\n", "      <td>3.6924</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSO2 Curncy</th>\n", "      <th>px_last</th>\n", "      <td>3.4263</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       price\n", "USSO1 Curncy px_last  3.6924\n", "USSO2 Curncy px_last  3.4263"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["d.stack().to_frame(name=\"price\").dropna()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSO1 Curncy</th>\n", "      <td>4.1235</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSO2 Curncy</th>\n", "      <td>3.9464</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   2\n", "USSO1 Curncy  4.1235\n", "USSO2 Curncy  3.9464"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["d.columns = [2]\n", "d"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USSO1 Curncy</th>\n", "      <th>USSO2 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.1235</td>\n", "      <td>3.9464</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   USSO1 Curncy  USSO2 Curncy\n", "2        4.1235        3.9464"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["d.T"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>px_mid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ER1 Comdty</th>\n", "      <td>97.1425</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             px_mid\n", "ER1 Comdty  97.1425"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["d2 = blp.bdp(tickers=['EUR003M Index', 'ER1 Comdty'], flds=['PX_MID'])\n", "d2"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["d2.columns = [dt.date(2024,12,12)]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>px_mid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>EESWE1Z Curncy</th>\n", "      <td>2.98525</td>\n", "    </tr>\n", "    <tr>\n", "      <th>EESWE2Z Curncy</th>\n", "      <td>2.94965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>ESTRON Index</th>\n", "      <td>3.16400</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 px_mid\n", "EESWE1Z Curncy  2.98525\n", "EESWE2Z Curncy  2.94965\n", "ESTRON Index    3.16400"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["d3 = blp.bdp(tickers=['ESTRON Index', 'EESWE1Z Curncy', 'EESWE2Z Curncy'], flds=[\"PX_MID\"])\n", "d3"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EESWE1Z Curncy</th>\n", "      <th>EESWE2Z Curncy</th>\n", "      <th>ESTRON Index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.98525</td>\n", "      <td>2.94965</td>\n", "      <td>3.164</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   EESWE1Z Curncy  EESWE2Z Curncy  ESTRON Index\n", "1         2.98525         2.94965         3.164"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["d3.columns = [1]\n", "d3 = d3.T\n", "d3"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">SOFRRATE Index</th>\n", "      <th colspan=\"2\" halign=\"left\">USSOSR1 Curncy</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-12-09</th>\n", "      <td>4.63</td>\n", "      <td>4.63</td>\n", "      <td>4.4113</td>\n", "      <td>4.4113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-10</th>\n", "      <td>4.64</td>\n", "      <td>4.64</td>\n", "      <td>4.4100</td>\n", "      <td>4.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-11</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.3720</td>\n", "      <td>4.3720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-12</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.3735</td>\n", "      <td>4.3735</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           SOFRRATE Index         USSOSR1 Curncy        \n", "                   PX_MID PX_LAST         PX_MID PX_LAST\n", "2024-12-09           4.63    4.63         4.4113  4.4113\n", "2024-12-10           4.64    4.64         4.4100  4.4100\n", "2024-12-11            NaN     NaN         4.3720  4.3720\n", "2024-12-12            NaN     NaN         4.3735  4.3735"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers=['SOFRRATE Index', 'USSOSR1 Curncy'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'], start_date='2024-12-09',end_date='2024-12-12')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdp(tickers='SFRH5 Comdty', flds=['Security_Name','MIFID_MATURITY_DATE'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["blp.bdp(tickers='SFRH5 Comdty', flds=['Security_Name','MIFID_MATURITY_DATE'])"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security_name</th>\n", "      <th>sw_eff_dt</th>\n", "      <th>maturity</th>\n", "      <th>px_mid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSOSR1 Curncy</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>4.245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR2 Curncy</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>4.0525</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                security_name   sw_eff_dt    maturity  px_mid\n", "USSOSR1 Curncy  US DOLLAR/US DOLLAR US DOLLAR  2025-05-07  2025-06-18   4.245\n", "USSOSR2 Curncy  US DOLLAR/US DOLLAR US DOLLAR  2025-06-18  2025-07-30  4.0525"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdp(tickers=['USSOSR1 Curncy', 'USSOSR2 Curncy'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID'])\n", "data"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th>field</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">USSOSR1 Curncy</th>\n", "      <th>security_name</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sw_eff_dt</th>\n", "      <td>2025-05-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>maturity</th>\n", "      <td>2025-06-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>px_mid</th>\n", "      <td>4.245</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"4\" valign=\"top\">USSOSR2 Curncy</th>\n", "      <th>security_name</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>sw_eff_dt</th>\n", "      <td>2025-06-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>maturity</th>\n", "      <td>2025-07-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>px_mid</th>\n", "      <td>4.0525</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                      price\n", "ticker         field                                       \n", "USSOSR1 Curncy security_name  US DOLLAR/US DOLLAR US DOLLAR\n", "               sw_eff_dt                         2025-05-07\n", "               maturity                          2025-06-18\n", "               px_mid                                 4.245\n", "USSOSR2 Curncy security_name  US DOLLAR/US DOLLAR US DOLLAR\n", "               sw_eff_dt                         2025-06-18\n", "               maturity                          2025-07-30\n", "               px_mid                                4.0525"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["temp = data.stack().to_frame(name=\"price\").rename_axis([\"ticker\", \"field\"])\n", "temp"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["temp.index = temp.index.get_level_values(0) + temp.index.get_level_values(1).map({\"px_last\": \"\", \"conventional_ctd_forward_frsk\": \" CTD_FORWARD_DV01\", \"px_mid\": \"\"})"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>2025-05-07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>2025-06-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR1 Curncy</th>\n", "      <td>4.245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>2025-06-18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>NaN</th>\n", "      <td>2025-07-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR2 Curncy</th>\n", "      <td>4.0525</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                        price\n", "ticker                                       \n", "NaN             US DOLLAR/US DOLLAR US DOLLAR\n", "NaN                                2025-05-07\n", "NaN                                2025-06-18\n", "USSOSR1 Curncy                          4.245\n", "NaN             US DOLLAR/US DOLLAR US DOLLAR\n", "NaN                                2025-06-18\n", "NaN                                2025-07-30\n", "USSOSR2 Curncy                         4.0525"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["temp.rename_axis(\"ticker\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sw_eff_dt</th>\n", "      <th>maturity</th>\n", "      <th>px_mid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <td>2024-10-18</td>\n", "      <td>2024-10-25</td>\n", "      <td>4.846</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  sw_eff_dt    maturity px_mid\n", "USOSFR1Z Curncy  2024-10-18  2024-10-25  4.846"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdp(tickers='USOSFR1Z Curncy', flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID'])"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">USOSFR1Z Curncy</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>5.3266</td>\n", "      <td>5.3266</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-12</th>\n", "      <td>5.3210</td>\n", "      <td>5.3210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-19</th>\n", "      <td>5.3145</td>\n", "      <td>5.3145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-26</th>\n", "      <td>5.3208</td>\n", "      <td>5.3208</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02-02</th>\n", "      <td>5.3120</td>\n", "      <td>5.3120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02-09</th>\n", "      <td>5.3120</td>\n", "      <td>5.3120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02-16</th>\n", "      <td>5.3078</td>\n", "      <td>5.3078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02-23</th>\n", "      <td>5.3126</td>\n", "      <td>5.3126</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-01</th>\n", "      <td>5.3122</td>\n", "      <td>5.3122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-08</th>\n", "      <td>5.3076</td>\n", "      <td>5.3076</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-15</th>\n", "      <td>5.3153</td>\n", "      <td>5.3153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-22</th>\n", "      <td>5.3202</td>\n", "      <td>5.3202</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-03-29</th>\n", "      <td>5.3168</td>\n", "      <td>5.3168</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04-05</th>\n", "      <td>5.3137</td>\n", "      <td>5.3137</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04-12</th>\n", "      <td>5.3095</td>\n", "      <td>5.3095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04-19</th>\n", "      <td>5.3110</td>\n", "      <td>5.3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-04-26</th>\n", "      <td>5.3095</td>\n", "      <td>5.3095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-03</th>\n", "      <td>5.3104</td>\n", "      <td>5.3104</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-10</th>\n", "      <td>5.3100</td>\n", "      <td>5.3100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-17</th>\n", "      <td>5.3110</td>\n", "      <td>5.3110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-24</th>\n", "      <td>5.3143</td>\n", "      <td>5.3143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-05-31</th>\n", "      <td>5.3141</td>\n", "      <td>5.3141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-07</th>\n", "      <td>5.3191</td>\n", "      <td>5.3191</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-14</th>\n", "      <td>5.3275</td>\n", "      <td>5.3275</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-21</th>\n", "      <td>5.3344</td>\n", "      <td>5.3344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-06-28</th>\n", "      <td>5.3259</td>\n", "      <td>5.3259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-05</th>\n", "      <td>5.3200</td>\n", "      <td>5.3200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-12</th>\n", "      <td>5.3280</td>\n", "      <td>5.3280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-19</th>\n", "      <td>5.3374</td>\n", "      <td>5.3375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-07-26</th>\n", "      <td>5.3362</td>\n", "      <td>5.3362</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-02</th>\n", "      <td>5.3130</td>\n", "      <td>5.3130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-09</th>\n", "      <td>5.3288</td>\n", "      <td>5.3288</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-16</th>\n", "      <td>5.3327</td>\n", "      <td>5.3327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-23</th>\n", "      <td>5.3412</td>\n", "      <td>5.3412</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-08-30</th>\n", "      <td>5.3355</td>\n", "      <td>5.3355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-06</th>\n", "      <td>5.3472</td>\n", "      <td>5.3472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-13</th>\n", "      <td>5.0975</td>\n", "      <td>5.0975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-20</th>\n", "      <td>4.8476</td>\n", "      <td>4.8476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-09-27</th>\n", "      <td>4.8405</td>\n", "      <td>4.8405</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-04</th>\n", "      <td>4.8355</td>\n", "      <td>4.8355</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11</th>\n", "      <td>4.8390</td>\n", "      <td>4.8390</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           USOSFR1Z Curncy        \n", "                    PX_MID PX_LAST\n", "2024-01-05          5.3266  5.3266\n", "2024-01-12          5.3210  5.3210\n", "2024-01-19          5.3145  5.3145\n", "2024-01-26          5.3208  5.3208\n", "2024-02-02          5.3120  5.3120\n", "2024-02-09          5.3120  5.3120\n", "2024-02-16          5.3078  5.3078\n", "2024-02-23          5.3126  5.3126\n", "2024-03-01          5.3122  5.3122\n", "2024-03-08          5.3076  5.3076\n", "2024-03-15          5.3153  5.3153\n", "2024-03-22          5.3202  5.3202\n", "2024-03-29          5.3168  5.3168\n", "2024-04-05          5.3137  5.3137\n", "2024-04-12          5.3095  5.3095\n", "2024-04-19          5.3110  5.3110\n", "2024-04-26          5.3095  5.3095\n", "2024-05-03          5.3104  5.3104\n", "2024-05-10          5.3100  5.3100\n", "2024-05-17          5.3110  5.3110\n", "2024-05-24          5.3143  5.3143\n", "2024-05-31          5.3141  5.3141\n", "2024-06-07          5.3191  5.3191\n", "2024-06-14          5.3275  5.3275\n", "2024-06-21          5.3344  5.3344\n", "2024-06-28          5.3259  5.3259\n", "2024-07-05          5.3200  5.3200\n", "2024-07-12          5.3280  5.3280\n", "2024-07-19          5.3374  5.3375\n", "2024-07-26          5.3362  5.3362\n", "2024-08-02          5.3130  5.3130\n", "2024-08-09          5.3288  5.3288\n", "2024-08-16          5.3327  5.3327\n", "2024-08-23          5.3412  5.3412\n", "2024-08-30          5.3355  5.3355\n", "2024-09-06          5.3472  5.3472\n", "2024-09-13          5.0975  5.0975\n", "2024-09-20          4.8476  4.8476\n", "2024-09-27          4.8405  4.8405\n", "2024-10-04          4.8355  4.8355\n", "2024-10-11          4.8390  4.8390"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers='USOSFR1Z Curncy', flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'], start_date='2024-01-01',end_date='2024-10-18', Per='W')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">USSOSR1 Curncy</th>\n", "      <th colspan=\"2\" halign=\"left\">SOFRRATE Index</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-12-16</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-17</th>\n", "      <td>0.063</td>\n", "      <td>0.063</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           USSOSR1 Curncy         SOFRRATE Index        \n", "                   PX_MID PX_LAST         PX_MID PX_LAST\n", "2020-12-16            NaN     NaN           0.09    0.09\n", "2020-12-17          0.063   0.063           0.09    0.09"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdh(tickers=['USSOSR1 Curncy','SOFRRATE Index'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'], start_date=dt.date(2020,12,16),end_date='2020-12-17')\n", "data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["original_data = data.copy()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>SOFRRATE Index</th>\n", "      <th>SOFRRATE Index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-12-16</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-17</th>\n", "      <td>0.063</td>\n", "      <td>0.063</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            USSOSR1 Curncy  USSOSR1 Curncy  SOFRRATE Index  SOFRRATE Index\n", "2020-12-16             NaN             NaN            0.09            0.09\n", "2020-12-17           0.063           0.063            0.09            0.09"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data.droplevel(1, axis=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR1 Curncy_PX_LAST</th>\n", "      <th>SOFRRATE Index</th>\n", "      <th>SOFRRATE Index_PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-12-16</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-17</th>\n", "      <td>0.063</td>\n", "      <td>0.063</td>\n", "      <td>0.09</td>\n", "      <td>0.09</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            USSOSR1 Curncy  USSOSR1 Curncy_PX_LAST  SOFRRATE Index  \\\n", "2020-12-16             NaN                     NaN            0.09   \n", "2020-12-17           0.063                   0.063            0.09   \n", "\n", "            SOFRRATE Index_PX_LAST  \n", "2020-12-16                    0.09  \n", "2020-12-17                    0.09  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["tickers = data.columns.get_level_values(0)\n", "second_levels = data.columns.get_level_values(1)\n", "\n", "# Create the new column names using vectorized operations\n", "new_columns = tickers + second_levels.map({\"PX_MID\": \"\", \"PX_LAST\": \"_PX_LAST\"})\n", "\n", "# Assign the new column names to the DataFrame\n", "data.columns = new_columns\n", "\n", "# Return the DataFrame with updated column names\n", "data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.date(2020, 12, 16)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data.index[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: []"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["data = blp.bdh(tickers=['USSOSR1 Curncy','USSOSR2 Curncy'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'], start_date=dt.date(2020,12,19),end_date='2020-12-20')\n", "data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["data = blp.bdh(tickers=['0QZ21 Comdty','0QZ1 Comdty'], flds=['Security_Name','PX_MID','PX_LAST'], start_date=dt.date(2022,3,1),end_date='2022-03-02')\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">0QZ21 Comdty</th>\n", "      <th colspan=\"2\" halign=\"left\">0QZ1 Comdty</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2022-03-01</th>\n", "      <td>99.946</td>\n", "      <td>99.948</td>\n", "      <td>17.5863</td>\n", "      <td>17.5863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2022-03-02</th>\n", "      <td>99.949</td>\n", "      <td>99.948</td>\n", "      <td>18.0391</td>\n", "      <td>18.0391</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           0QZ21 Comdty         0QZ1 Comdty         \n", "                 PX_MID PX_LAST      PX_MID  PX_LAST\n", "2022-03-01       99.946  99.948     17.5863  17.5863\n", "2022-03-02       99.949  99.948     18.0391  18.0391"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>JYSOMPM3 Curncy</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-07-01</th>\n", "      <td>-0.07500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-02</th>\n", "      <td>-0.07375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-03</th>\n", "      <td>-0.07375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-06</th>\n", "      <td>-0.07375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-07</th>\n", "      <td>-0.07000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-08</th>\n", "      <td>-0.07250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-09</th>\n", "      <td>-0.07188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-10</th>\n", "      <td>-0.07250</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           JYSOMPM3 Curncy\n", "                   PX_LAST\n", "2020-07-01        -0.07500\n", "2020-07-02        -0.07375\n", "2020-07-03        -0.07375\n", "2020-07-06        -0.07375\n", "2020-07-07        -0.07000\n", "2020-07-08        -0.07250\n", "2020-07-09        -0.07188\n", "2020-07-10        -0.07250"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers=['JYSOMPM3 Curncy','JYSOMPM4 Curncy','JYSOMPM5 Curncy','JYSOMPM7 Curncy'], flds=['PX_LAST'], start_date=dt.date(2020,7,1),end_date='2020-07-10')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">USOSFR3Z Curncy</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_MID</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-10</th>\n", "      <td>4.8460</td>\n", "      <td>4.8460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11</th>\n", "      <td>4.8435</td>\n", "      <td>4.8435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-14</th>\n", "      <td>4.8435</td>\n", "      <td>4.8435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8500</td>\n", "      <td>4.8500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8510</td>\n", "      <td>4.8510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8177</td>\n", "      <td>4.8177</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8135</td>\n", "      <td>4.8135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8065</td>\n", "      <td>4.8065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-22</th>\n", "      <td>4.7915</td>\n", "      <td>4.7915</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-23</th>\n", "      <td>4.7852</td>\n", "      <td>4.7852</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-24</th>\n", "      <td>4.7485</td>\n", "      <td>4.7485</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           USOSFR3Z Curncy        \n", "                    PX_MID PX_LAST\n", "2024-10-10          4.8460  4.8460\n", "2024-10-11          4.8435  4.8435\n", "2024-10-14          4.8435  4.8435\n", "2024-10-15          4.8500  4.8500\n", "2024-10-16          4.8510  4.8510\n", "2024-10-17          4.8177  4.8177\n", "2024-10-18          4.8135  4.8135\n", "2024-10-21          4.8065  4.8065\n", "2024-10-22          4.7915  4.7915\n", "2024-10-23          4.7852  4.7852\n", "2024-10-24          4.7485  4.7485"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdh(tickers=['USOSFR3Z Curncy'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'], start_date=dt.date(2024,10,10),end_date='2024-10-24')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>security_name</th>\n", "      <th>sw_eff_dt</th>\n", "      <th>maturity</th>\n", "      <th>px_mid</th>\n", "      <th>px_last</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSOSR1 Curncy</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>4.6477</td>\n", "      <td>4.6477</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR2 Curncy</th>\n", "      <td>US DOLLAR/US DOLLAR US DOLLAR</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>4.466</td>\n", "      <td>4.466</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                security_name   sw_eff_dt    maturity  px_mid  \\\n", "USSOSR1 Curncy  US DOLLAR/US DOLLAR US DOLLAR  2024-11-07  2024-12-18  4.6477   \n", "USSOSR2 Curncy  US DOLLAR/US DOLLAR US DOLLAR  2024-12-18  2025-01-29   4.466   \n", "\n", "               px_last  \n", "USSOSR1 Curncy  4.6477  \n", "USSOSR2 C<PERSON>cy   4.466  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["blp.bdp(tickers=['USSOSR1 Curncy','USSOSR2 Curncy'], flds=['Security_Name','SW_EFF_DT','MATURITY','PX_MID','PX_LAST'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>px_mid</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSOSR1 Curncy</th>\n", "      <td>4.2439</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR2 Curncy</th>\n", "      <td>4.0490</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                px_mid\n", "USSOSR1 Curncy  4.2439\n", "USSOSR2 Curncy  4.0490"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["singleData = blp.bdp(tickers=['USSOSR1 Curncy','USSOSR2 Curncy'], flds=['PX_MID'])\n", "singleData"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'dt' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m pd\u001b[38;5;241m.\u001b[39mconcat({\u001b[43mdt\u001b[49m\u001b[38;5;241m.\u001b[39mdate\u001b[38;5;241m.\u001b[39mtoday(): singleData}, names\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[1;31mNameError\u001b[0m: name 'dt' is not defined"]}], "source": ["import pandas as pd\n", "pd.concat({dt.date.today(): singleData}, names=['date'])"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["import QuantLib as ql\n", "ql_date = ql.Date.todaysDate()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>px_mid</th>\n", "      <td>4.2439</td>\n", "    </tr>\n", "    <tr>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>px_mid</th>\n", "      <td>4.0490</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        price\n", "USSOSR1 Curncy px_mid  4.2439\n", "USSOSR2 Curncy px_mid  4.0490"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["singleData.stack().to_frame(name=\"price\").dropna()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.658</td>\n", "      <td>4.501</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            USSOSR1 Curncy  USSOSR2 Curncy\n", "2024-10-21           4.658           4.501"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["singleData.columns = [dt.date.today()]\n", "singleData.T"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["USSOSR1 Curncy    4.658\n", "USSOSR2 Curncy    4.501\n", "Name: 2024-10-21, dtype: float64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["singleData.squeeze()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'xbbg.blp' has no attribute 'fld_info'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m blp\u001b[38;5;241m.\u001b[39mfld_info()\n", "\u001b[1;31mAttributeError\u001b[0m: module 'xbbg.blp' has no attribute 'fld_info'"]}], "source": ["blp.fld_info()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}