import pandas as pd
import plotly.graph_objects as go
import QuantLib as ql
import datetime as dt
from dateutil.relativedelta import relativedelta
from curves.curve import Curve
from helpers.date_helpers import to_ql_date
from typing import List

pd.options.plotting.backend = "plotly"


def strp_date(d: str, reference_date: ql.Date) -> ql.Date:
    if not d:
        return reference_date
    elif d[-1].isalpha():
        return reference_date + ql.Period(d)
    else:
        return to_ql_date(pd.to_datetime(d).date())


def plot_curves(
    curves: Curve | List[Curve],
    start: str = "",
    end: str = "30y",
    forwardTerm: str = "1d",
) -> None:
    fig = plot_curves_fig(curves, start, end, forwardTerm)
    fig.show()


def plot_curves_fig(
    curves: Curve | List[Curve],
    start: str = "",
    end: str = "30y",
    forwardTerm: str = "1d",
) -> go.Figure:
    r"""
    Parameters:
    start (str): The start period of the curve:
                   - '18M': Represents 18 months
                   - '1y':  Represents 1 year
                   - '3w':  Represents 3 weeks
    end (str): The end period of the curve.
    """
    if not isinstance(curves, list):
        curves = [curves]
    fig = go.Figure()
    min_rate, max_rate = 0.0, 0.0
    min_date = dt.date(3000, 1, 1)
    max_date = dt.date(1900, 1, 1)
    for curve in curves:
        reference_date = to_ql_date(curve.date)
        ql.Settings.instance().evaluationDate = reference_date
        start_date = strp_date(start, reference_date)
        end_date = strp_date(end, reference_date) - ql.Period(forwardTerm)

        dates = [
            date
            for serial in range(start_date.serialNumber(), end_date.serialNumber() + 1)
            if curve.fixed_calendar.isBusinessDay(date := ql.Date(serial))
        ]
        rates = [
            curve.curve.forwardRate(
                d,
                curve.fixed_calendar.advance(d, ql.Period(forwardTerm)),
                curve.index.dayCounter(),
                ql.Simple,
            ).rate()
            for d in dates
        ]

        dates = [d.to_date() for d in dates]
        min_date, max_date = min(dates[0], min_date), max(dates[-1], max_date)
        min_rate, max_rate = (
            min(min(rates), min_rate) - 5 / 10000,
            max(max(rates), max_rate) + 5 / 10000,
        )

        fig.add_trace(
            go.Scatter(
                x=dates,
                y=rates,
                mode="lines",
                name=f"{curve.curve_name}: {curve.context_key}",
                hovertemplate=(
                    f"{curve.curve_name}<br>"
                    "Date: %{x|%Y-%m-%d}<br>"
                    "Rate: %{y:.4%}<extra></extra>"
                ),
            )
        )

    fig.update_layout(
        title=dict(
            text=f"{forwardTerm} Forward Curve",
            x=0.5,
            xanchor="center",
            font=dict(size=30),
        ),
        height=720,
        xaxis_title="Date",
        yaxis_title="Rate (%)",
        xaxis=dict(
            type="date",
            tickformat="%Y-%m",
            ticklabelmode="period",
            range=[min_date, min(min_date + relativedelta(years=5), max_date)],
            rangeslider=dict(
                visible=True,
                range=[min_date, max_date],
            ),
        ),
        yaxis=dict(tickformat=".2%", range=[min_rate, max_rate], fixedrange=False),
        template="plotly",
        showlegend=True,
    )

    return fig


def plot_discount_curves(
    curves: Curve | List[Curve],
    start: str = "",
    end: str = "30y",
    forwardTerm: str = "1d",
) -> go.Figure:
    r"""
    Parameters:
    start (str): The start period of the curve:
                   - '18M': Represents 18 months
                   - '1y':  Represents 1 year
                   - '3w':  Represents 3 weeks
    end (str): The end period of the curve.
    """
    if not isinstance(curves, list):
        curves = [curves]
    fig = go.Figure()
    for curve in curves:
        reference_date = to_ql_date(curve.date)
        ql.Settings.instance().evaluationDate = reference_date
        start_date = strp_date(start, reference_date)
        end_date = strp_date(end, reference_date) - ql.Period(forwardTerm)
        dates = [
            ql.Date(serial)
            for serial in range(start_date.serialNumber(), end_date.serialNumber() + 1)
        ]
        dcfs = [curve.curve.discount(d) for d in dates]

        dates = [d.to_date() for d in dates]

        fig.add_trace(
            go.Scatter(
                x=dates,
                y=dcfs,
                mode="lines",
                name=f"{curve.curve_name}: {curve.context_key}",
                hovertemplate=(
                    f"{curve.curve_name}<br>"
                    "Date: %{x|%B %d, %Y}<br>"
                    "Discount Factor: %{y:.8f}<extra></extra>"
                ),
            )
        )

    fig.update_layout(
        title=dict(
            text="Discount Curve",
            x=0.5,
            xanchor="center",
            font=dict(size=30),
        ),
        xaxis_title="Date",
        yaxis_title="Discount Factor",
        xaxis=dict(type="date", tickformat="%Y-%m", ticklabelmode="period"),
        yaxis=dict(tickformat=".2f", range=[0, 1.005]),
        template="plotly",
    )

    fig.show()


def plot_series(
    data: pd.DataFrame,
) -> None:
    fig = plot_series_fig(data)
    fig.show()


def plot_series_fig(
    data: pd.DataFrame,
) -> go.Figure:
    fig = go.Figure()
    for column in data.columns:
        fig.add_trace(
            go.Scatter(
                x=data.index,
                y=data[column],
                mode="lines",
                name=column,
                hovertemplate=f"{column}<br>"
                "Date: %{x|%Y-%m-%d}<br>"
                "Rate: %{y:.4%}<extra></extra>",
            )
        )

    fig.update_layout(
        title=dict(
            text="Rate History",
            x=0.5,
            xanchor="center",
            font=dict(size=30),
        ),
        xaxis_title="Date",
        yaxis_title="Forward Rate",
        xaxis=dict(type="date", tickformat="%Y-%m", ticklabelmode="period"),
        yaxis=dict(tickformat=".2%"),
        template="plotly_white",
        showlegend=True,
    )
    return fig


def plot_xy(
    data: pd.DataFrame,
) -> None:
    fig = plot_xy_fig(data)
    fig.show()


def plot_xy_fig(
    data: pd.DataFrame,
) -> go.Figure:
    fig = go.Figure()
    x_column, y_column = data.columns
    min_val, max_val = (
        min(min(data[x_column]), min(data[y_column])) - 5 / 10000,
        max(max(data[x_column]), max(data[y_column])) + 5 / 10000,
    )
    fig.add_trace(
        go.Scatter(
            x=data[x_column],
            y=data[y_column],
            mode="markers",
            marker=dict(size=6),
            hovertemplate=(
                "Date: %{customdata}<br>"
                f"y: " + "%{y:.4%}<br>"
                f"x: " + "%{x:.4%}<extra></extra>"
            ),
            customdata=pd.to_datetime(data.index).strftime("%Y-%m-%d"),
        )
    )
    fig.update_layout(
        shapes=[
            {
                "type": "line",
                "yref": "paper",
                "xref": "paper",
                "y0": 0,
                "y1": 1,
                "x0": 0,
                "x1": 1,
                "line": {
                    "color": "rgba(128, 128, 128, 0.5)",
                    "width": 1,
                    "dash": "longdash",
                },
            }
        ],
        xaxis=dict(scaleanchor="y"),
        yaxis=dict(scaleanchor="x"),
    )

    fig.update_layout(
        title=dict(
            text="",
            x=0.5,
            xanchor="center",
            font=dict(size=30),
        ),
        width=900,
        height=900,
        xaxis_title=x_column,
        yaxis_title=y_column,
        xaxis=dict(tickformat=".2%", scaleanchor="y", range=[min_val, max_val]),
        yaxis=dict(tickformat=".2%", scaleanchor="x", range=[min_val, max_val]),
        template="plotly",
    )
    return fig
