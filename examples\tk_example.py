import tkinter as tk
from tkinter import messagebox

in_jupyter = True
try:
    get_ipython
    in_jupyter = True
except NameError:
    in_jupyter = False
print("in jupyter or not:", in_jupyter)

choice = messagebox.askquestion("Yes/No", "Are you sure?", icon="warning")
print(choice)

# proceed = messagebox.askyesno("Confirmation", "Do you want to proceed?")

# # Use the user's response...
# if proceed:
#     print("User chose Yes!")
# else:
#     print("User chose No!")

proceed2 = messagebox.askyesnocancel(
    "Warning",
    "You are about to download more than 12M of Underlying Futures data:, \n Do you want to proceed?",
)
print(proceed2)
if proceed2 is None:
    print("User chose Cancel!")
elif proceed2:
    print("User chose Yes!")
else:
    print("User chose No!")
