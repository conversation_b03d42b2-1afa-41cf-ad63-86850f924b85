{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "import datetime as dt\n", "import pandas as pd\n", "import QuantLib as ql\n", "from loader.curve_calibrator import curveCalibrator\n", "from helpers.date_helpers import to_ql_date\n", "from helpers.plot_tools import plot_series, plot_xy\n", "from curves import sofrCurve"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sofr_calibrator = curveCalibrator(\"USD.SOFR\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-01, 2024-09-29]\n", "Loading close price from 2024-01-01\n", "Opening cached curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\n", "Opening cached calibration results: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_calibration_results\\USD_SOFR_20240101_calib_results_marketclose.parquet\n"]}], "source": ["sofr_calibrator.recalibrate(dt.date(2024,1,1), dt.date(2024,9,29), False)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>45</th>\n", "      <th>46</th>\n", "      <th>47</th>\n", "      <th>48</th>\n", "      <th>49</th>\n", "      <th>50</th>\n", "      <th>51</th>\n", "      <th>52</th>\n", "      <th>53</th>\n", "      <th>54</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>8</td>\n", "      <td>2024-01-02</td>\n", "      <td>2024-01-31</td>\n", "      <td>2024-03-20</td>\n", "      <td>2024-05-01</td>\n", "      <td>2024-06-12</td>\n", "      <td>2024-07-31</td>\n", "      <td>2024-09-18</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>...</td>\n", "      <td>0.7304257184254949</td>\n", "      <td>0.7052976337238427</td>\n", "      <td>0.6806346108611827</td>\n", "      <td>0.6565854646576645</td>\n", "      <td>0.5891995916479108</td>\n", "      <td>0.4967817293405249</td>\n", "      <td>0.4294982039911859</td>\n", "      <td>0.3764795448827378</td>\n", "      <td>0.3083914790060052</td>\n", "      <td>0.2702001371004585</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>8</td>\n", "      <td>2024-01-03</td>\n", "      <td>2024-01-31</td>\n", "      <td>2024-03-20</td>\n", "      <td>2024-05-01</td>\n", "      <td>2024-06-12</td>\n", "      <td>2024-07-31</td>\n", "      <td>2024-09-18</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>...</td>\n", "      <td>0.7300258106120231</td>\n", "      <td>0.7048107467420348</td>\n", "      <td>0.6799773917105683</td>\n", "      <td>0.6555926712414656</td>\n", "      <td>0.5876843606724611</td>\n", "      <td>0.4943973257239913</td>\n", "      <td>0.4263872457149352</td>\n", "      <td>0.3728231478200170</td>\n", "      <td>0.3047642978908557</td>\n", "      <td>0.2666606123141487</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>8</td>\n", "      <td>2024-01-04</td>\n", "      <td>2024-01-31</td>\n", "      <td>2024-03-20</td>\n", "      <td>2024-05-01</td>\n", "      <td>2024-06-12</td>\n", "      <td>2024-07-31</td>\n", "      <td>2024-09-18</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>...</td>\n", "      <td>0.7239988340571646</td>\n", "      <td>0.6984235421368339</td>\n", "      <td>0.6733372992449604</td>\n", "      <td>0.6488436921684327</td>\n", "      <td>0.5800185637797967</td>\n", "      <td>0.4861728179109742</td>\n", "      <td>0.4178549395285065</td>\n", "      <td>0.3642655850940540</td>\n", "      <td>0.2956367567382706</td>\n", "      <td>0.2587173811026122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>8</td>\n", "      <td>2024-01-05</td>\n", "      <td>2024-01-31</td>\n", "      <td>2024-03-20</td>\n", "      <td>2024-05-01</td>\n", "      <td>2024-06-12</td>\n", "      <td>2024-07-31</td>\n", "      <td>2024-09-18</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>...</td>\n", "      <td>0.7215635326154468</td>\n", "      <td>0.6956290995316525</td>\n", "      <td>0.6702481811000079</td>\n", "      <td>0.6455169968632106</td>\n", "      <td>0.5762889633650763</td>\n", "      <td>0.4819795122575675</td>\n", "      <td>0.4134807530003328</td>\n", "      <td>0.3599376946264896</td>\n", "      <td>0.2910788845207846</td>\n", "      <td>0.2531780943327013</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>8</td>\n", "      <td>2024-01-08</td>\n", "      <td>2024-01-31</td>\n", "      <td>2024-03-20</td>\n", "      <td>2024-05-01</td>\n", "      <td>2024-06-12</td>\n", "      <td>2024-07-31</td>\n", "      <td>2024-09-18</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>...</td>\n", "      <td>0.7212360002035486</td>\n", "      <td>0.6951880808024453</td>\n", "      <td>0.6698041775969512</td>\n", "      <td>0.6449856174027878</td>\n", "      <td>0.5757014300581140</td>\n", "      <td>0.4814255355392437</td>\n", "      <td>0.4127758849528084</td>\n", "      <td>0.3593933959366831</td>\n", "      <td>0.2896009191691322</td>\n", "      <td>0.2523600470717889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-25</th>\n", "      <td>8</td>\n", "      <td>2024-10-25</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-09-17</td>\n", "      <td>...</td>\n", "      <td>0.7141093176936544</td>\n", "      <td>0.6870332721896097</td>\n", "      <td>0.6606672101091358</td>\n", "      <td>0.6348396539076885</td>\n", "      <td>0.5633030969723212</td>\n", "      <td>0.4660704074547725</td>\n", "      <td>0.3949255622889839</td>\n", "      <td>0.3405802524889747</td>\n", "      <td>0.2681259170377794</td>\n", "      <td>0.2245593312484464</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>8</td>\n", "      <td>2024-10-28</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-09-17</td>\n", "      <td>...</td>\n", "      <td>0.7122985709380411</td>\n", "      <td>0.6851460320659225</td>\n", "      <td>0.6585837516500234</td>\n", "      <td>0.6327450681866070</td>\n", "      <td>0.5611593891088649</td>\n", "      <td>0.4637780567815241</td>\n", "      <td>0.3926349824430440</td>\n", "      <td>0.3382736142049683</td>\n", "      <td>0.2659355840905576</td>\n", "      <td>0.2221203640647308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>8</td>\n", "      <td>2024-10-29</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-09-17</td>\n", "      <td>...</td>\n", "      <td>0.7142709234550548</td>\n", "      <td>0.6870505659478104</td>\n", "      <td>0.6605564626346104</td>\n", "      <td>0.6347403550427534</td>\n", "      <td>0.5633230868439117</td>\n", "      <td>0.4661195663565391</td>\n", "      <td>0.3950752962863557</td>\n", "      <td>0.3407388926160062</td>\n", "      <td>0.2683258398352471</td>\n", "      <td>0.2245345703561698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>8</td>\n", "      <td>2024-10-30</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-09-17</td>\n", "      <td>...</td>\n", "      <td>0.7116360269417061</td>\n", "      <td>0.6846715221787423</td>\n", "      <td>0.6583988931915022</td>\n", "      <td>0.6326499966022885</td>\n", "      <td>0.5620263518302352</td>\n", "      <td>0.4655969756565062</td>\n", "      <td>0.3951941642348840</td>\n", "      <td>0.3415144550189103</td>\n", "      <td>0.2688009244638242</td>\n", "      <td>0.2247594884015164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>8</td>\n", "      <td>2024-10-31</td>\n", "      <td>2024-11-07</td>\n", "      <td>2024-12-18</td>\n", "      <td>2025-01-29</td>\n", "      <td>2025-03-19</td>\n", "      <td>2025-05-07</td>\n", "      <td>2025-06-18</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-09-17</td>\n", "      <td>...</td>\n", "      <td>0.7133005690241672</td>\n", "      <td>0.6865077120565407</td>\n", "      <td>0.6605328552176361</td>\n", "      <td>0.6352324340702056</td>\n", "      <td>0.5648741390824474</td>\n", "      <td>0.4689629678954763</td>\n", "      <td>0.3989757628395159</td>\n", "      <td>0.3454353231589464</td>\n", "      <td>0.2732403537129020</td>\n", "      <td>0.2289065001483435</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>209 rows × 55 columns</p>\n", "</div>"], "text/plain": ["            0           1           2           3           4           5   \\\n", "2024-01-02   8  2024-01-02  2024-01-31  2024-03-20  2024-05-01  2024-06-12   \n", "2024-01-03   8  2024-01-03  2024-01-31  2024-03-20  2024-05-01  2024-06-12   \n", "2024-01-04   8  2024-01-04  2024-01-31  2024-03-20  2024-05-01  2024-06-12   \n", "2024-01-05   8  2024-01-05  2024-01-31  2024-03-20  2024-05-01  2024-06-12   \n", "2024-01-08   8  2024-01-08  2024-01-31  2024-03-20  2024-05-01  2024-06-12   \n", "...         ..         ...         ...         ...         ...         ...   \n", "2024-10-25   8  2024-10-25  2024-11-07  2024-12-18  2025-01-29  2025-03-19   \n", "2024-10-28   8  2024-10-28  2024-11-07  2024-12-18  2025-01-29  2025-03-19   \n", "2024-10-29   8  2024-10-29  2024-11-07  2024-12-18  2025-01-29  2025-03-19   \n", "2024-10-30   8  2024-10-30  2024-11-07  2024-12-18  2025-01-29  2025-03-19   \n", "2024-10-31   8  2024-10-31  2024-11-07  2024-12-18  2025-01-29  2025-03-19   \n", "\n", "                    6           7           8           9   ...  \\\n", "2024-01-02  2024-07-31  2024-09-18  2024-11-07  2024-12-18  ...   \n", "2024-01-03  2024-07-31  2024-09-18  2024-11-07  2024-12-18  ...   \n", "2024-01-04  2024-07-31  2024-09-18  2024-11-07  2024-12-18  ...   \n", "2024-01-05  2024-07-31  2024-09-18  2024-11-07  2024-12-18  ...   \n", "2024-01-08  2024-07-31  2024-09-18  2024-11-07  2024-12-18  ...   \n", "...                ...         ...         ...         ...  ...   \n", "2024-10-25  2025-05-07  2025-06-18  2025-07-30  2025-09-17  ...   \n", "2024-10-28  2025-05-07  2025-06-18  2025-07-30  2025-09-17  ...   \n", "2024-10-29  2025-05-07  2025-06-18  2025-07-30  2025-09-17  ...   \n", "2024-10-30  2025-05-07  2025-06-18  2025-07-30  2025-09-17  ...   \n", "2024-10-31  2025-05-07  2025-06-18  2025-07-30  2025-09-17  ...   \n", "\n", "                           45                 46                 47  \\\n", "2024-01-02 0.7304257184254949 0.7052976337238427 0.6806346108611827   \n", "2024-01-03 0.7300258106120231 0.7048107467420348 0.6799773917105683   \n", "2024-01-04 0.7239988340571646 0.6984235421368339 0.6733372992449604   \n", "2024-01-05 0.7215635326154468 0.6956290995316525 0.6702481811000079   \n", "2024-01-08 0.7212360002035486 0.6951880808024453 0.6698041775969512   \n", "...                       ...                ...                ...   \n", "2024-10-25 0.7141093176936544 0.6870332721896097 0.6606672101091358   \n", "2024-10-28 0.7122985709380411 0.6851460320659225 0.6585837516500234   \n", "2024-10-29 0.7142709234550548 0.6870505659478104 0.6605564626346104   \n", "2024-10-30 0.7116360269417061 0.6846715221787423 0.6583988931915022   \n", "2024-10-31 0.7133005690241672 0.6865077120565407 0.6605328552176361   \n", "\n", "                           48                 49                 50  \\\n", "2024-01-02 0.6565854646576645 0.5891995916479108 0.4967817293405249   \n", "2024-01-03 0.6555926712414656 0.5876843606724611 0.4943973257239913   \n", "2024-01-04 0.6488436921684327 0.5800185637797967 0.4861728179109742   \n", "2024-01-05 0.6455169968632106 0.5762889633650763 0.4819795122575675   \n", "2024-01-08 0.6449856174027878 0.5757014300581140 0.4814255355392437   \n", "...                       ...                ...                ...   \n", "2024-10-25 0.6348396539076885 0.5633030969723212 0.4660704074547725   \n", "2024-10-28 0.6327450681866070 0.5611593891088649 0.4637780567815241   \n", "2024-10-29 0.6347403550427534 0.5633230868439117 0.4661195663565391   \n", "2024-10-30 0.6326499966022885 0.5620263518302352 0.4655969756565062   \n", "2024-10-31 0.6352324340702056 0.5648741390824474 0.4689629678954763   \n", "\n", "                           51                 52                 53  \\\n", "2024-01-02 0.4294982039911859 0.3764795448827378 0.3083914790060052   \n", "2024-01-03 0.4263872457149352 0.3728231478200170 0.3047642978908557   \n", "2024-01-04 0.4178549395285065 0.3642655850940540 0.2956367567382706   \n", "2024-01-05 0.4134807530003328 0.3599376946264896 0.2910788845207846   \n", "2024-01-08 0.4127758849528084 0.3593933959366831 0.2896009191691322   \n", "...                       ...                ...                ...   \n", "2024-10-25 0.3949255622889839 0.3405802524889747 0.2681259170377794   \n", "2024-10-28 0.3926349824430440 0.3382736142049683 0.2659355840905576   \n", "2024-10-29 0.3950752962863557 0.3407388926160062 0.2683258398352471   \n", "2024-10-30 0.3951941642348840 0.3415144550189103 0.2688009244638242   \n", "2024-10-31 0.3989757628395159 0.3454353231589464 0.2732403537129020   \n", "\n", "                           54  \n", "2024-01-02 0.2702001371004585  \n", "2024-01-03 0.2666606123141487  \n", "2024-01-04 0.2587173811026122  \n", "2024-01-05 0.2531780943327013  \n", "2024-01-08 0.2523600470717889  \n", "...                       ...  \n", "2024-10-25 0.2245593312484464  \n", "2024-10-28 0.2221203640647308  \n", "2024-10-29 0.2245345703561698  \n", "2024-10-30 0.2247594884015164  \n", "2024-10-31 0.2289065001483435  \n", "\n", "[209 rows x 55 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option('display.float_format', '{:.16f}'.format)\n", "loaded_calibration_results = sofr_calibrator.load(dt.date(2024,1,1),dt.date(2024,11,7))\n", "loaded_calibration_results[0]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(209, 55)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["loaded_calibration_results[0].shape"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(53,)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["loaded_calibration_results[0].loc[dt.date(2024,1,31)].dropna().shape"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["0                     8\n", "1            2024-10-01\n", "2            2024-11-07\n", "3            2024-12-18\n", "4            2025-01-29\n", "5            2025-03-19\n", "6            2025-05-07\n", "7            2025-06-18\n", "8            2025-07-30\n", "9            2025-09-17\n", "10           2026-04-06\n", "11           2026-10-05\n", "12           2027-10-04\n", "13           2028-10-03\n", "14           2029-10-03\n", "15           2030-10-03\n", "16           2031-10-03\n", "17           2032-10-04\n", "18           2033-10-03\n", "19           2034-10-03\n", "20           2035-10-03\n", "21           2036-10-03\n", "22           2039-10-03\n", "23           2044-10-03\n", "24           2049-10-04\n", "25           2054-10-05\n", "26           2064-10-03\n", "27           2074-10-03\n", "28   1.0000000000000000\n", "29   0.9950306028001958\n", "30   0.9899261147844405\n", "31   0.9851037037865613\n", "32   0.9799538015709228\n", "33   0.9752060865877988\n", "34   0.9713835304980898\n", "35   0.9677423997192021\n", "36   0.9636598947277490\n", "37   0.9481370027074812\n", "38   0.9342018666928866\n", "39   0.9068687818829966\n", "40   0.8796789089069790\n", "41   0.8526301491195788\n", "42   0.8257358534988838\n", "43   0.7991820963840947\n", "44   0.7727257348089374\n", "45   0.7469133490493234\n", "46   0.7214581375314929\n", "47   0.6964213061648956\n", "48   0.6718430203622825\n", "49   0.6030588334471121\n", "50   0.5076073178570395\n", "51   0.4368529791287405\n", "52   0.3821251763355478\n", "53   0.3108195546683128\n", "54   0.2669655406403368\n", "Name: 2024-10-01, dtype: object"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["single_curve_data = loaded_calibration_results[0].iloc[0,:]\n", "single_curve_data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["27"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["total_pillar_size = loaded_calibration_results[0].shape[1]//2\n", "total_pillar_size"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["sofr_curve = sofrCurve(\"20241001\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def calculate(curve_data):\n", "    flat_pillar_size = int(curve_data[0])\n", "    dates = [to_ql_date(date) for date in curve_data[1: total_pillar_size+1]]\n", "    discount_factors = curve_data[total_pillar_size+1:].to_list()\n", "    curve = ql.LogMixedLinearCubicDiscountCurve(dates,discount_factors,sofr_curve.day_counter,sofr_curve.calendar,ql.LogMixedLinearCubic(n=flat_pillar_size,behavior=ql.MixedInterpolation.SplitRanges))\n", "    forward_rate = curve.forwardRate(to_ql_date(curve_data.name), sofr_curve.calendar.advance(to_ql_date(curve_data.name), ql.Period(\"5Y\")), sofr_curve.day_counter, ql.Simple)\n", "    return [forward_rate.rate()]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from pricer.forward_rate import forwardRatePricer\n", "forwards_pricer = forwardRatePricer(\"USD.SOFR\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USD_SOFR: 5y</th>\n", "      <th>USD_SOFR: 10y5y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>0.0383866558064411</td>\n", "      <td>0.0388472597622740</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>0.0384462253328811</td>\n", "      <td>0.0392917505371536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>0.0394771211646335</td>\n", "      <td>0.0402203140408246</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>0.0397423236065036</td>\n", "      <td>0.0408012005838889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>0.0398372928517384</td>\n", "      <td>0.0409205814915635</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-25</th>\n", "      <td>0.0401924911682268</td>\n", "      <td>0.0432771971555747</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>0.0406364442729421</td>\n", "      <td>0.0435364106552664</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>0.0401386412250059</td>\n", "      <td>0.0433042716353861</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>0.0410171229407092</td>\n", "      <td>0.0430242675472629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>0.0408803136603847</td>\n", "      <td>0.0425016936739868</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>209 rows × 2 columns</p>\n", "</div>"], "text/plain": ["                 USD_SOFR: 5y    USD_SOFR: 10y5y\n", "2024-01-02 0.0383866558064411 0.0388472597622740\n", "2024-01-03 0.0384462253328811 0.0392917505371536\n", "2024-01-04 0.0394771211646335 0.0402203140408246\n", "2024-01-05 0.0397423236065036 0.0408012005838889\n", "2024-01-08 0.0398372928517384 0.0409205814915635\n", "...                       ...                ...\n", "2024-10-25 0.0401924911682268 0.0432771971555747\n", "2024-10-28 0.0406364442729421 0.0435364106552664\n", "2024-10-29 0.0401386412250059 0.0433042716353861\n", "2024-10-30 0.0410171229407092 0.0430242675472629\n", "2024-10-31 0.0408803136603847 0.0425016936739868\n", "\n", "[209 rows x 2 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result = forwards_pricer.price(dt.date(2024,1,1),dt.date(2024,10,31),[\"5y\",\"10y5y\"])\n", "result"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "variable=USD_SOFR: 5y<br>index=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "USD_SOFR: 5y", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "USD_SOFR: 5y", "orientation": "v", "showlegend": true, "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "xaxis": "x", "y": [0.038386655806441065, 0.03844622533288111, 0.03947712116463352, 0.03974232360650364, 0.039837292851738385, 0.039570262964291676, 0.03949980002671596, 0.03849675650638007, 0.03800396864286523, 0.039184692183872646, 0.04030613671434469, 0.04060303944499459, 0.04075186547685684, 0.04045916648994436, 0.04056430825963972, 0.04110105591945939, 0.0402465965095976, 0.04061933770277696, 0.04000986047365958, 0.039855181709948916, 0.03826864679394356, 0.03809369617676797, 0.04002699869839285, 0.0415219181612247, 0.040588871626124096, 0.040919545720257744, 0.041499660212968095, 0.041762918141907864, 0.04172228553472355, 0.043824612598879584, 0.04299779996879186, 0.04278314758743683, 0.0433995125013497, 0.04314138528034262, 0.043727024673696005, 0.044005348417239784, 0.04327968105563881, 0.04351525010664547, 0.04369017242769449, 0.043143504449623005, 0.04307514957246121, 0.042206920893087814, 0.04265346686126302, 0.0419468037656378, 0.04166869964376258, 0.0412182673089686, 0.04094639319534859, 0.0413662680939504, 0.042026403777684886, 0.042563523345596384, 0.043685766038241376, 0.04407258182018217, 0.04420727060269943, 0.04376897801710817, 0.04326913628947443, 0.043368790186161014, 0.042557551879848575, 0.04312654819431562, 0.04303302433792195, 0.04262779147502168, 0.04292276402622128, 0.04404862675230176, 0.04426639327466854, 0.044100266496926416, 0.04384483507635656, 0.0449116313296956, 0.04531764230704285, 0.044680874947378046, 0.04734391348958015, 0.04765937484011531, 0.04676831329915844, 0.047432824354514035, 0.048371586787918516, 0.04742036488226778, 0.04811119089414386, 0.04802552424084822, 0.04778756374497333, 0.04757606164836326, 0.047946827074712116, 0.04883817296963845, 0.04849975990632446, 0.04792683319482633, 0.04880563584939313, 0.04805908007616116, 0.04712830886065789, 0.04621279085077907, 0.04599299043931217, 0.04582138147803034, 0.046157677049573964, 0.04584918442396005, 0.04639235493306718, 0.046277685434753156, 0.04566279460894473, 0.04439661952691145, 0.04503752323516278, 0.04557991399870782, 0.0457599109810382, 0.04537184025523917, 0.04568543034641696, 0.046479567791710016, 0.04632689381456271, 0.04716994789882012, 0.047730482017689695, 0.04697758306978957, 0.046249973322183585, 0.04506241097125623, 0.04430098166308495, 0.0436935305179998, 0.04379471587587439, 0.04563715979757563, 0.04585800207458494, 0.04511696455476383, 0.04395705072821117, 0.04289834019876112, 0.04273136941302407, 0.04354404658790827, 0.04286191524515081, 0.04326102060731194, 0.043156792775220686, 0.042837969087098034, 0.043094281859481935, 0.043924625579780184, 0.04344245800160551, 0.04431154920510423, 0.044941521078502224, 0.04454399051265652, 0.04370883947742104, 0.04258546742343767, 0.042699677424648044, 0.04271555478345646, 0.042556324185973646, 0.041381475809041605, 0.04093945389110508, 0.04125716544774507, 0.040612047327604775, 0.040695134072911525, 0.04120293101481923, 0.041727620216330696, 0.041729276283268626, 0.04160068955375716, 0.0415935103972492, 0.04123989083209234, 0.040695713239936274, 0.04069385909785952, 0.04020410032580761, 0.03891015030349403, 0.03804586470794255, 0.03559383596546545, 0.0358355667238466, 0.0368233718656023, 0.03710250895705158, 0.037811363856478285, 0.03742086742364604, 0.036874292987910284, 0.036033566098105894, 0.03616500930028822, 0.037424473458161064, 0.03706468527421722, 0.03698899781011057, 0.036190980017735956, 0.035896117136395796, 0.036527030778675855, 0.03574152024526776, 0.035847729802471716, 0.0356643161672942, 0.035831909409292644, 0.03603253673622972, 0.03632456912524068, 0.035589503997928075, 0.03462050707101712, 0.03448735800869534, 0.0338622419170502, 0.03378913858110971, 0.033069767682101354, 0.033307947578299586, 0.03356464910804977, 0.03329371878105623, 0.0329762915419292, 0.03343600309621562, 0.033923648189868594, 0.03393774396322235, 0.03416982205244243, 0.034222823764481033, 0.03377646555715772, 0.034355018809462526, 0.0347322942775599, 0.03408281978673208, 0.034035975329094956, 0.03455240549538277, 0.03536065590424624, 0.0373460227403156, 0.038055622099636276, 0.03779495200373147, 0.03859895568595935, 0.03821234688417013, 0.03837223770674239, 0.03782415300779225, 0.0377176669879489, 0.03835635015227738, 0.03821257826755097, 0.03946733253045059, 0.039652108504771535, 0.0402260664059889, 0.03988653586713652, 0.04019249116822682, 0.040636444272942135, 0.04013864122500594, 0.041017122940709166, 0.04088031366038466], "yaxis": "y"}, {"hovertemplate": "variable=USD_SOFR: 10y5y<br>index=%{x}<br>value=%{y}<extra></extra>", "legendgroup": "USD_SOFR: 10y5y", "line": {"color": "#EF553B", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "USD_SOFR: 10y5y", "orientation": "v", "showlegend": true, "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "xaxis": "x", "y": [0.038847259762274, 0.03929175053715356, 0.040220314040824635, 0.04080120058388886, 0.04092058149156354, 0.040895651678424065, 0.04104970953252709, 0.04086089162630433, 0.040791438879436724, 0.04224087870616305, 0.04211857172056842, 0.04295475615327075, 0.042426248528339934, 0.04248021503948919, 0.04302883532748487, 0.04354613120368461, 0.043287128198215515, 0.04309022304164405, 0.042488649928171314, 0.04150552202410612, 0.040569899320808754, 0.03987246861893561, 0.04078247398186592, 0.041975873892130124, 0.041555086762831255, 0.0419898959432961, 0.042164826598018956, 0.04231796209150803, 0.04231815160538505, 0.04305432979924935, 0.043008607041156596, 0.04276756469207335, 0.04284068466053425, 0.04323663018100561, 0.04349095812038915, 0.04311563432790008, 0.04198304091829755, 0.04229204332143237, 0.042620408479783524, 0.0423877032748337, 0.042021101859286814, 0.04142258212147365, 0.04155070124343373, 0.04078490380956073, 0.04016343383329891, 0.04033633728711027, 0.04058369281362324, 0.04053010243019636, 0.04102658432525994, 0.0411963699612242, 0.04227734525002462, 0.04204162629204783, 0.04237481443081215, 0.042527059767540565, 0.042973945185362926, 0.04262024530954728, 0.04207390595293093, 0.04259841047906676, 0.042279203244625606, 0.04165200141539561, 0.04146412199221293, 0.042536713667224474, 0.043034315181618724, 0.043338756277366754, 0.04293008544327707, 0.04367476481127089, 0.04355095415396366, 0.04293318484424155, 0.04392228265520109, 0.044788530694461814, 0.04435084027334114, 0.04532563934735911, 0.04579716232705612, 0.045199561471918975, 0.045400061352304885, 0.045105063736581956, 0.045262363376035154, 0.04554679461285961, 0.0461203700817944, 0.04650614173079425, 0.04607664462364137, 0.045565747346661946, 0.04610632862112697, 0.045833063720917756, 0.04570312051098324, 0.04492890864752682, 0.044475648901471196, 0.04400600638447656, 0.04456265547609326, 0.044128139516460614, 0.04453657786287731, 0.04445057940310808, 0.0439880458926681, 0.04328486902240663, 0.04337142345211027, 0.043828888052314725, 0.04415128158869244, 0.04375285776422176, 0.04349735397671739, 0.04389744731720245, 0.04360850373257527, 0.04493185279449483, 0.04573992553725017, 0.04507263250656118, 0.0447735217203688, 0.043525736777693974, 0.042692506091493235, 0.04210475825145775, 0.042154104090207846, 0.04332830782710276, 0.04391732071586194, 0.043347501357520156, 0.042848611193108566, 0.04164640561162145, 0.040938018679287515, 0.04162184044370985, 0.04095033081616843, 0.04161335578048475, 0.04160341068510694, 0.0410648639412764, 0.041135335609933835, 0.04214569574615702, 0.041696590150558446, 0.043230928587101175, 0.04403499518539655, 0.0439354850488576, 0.04296241342372047, 0.04258092575836915, 0.04252906492865655, 0.04292965543808505, 0.042875734975601064, 0.0422217021001254, 0.04202626876890759, 0.04275753422807465, 0.04169345420049062, 0.04169447866597712, 0.042201685589571984, 0.04235923238990516, 0.042641611146348395, 0.04285814696546504, 0.04347900035177658, 0.04265127776787561, 0.04250160517116078, 0.04214358364137555, 0.041814441600311, 0.041048161100984706, 0.040893473791644486, 0.03891555998275565, 0.03822459932606989, 0.03949296310825308, 0.04027938124174104, 0.040487324235132766, 0.039751000605572254, 0.039743359922954406, 0.03946879379691378, 0.03901029244914263, 0.03933921503491017, 0.038994629124210124, 0.03865254541054854, 0.037995480195573306, 0.038482209601216316, 0.03898646364198323, 0.03880572086514194, 0.038930102552239276, 0.0391796579193428, 0.0392840319311019, 0.03944770870411962, 0.039955678405008854, 0.039115003475563986, 0.0383948599149117, 0.03786466573954441, 0.0380602464042965, 0.03776368009164392, 0.03744038091087244, 0.0374369127878161, 0.03769190890901291, 0.03769522699238811, 0.03703298053885642, 0.03738665179352322, 0.03818158102778422, 0.03857149961229965, 0.03905457845474656, 0.0391473405464493, 0.03913697733876665, 0.039702793587452566, 0.039446038719098686, 0.03931766426723098, 0.03870856989435519, 0.03940253312544818, 0.03982039694795579, 0.04023663094204145, 0.04094311968798666, 0.0408306361585838, 0.04131781869027043, 0.041736462289110404, 0.04247339146969603, 0.041262656890984115, 0.041051155144485804, 0.04216710776132969, 0.04230682631814865, 0.043521861721589755, 0.04337001462802339, 0.04353365784995032, 0.04302809557085159, 0.043277197155574654, 0.04353641065526643, 0.043304271635386075, 0.043024267547262886, 0.04250169367398683], "yaxis": "y"}], "layout": {"legend": {"title": {"text": "variable"}, "tracegroupgap": 0}, "margin": {"t": 60}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "index"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "value"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["pd.options.plotting.backend = \"plotly\"\n", "result.plot(x=result.index,y=result.columns,kind=\"line\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "USD_SOFR: 5y<br>Date: %{x|%Y-%m-%d}<br>Rate: %{y:.4%}<extra></extra>", "mode": "lines", "name": "USD_SOFR: 5y", "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "y": [0.038386655806441065, 0.03844622533288111, 0.03947712116463352, 0.03974232360650364, 0.039837292851738385, 0.039570262964291676, 0.03949980002671596, 0.03849675650638007, 0.03800396864286523, 0.039184692183872646, 0.04030613671434469, 0.04060303944499459, 0.04075186547685684, 0.04045916648994436, 0.04056430825963972, 0.04110105591945939, 0.0402465965095976, 0.04061933770277696, 0.04000986047365958, 0.039855181709948916, 0.03826864679394356, 0.03809369617676797, 0.04002699869839285, 0.0415219181612247, 0.040588871626124096, 0.040919545720257744, 0.041499660212968095, 0.041762918141907864, 0.04172228553472355, 0.043824612598879584, 0.04299779996879186, 0.04278314758743683, 0.0433995125013497, 0.04314138528034262, 0.043727024673696005, 0.044005348417239784, 0.04327968105563881, 0.04351525010664547, 0.04369017242769449, 0.043143504449623005, 0.04307514957246121, 0.042206920893087814, 0.04265346686126302, 0.0419468037656378, 0.04166869964376258, 0.0412182673089686, 0.04094639319534859, 0.0413662680939504, 0.042026403777684886, 0.042563523345596384, 0.043685766038241376, 0.04407258182018217, 0.04420727060269943, 0.04376897801710817, 0.04326913628947443, 0.043368790186161014, 0.042557551879848575, 0.04312654819431562, 0.04303302433792195, 0.04262779147502168, 0.04292276402622128, 0.04404862675230176, 0.04426639327466854, 0.044100266496926416, 0.04384483507635656, 0.0449116313296956, 0.04531764230704285, 0.044680874947378046, 0.04734391348958015, 0.04765937484011531, 0.04676831329915844, 0.047432824354514035, 0.048371586787918516, 0.04742036488226778, 0.04811119089414386, 0.04802552424084822, 0.04778756374497333, 0.04757606164836326, 0.047946827074712116, 0.04883817296963845, 0.04849975990632446, 0.04792683319482633, 0.04880563584939313, 0.04805908007616116, 0.04712830886065789, 0.04621279085077907, 0.04599299043931217, 0.04582138147803034, 0.046157677049573964, 0.04584918442396005, 0.04639235493306718, 0.046277685434753156, 0.04566279460894473, 0.04439661952691145, 0.04503752323516278, 0.04557991399870782, 0.0457599109810382, 0.04537184025523917, 0.04568543034641696, 0.046479567791710016, 0.04632689381456271, 0.04716994789882012, 0.047730482017689695, 0.04697758306978957, 0.046249973322183585, 0.04506241097125623, 0.04430098166308495, 0.0436935305179998, 0.04379471587587439, 0.04563715979757563, 0.04585800207458494, 0.04511696455476383, 0.04395705072821117, 0.04289834019876112, 0.04273136941302407, 0.04354404658790827, 0.04286191524515081, 0.04326102060731194, 0.043156792775220686, 0.042837969087098034, 0.043094281859481935, 0.043924625579780184, 0.04344245800160551, 0.04431154920510423, 0.044941521078502224, 0.04454399051265652, 0.04370883947742104, 0.04258546742343767, 0.042699677424648044, 0.04271555478345646, 0.042556324185973646, 0.041381475809041605, 0.04093945389110508, 0.04125716544774507, 0.040612047327604775, 0.040695134072911525, 0.04120293101481923, 0.041727620216330696, 0.041729276283268626, 0.04160068955375716, 0.0415935103972492, 0.04123989083209234, 0.040695713239936274, 0.04069385909785952, 0.04020410032580761, 0.03891015030349403, 0.03804586470794255, 0.03559383596546545, 0.0358355667238466, 0.0368233718656023, 0.03710250895705158, 0.037811363856478285, 0.03742086742364604, 0.036874292987910284, 0.036033566098105894, 0.03616500930028822, 0.037424473458161064, 0.03706468527421722, 0.03698899781011057, 0.036190980017735956, 0.035896117136395796, 0.036527030778675855, 0.03574152024526776, 0.035847729802471716, 0.0356643161672942, 0.035831909409292644, 0.03603253673622972, 0.03632456912524068, 0.035589503997928075, 0.03462050707101712, 0.03448735800869534, 0.0338622419170502, 0.03378913858110971, 0.033069767682101354, 0.033307947578299586, 0.03356464910804977, 0.03329371878105623, 0.0329762915419292, 0.03343600309621562, 0.033923648189868594, 0.03393774396322235, 0.03416982205244243, 0.034222823764481033, 0.03377646555715772, 0.034355018809462526, 0.0347322942775599, 0.03408281978673208, 0.034035975329094956, 0.03455240549538277, 0.03536065590424624, 0.0373460227403156, 0.038055622099636276, 0.03779495200373147, 0.03859895568595935, 0.03821234688417013, 0.03837223770674239, 0.03782415300779225, 0.0377176669879489, 0.03835635015227738, 0.03821257826755097, 0.03946733253045059, 0.039652108504771535, 0.0402260664059889, 0.03988653586713652, 0.04019249116822682, 0.040636444272942135, 0.04013864122500594, 0.041017122940709166, 0.04088031366038466]}, {"hovertemplate": "USD_SOFR: 10y5y<br>Date: %{x|%Y-%m-%d}<br>Rate: %{y:.4%}<extra></extra>", "mode": "lines", "name": "USD_SOFR: 10y5y", "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "y": [0.038847259762274, 0.03929175053715356, 0.040220314040824635, 0.04080120058388886, 0.04092058149156354, 0.040895651678424065, 0.04104970953252709, 0.04086089162630433, 0.040791438879436724, 0.04224087870616305, 0.04211857172056842, 0.04295475615327075, 0.042426248528339934, 0.04248021503948919, 0.04302883532748487, 0.04354613120368461, 0.043287128198215515, 0.04309022304164405, 0.042488649928171314, 0.04150552202410612, 0.040569899320808754, 0.03987246861893561, 0.04078247398186592, 0.041975873892130124, 0.041555086762831255, 0.0419898959432961, 0.042164826598018956, 0.04231796209150803, 0.04231815160538505, 0.04305432979924935, 0.043008607041156596, 0.04276756469207335, 0.04284068466053425, 0.04323663018100561, 0.04349095812038915, 0.04311563432790008, 0.04198304091829755, 0.04229204332143237, 0.042620408479783524, 0.0423877032748337, 0.042021101859286814, 0.04142258212147365, 0.04155070124343373, 0.04078490380956073, 0.04016343383329891, 0.04033633728711027, 0.04058369281362324, 0.04053010243019636, 0.04102658432525994, 0.0411963699612242, 0.04227734525002462, 0.04204162629204783, 0.04237481443081215, 0.042527059767540565, 0.042973945185362926, 0.04262024530954728, 0.04207390595293093, 0.04259841047906676, 0.042279203244625606, 0.04165200141539561, 0.04146412199221293, 0.042536713667224474, 0.043034315181618724, 0.043338756277366754, 0.04293008544327707, 0.04367476481127089, 0.04355095415396366, 0.04293318484424155, 0.04392228265520109, 0.044788530694461814, 0.04435084027334114, 0.04532563934735911, 0.04579716232705612, 0.045199561471918975, 0.045400061352304885, 0.045105063736581956, 0.045262363376035154, 0.04554679461285961, 0.0461203700817944, 0.04650614173079425, 0.04607664462364137, 0.045565747346661946, 0.04610632862112697, 0.045833063720917756, 0.04570312051098324, 0.04492890864752682, 0.044475648901471196, 0.04400600638447656, 0.04456265547609326, 0.044128139516460614, 0.04453657786287731, 0.04445057940310808, 0.0439880458926681, 0.04328486902240663, 0.04337142345211027, 0.043828888052314725, 0.04415128158869244, 0.04375285776422176, 0.04349735397671739, 0.04389744731720245, 0.04360850373257527, 0.04493185279449483, 0.04573992553725017, 0.04507263250656118, 0.0447735217203688, 0.043525736777693974, 0.042692506091493235, 0.04210475825145775, 0.042154104090207846, 0.04332830782710276, 0.04391732071586194, 0.043347501357520156, 0.042848611193108566, 0.04164640561162145, 0.040938018679287515, 0.04162184044370985, 0.04095033081616843, 0.04161335578048475, 0.04160341068510694, 0.0410648639412764, 0.041135335609933835, 0.04214569574615702, 0.041696590150558446, 0.043230928587101175, 0.04403499518539655, 0.0439354850488576, 0.04296241342372047, 0.04258092575836915, 0.04252906492865655, 0.04292965543808505, 0.042875734975601064, 0.0422217021001254, 0.04202626876890759, 0.04275753422807465, 0.04169345420049062, 0.04169447866597712, 0.042201685589571984, 0.04235923238990516, 0.042641611146348395, 0.04285814696546504, 0.04347900035177658, 0.04265127776787561, 0.04250160517116078, 0.04214358364137555, 0.041814441600311, 0.041048161100984706, 0.040893473791644486, 0.03891555998275565, 0.03822459932606989, 0.03949296310825308, 0.04027938124174104, 0.040487324235132766, 0.039751000605572254, 0.039743359922954406, 0.03946879379691378, 0.03901029244914263, 0.03933921503491017, 0.038994629124210124, 0.03865254541054854, 0.037995480195573306, 0.038482209601216316, 0.03898646364198323, 0.03880572086514194, 0.038930102552239276, 0.0391796579193428, 0.0392840319311019, 0.03944770870411962, 0.039955678405008854, 0.039115003475563986, 0.0383948599149117, 0.03786466573954441, 0.0380602464042965, 0.03776368009164392, 0.03744038091087244, 0.0374369127878161, 0.03769190890901291, 0.03769522699238811, 0.03703298053885642, 0.03738665179352322, 0.03818158102778422, 0.03857149961229965, 0.03905457845474656, 0.0391473405464493, 0.03913697733876665, 0.039702793587452566, 0.039446038719098686, 0.03931766426723098, 0.03870856989435519, 0.03940253312544818, 0.03982039694795579, 0.04023663094204145, 0.04094311968798666, 0.0408306361585838, 0.04131781869027043, 0.041736462289110404, 0.04247339146969603, 0.041262656890984115, 0.041051155144485804, 0.04216710776132969, 0.04230682631814865, 0.043521861721589755, 0.04337001462802339, 0.04353365784995032, 0.04302809557085159, 0.043277197155574654, 0.04353641065526643, 0.043304271635386075, 0.043024267547262886, 0.04250169367398683]}], "layout": {"showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "white", "showlakes": true, "showland": true, "subunitcolor": "#C8D4E3"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "white", "polar": {"angularaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}, "bgcolor": "white", "radialaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "yaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "zaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "baxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "bgcolor": "white", "caxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}}}, "title": {"font": {"size": 30}, "text": "Rate History", "x": 0.5, "xanchor": "center"}, "xaxis": {"tickformat": "%Y-%m", "ticklabelmode": "period", "title": {"text": "Date"}, "type": "date"}, "yaxis": {"tickformat": ".2%", "title": {"text": "Forward Rate"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_series(result)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from pricer.ois_swap import oisSwapPricer\n", "ois_pricer = oisSwapPricer(\"USD.SOFR\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USD_SOFR OIS 2y</th>\n", "      <th>USD_SOFR OIS 5y</th>\n", "      <th>USD_SOFR OIS 10y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>0.041410</td>\n", "      <td>0.035925</td>\n", "      <td>0.035161</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>0.041609</td>\n", "      <td>0.035982</td>\n", "      <td>0.035232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>0.042227</td>\n", "      <td>0.036848</td>\n", "      <td>0.036119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>0.042161</td>\n", "      <td>0.037066</td>\n", "      <td>0.036515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>0.042180</td>\n", "      <td>0.037168</td>\n", "      <td>0.036610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-25</th>\n", "      <td>0.038910</td>\n", "      <td>0.037316</td>\n", "      <td>0.037630</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>0.039244</td>\n", "      <td>0.037710</td>\n", "      <td>0.037956</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>0.038781</td>\n", "      <td>0.037280</td>\n", "      <td>0.037657</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>0.039624</td>\n", "      <td>0.038045</td>\n", "      <td>0.038057</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>0.039458</td>\n", "      <td>0.037909</td>\n", "      <td>0.037752</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>209 rows × 3 columns</p>\n", "</div>"], "text/plain": ["            USD_SOFR OIS 2y  USD_SOFR OIS 5y  USD_SOFR OIS 10y\n", "2024-01-02         0.041410         0.035925          0.035161\n", "2024-01-03         0.041609         0.035982          0.035232\n", "2024-01-04         0.042227         0.036848          0.036119\n", "2024-01-05         0.042161         0.037066          0.036515\n", "2024-01-08         0.042180         0.037168          0.036610\n", "...                     ...              ...               ...\n", "2024-10-25         0.038910         0.037316          0.037630\n", "2024-10-28         0.039244         0.037710          0.037956\n", "2024-10-29         0.038781         0.037280          0.037657\n", "2024-10-30         0.039624         0.038045          0.038057\n", "2024-10-31         0.039458         0.037909          0.037752\n", "\n", "[209 rows x 3 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result_ois = ois_pricer.fairRate(dt.date(2024,1,1),dt.date(2024,10,31),[\"2y\",\"5y\",\"10y\"])\n", "result_ois"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "USD_SOFR OIS 2y<br>Date: %{x|%Y-%m-%d}<br>Rate: %{y:.4%}<extra></extra>", "mode": "lines", "name": "USD_SOFR OIS 2y", "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "y": [0.04140999999999989, 0.041609000000000014, 0.042226999999999945, 0.04216100000000058, 0.0421800000000001, 0.042074999999999925, 0.04192599999999999, 0.04073300000000188, 0.03976600000000174, 0.04061400000000013, 0.042034000000000196, 0.041874000000001424, 0.04229500000000211, 0.042260000000000186, 0.04205000000000008, 0.042432000000000074, 0.04154900000000002, 0.04205399999999992, 0.04177099999999998, 0.041977000000360774, 0.040714999999999925, 0.04057900000000143, 0.04237000000000177, 0.0433620000003736, 0.04265599999980999, 0.04292599999982159, 0.04316700000000319, 0.043436000000003465, 0.04336499999961549, 0.04540899999982821, 0.04468599999974988, 0.04456500000000103, 0.045090000000001025, 0.044863000000000035, 0.04536200000016329, 0.04574000000000068, 0.045589000000000685, 0.045885000000144296, 0.045937999999999875, 0.04539499999985726, 0.04520500000000123, 0.0445010000000013, 0.0451409999996007, 0.0446469999996276, 0.04466199999985013, 0.04410500000000178, 0.0437950000000021, 0.044260000000167284, 0.04476999999999996, 0.04525800000000001, 0.04579499999999997, 0.04624500000000003, 0.04630299999999993, 0.0458949999999999, 0.04502300000000004, 0.045310999999999886, 0.04479600000000049, 0.0451969999998563, 0.045194185475363885, 0.04493600000000351, 0.045455, 0.04601511182734658, 0.046019999999784546, 0.04585199999981396, 0.045579000000000126, 0.04653800000000016, 0.046928000000141384, 0.046442000000188055, 0.048870999999999956, 0.048767000000000844, 0.0481900000000011, 0.048394999999999966, 0.049006185947620705, 0.04844999999999995, 0.04893800000000175, 0.048939000000002106, 0.04875400000000032, 0.048364999999999846, 0.04852299999999991, 0.04924999999999995, 0.04922299999999985, 0.04901999999999978, 0.04968499999999969, 0.048974999999999595, 0.04811599999982244, 0.04740599999960582, 0.0474790000002638, 0.04743700000025606, 0.04755800000036407, 0.047331, 0.047817999999999854, 0.04776999999981696, 0.04732999999980324, 0.046350999999775905, 0.04703700000000005, 0.04731999999999987, 0.04753999999985421, 0.0472769999997923, 0.047682999999618635, 0.048351000000000276, 0.048388000000000285, 0.04868500000017917, 0.04877900000000007, 0.04833000000000092, 0.047798000000000965, 0.04714000000035176, 0.04664999999999999, 0.046155, 0.046068999999999964, 0.04760500000000007, 0.04758099999999997, 0.04708999999999998, 0.04622000000000003, 0.04540500000017641, 0.045422000000141134, 0.04605199999979446, 0.045515999999613505, 0.04569499999999996, 0.0455699999999999, 0.04544399999985443, 0.04563099999985374, 0.0461390000002515, 0.04576699999999994, 0.04608200000000009, 0.0460171787606062, 0.045982000000000016, 0.04550500000000023, 0.04455200000000024, 0.044712999999736894, 0.044649999999661016, 0.04452499999967611, 0.0433130000000004, 0.04283500000000076, 0.04276199999999999, 0.04243000000000006, 0.042639, 0.04290900000000121, 0.043261000000001444, 0.0433160000000002, 0.043095999999823636, 0.04281300000023934, 0.042721999999999975, 0.042314999999849265, 0.04245500000000007, 0.0420039999999999, 0.041074999999999987, 0.03978600000000003, 0.03690799999999992, 0.037357999999827744, 0.037899999999818273, 0.037745999999708436, 0.03846099999999999, 0.03861499999999995, 0.038188000000140374, 0.037250000000152106, 0.03759600000000002, 0.03895999999999994, 0.03851100000000008, 0.03863200000000387, 0.037790000000000074, 0.03727499999999998, 0.03796400000000037, 0.03706000000000058, 0.037260000000000036, 0.036856000000000035, 0.03692599999999993, 0.03702200000000171, 0.037155000000002714, 0.03665900000000023, 0.03552600000000022, 0.0354260000000015, 0.03438500000000179, 0.03458200000000033, 0.033868999999999955, 0.034412000000000074, 0.034280000000000005, 0.033839999999999926, 0.03349799999999999, 0.034083, 0.03417300000000003, 0.03380800000000115, 0.03386700000000112, 0.03377999999983013, 0.03321399999982135, 0.033652999999728955, 0.0342159999999999, 0.03353999999999998, 0.03398500000015109, 0.03438799999999992, 0.035001000000000004, 0.03726500000000014, 0.03798899999961228, 0.03766999999959891, 0.038375999999652965, 0.03770500000000098, 0.03762000000000149, 0.03748599999999988, 0.037404999999999994, 0.037751000000001055, 0.03752200000000108, 0.038421999999999956, 0.03847100000000012, 0.03891000000000013, 0.038686000000001865, 0.03891000000000197, 0.039244000000000376, 0.03878100000000037, 0.039623999999999965, 0.03945800000000001]}, {"hovertemplate": "USD_SOFR OIS 5y<br>Date: %{x|%Y-%m-%d}<br>Rate: %{y:.4%}<extra></extra>", "mode": "lines", "name": "USD_SOFR OIS 5y", "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "y": [0.03592500000006283, 0.03598200000006905, 0.036847999999990715, 0.03706599999996392, 0.0371679999999823, 0.03694200000008215, 0.03687600000007966, 0.035954999999765654, 0.03550499999995444, 0.03655899999995764, 0.03755200000000004, 0.03777099999995188, 0.03791199999995343, 0.037687999999917, 0.037764999999963, 0.03822599999995577, 0.03745999999994107, 0.037789999999950426, 0.03729199999998112, 0.03717500000002095, 0.03579599999996634, 0.03562400000009481, 0.03731400000009233, 0.03861700000001274, 0.0378150000000302, 0.0381010000000394, 0.03857200000003676, 0.03880000000001273, 0.03878299999999297, 0.04060500000007609, 0.03989400000007483, 0.03968199999997886, 0.040212999999975435, 0.04001800000004606, 0.040520000000056525, 0.04074499999991098, 0.04014499999998461, 0.0403700000000064, 0.040514999999992564, 0.04004500000008552, 0.039958999999897646, 0.039211999999854245, 0.03962800000000935, 0.039025000000014076, 0.038796000000123676, 0.03837499999985432, 0.038137999999818865, 0.0385260000000092, 0.039095000000078754, 0.039559000000060754, 0.04048600000010263, 0.04082700000017231, 0.04095700000020952, 0.04058000000017887, 0.040130000000108655, 0.04020500000014867, 0.03951350950509422, 0.040020000000012663, 0.03994377358857448, 0.0395710000000333, 0.03983500000015436, 0.040819313628952664, 0.04099200000001009, 0.04084900000005969, 0.040607000000024665, 0.041523999999999076, 0.04189100000003051, 0.041352000000033785, 0.043630000000181884, 0.04386000000002459, 0.04311400000001872, 0.04367500000007628, 0.044462751785306745, 0.04366900000006219, 0.044231999999969, 0.044165999999992066, 0.04397900000014105, 0.04379000000018857, 0.04409500000017001, 0.0448300000002071, 0.04455900000024113, 0.04410300000004572, 0.044842000000052, 0.044208000000047445, 0.04340000000019058, 0.04262700000018174, 0.04246800000002359, 0.042329000000025575, 0.04260700000005464, 0.042327000000176744, 0.04279200000015684, 0.04271200000001773, 0.04219300000001635, 0.04111500000006027, 0.04164900000011695, 0.04210600000007695, 0.0422790000000274, 0.04195200000001994, 0.04222700000012631, 0.04287500000007548, 0.04275500000009375, 0.043475000000010124, 0.04393000000006851, 0.04328499999997923, 0.04267099999994007, 0.041693000000049926, 0.04105000000006889, 0.04053100000005169, 0.0405870000000426, 0.04216400000006331, 0.04236100000013005, 0.041736000000119344, 0.04074900000007569, 0.03982300000006364, 0.039685510093169764, 0.040395000000095334, 0.03981000000021711, 0.04013400000017885, 0.0400440000001618, 0.03979600000001593, 0.04001600000001509, 0.04071900000003566, 0.04028700000024868, 0.04101400000019621, 0.041548222379367776, 0.041209000000176126, 0.040485000000066294, 0.03952799999998795, 0.039648000000009544, 0.03965900000000126, 0.039523000000058615, 0.038477999999914546, 0.03809199999988043, 0.03836999999995505, 0.03782399999996506, 0.037902000000025346, 0.03831199999981171, 0.038762999999794746, 0.038784999999964105, 0.0386700000000123, 0.038651000000004744, 0.03833400000000617, 0.03786599999999801, 0.03789000000000381, 0.03746600000002332, 0.03635000000002127, 0.03555100000007537, 0.03337100000002557, 0.03361500000000042, 0.03447099999999685, 0.034698999999998495, 0.03530099999996233, 0.03498099999998741, 0.03452399999999305, 0.033779999999991275, 0.03390500000001621, 0.034995999999970384, 0.034675999999944425, 0.034639999999989825, 0.03393499999998585, 0.03366500000000525, 0.034200999999896106, 0.03350199999985948, 0.03362199999995566, 0.03345199999994688, 0.033594999999962974, 0.03373399999993789, 0.033985999999928726, 0.03337999999991998, 0.03251699999994406, 0.03237499999995351, 0.03180499999994051, 0.031772999999877025, 0.03113499999996944, 0.03135899999997579, 0.031546999999965915, 0.031299999999960706, 0.031039999999976378, 0.031457999999988925, 0.031879999999985746, 0.031859999999991916, 0.0320599999999693, 0.0321189999999903, 0.03171999999998877, 0.0322279999999843, 0.03255099999994566, 0.03197399999990842, 0.031967999999985154, 0.03242199999999074, 0.03311199999989256, 0.03487499999992125, 0.03551199999998164, 0.03528299999998051, 0.0359850000000023, 0.03560999999995625, 0.035739999999947085, 0.035299999999958934, 0.035207999999974884, 0.03573999999976704, 0.03560999999973319, 0.036704999999908186, 0.03686299999992155, 0.03735799999993835, 0.037053999999951535, 0.03731599999994736, 0.037709999999943844, 0.037279999999941645, 0.03804500000010796, 0.037909000000041014]}, {"hovertemplate": "USD_SOFR OIS 10y<br>Date: %{x|%Y-%m-%d}<br>Rate: %{y:.4%}<extra></extra>", "mode": "lines", "name": "USD_SOFR OIS 10y", "type": "scatter", "x": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "y": [0.03516099999998568, 0.03523199999997635, 0.03611899999988534, 0.03651499999987351, 0.03660999999995954, 0.03640399999995478, 0.03643599999988891, 0.0358099999999806, 0.03547599999998076, 0.036676999999884004, 0.037211999999886766, 0.03763799999998506, 0.03755799999998636, 0.03741299999991334, 0.03763499999994461, 0.038101999999893575, 0.03755199999992116, 0.03771599999993782, 0.037126999999956216, 0.03672099999998743, 0.03548699999991398, 0.035207999999927964, 0.03660499999996095, 0.037904999999999606, 0.03724299999998424, 0.03743999999998249, 0.03793699999993001, 0.038148999999926936, 0.038159999999978975, 0.03955700000001277, 0.039000000000027235, 0.03876199999987707, 0.039170999999877325, 0.03914599999999137, 0.03955100000001977, 0.03956399999991967, 0.038755999999944495, 0.03895800000000883, 0.03918800000001366, 0.03884400000001456, 0.03873299999988005, 0.03806399999988337, 0.03836500000000926, 0.037745000000014524, 0.03730600000003426, 0.037114999999989115, 0.03703799999998948, 0.03727600000000869, 0.03778900000006459, 0.03812400000007233, 0.03912400000007515, 0.03928000000000795, 0.039445000000008834, 0.03919600000012293, 0.03909500000007883, 0.039030999999983065, 0.03836351296601384, 0.03888900000000935, 0.03872362841610276, 0.038288999999998394, 0.038374999999959816, 0.03941411024987446, 0.039741000000022814, 0.03970200000001461, 0.039398999999934625, 0.04024499999995127, 0.040442000000040376, 0.03985423814288727, 0.04170600000010717, 0.04211021684513023, 0.04141500000000762, 0.042150000000069965, 0.04284180689107051, 0.042075129352287646, 0.04251499999996718, 0.04240100000002205, 0.04230000000010883, 0.04227000000000807, 0.042682000000008054, 0.04332600000000896, 0.042936000000011326, 0.04242000000001126, 0.04310100000001227, 0.042622000000011956, 0.042096000000018535, 0.04133500000003802, 0.04105400000003512, 0.040800000000046126, 0.0411510000000479, 0.04085400000000155, 0.04127000000004156, 0.04120000000003971, 0.040732000000032985, 0.039785000000023406, 0.040152999999969574, 0.040573000000002524, 0.04080800000003923, 0.040468000000034234, 0.040599000000058685, 0.04113999999995525, 0.04092499999993022, 0.04184000000005375, 0.04247200000004413, 0.041799999999924474, 0.04129099999993365, 0.04022500000004031, 0.03956600000007906, 0.03900700000008428, 0.03911900000005019, 0.04053200000009821, 0.040870000000112615, 0.040234000000107815, 0.03939100000009516, 0.03842299999998601, 0.03813853562974663, 0.03881800000004352, 0.038218000000039574, 0.038623000000000296, 0.03855600000002765, 0.03824600000003448, 0.03843200000003893, 0.03925000000009766, 0.038797000000005556, 0.03978999999999843, 0.04045905137538689, 0.040107999999994655, 0.03937999999992954, 0.03855599999990763, 0.038588999999996695, 0.0387619999999846, 0.038600999999986355, 0.03773699999998937, 0.03747099999998892, 0.03787999999993575, 0.03722199999994106, 0.03726199999994455, 0.03770599999998693, 0.03803999999998812, 0.038163999999947795, 0.03815699999998385, 0.038344999999977064, 0.037919999999983446, 0.037521999999973764, 0.03738399999998293, 0.037014999999985906, 0.03602999999998164, 0.035484999999897966, 0.03356999999987112, 0.03357199999994387, 0.034555999999935444, 0.03498499999992137, 0.03536599999988642, 0.034924999999991824, 0.034587999999934955, 0.03400699999992307, 0.033979999999934854, 0.034728999999873215, 0.0344469999999912, 0.03428199999995484, 0.033634999999949775, 0.03366599999993142, 0.034144999999989746, 0.033641999999987, 0.03371199999991631, 0.033755999999900387, 0.03388599999998687, 0.034031999999977136, 0.034366999999977894, 0.033680999999888626, 0.03294699999988011, 0.03268499999998601, 0.032449999999982077, 0.0322939999999926, 0.031749999999917886, 0.031854999999918567, 0.032049999999931314, 0.03189999999990979, 0.03158499999993189, 0.03188499999994071, 0.03247099999993329, 0.03256999999988812, 0.03288799999999117, 0.032970999999908865, 0.03271799999990375, 0.03319999999990254, 0.03329999999998218, 0.03291999999998932, 0.032647999999905676, 0.033152999999885524, 0.033761999999991084, 0.034967999999991554, 0.03561899999993304, 0.03546999999993329, 0.03608899999993842, 0.035943999999984565, 0.03625499999998319, 0.03562499999991219, 0.03549599999991235, 0.036220999999987534, 0.03618199999998495, 0.03729899999987931, 0.0373699999998903, 0.03774199999990518, 0.03737999999998897, 0.03762999999998727, 0.03795599999993076, 0.037656999999924314, 0.03805700000003044, 0.0377520000000058]}], "layout": {"showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "white", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "#C8D4E3", "linecolor": "#C8D4E3", "minorgridcolor": "#C8D4E3", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "white", "showlakes": true, "showland": true, "subunitcolor": "#C8D4E3"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "white", "polar": {"angularaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}, "bgcolor": "white", "radialaxis": {"gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "yaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}, "zaxis": {"backgroundcolor": "white", "gridcolor": "#DFE8F3", "gridwidth": 2, "linecolor": "#EBF0F8", "showbackground": true, "ticks": "", "zerolinecolor": "#EBF0F8"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "baxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}, "bgcolor": "white", "caxis": {"gridcolor": "#DFE8F3", "linecolor": "#A2B1C6", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#EBF0F8", "linecolor": "#EBF0F8", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#EBF0F8", "zerolinewidth": 2}}}, "title": {"font": {"size": 30}, "text": "Rate History", "x": 0.5, "xanchor": "center"}, "xaxis": {"tickformat": "%Y-%m", "ticklabelmode": "period", "title": {"text": "Date"}, "type": "date"}, "yaxis": {"tickformat": ".2%", "title": {"text": "Forward Rate"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_series(result_ois)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": ["2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05", "2024-01-08", "2024-01-09", "2024-01-10", "2024-01-11", "2024-01-12", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-22", "2024-01-23", "2024-01-24", "2024-01-25", "2024-01-26", "2024-01-29", "2024-01-30", "2024-01-31", "2024-02-01", "2024-02-02", "2024-02-05", "2024-02-06", "2024-02-07", "2024-02-08", "2024-02-09", "2024-02-12", "2024-02-13", "2024-02-14", "2024-02-15", "2024-02-16", "2024-02-20", "2024-02-21", "2024-02-22", "2024-02-23", "2024-02-26", "2024-02-27", "2024-02-28", "2024-02-29", "2024-03-01", "2024-03-04", "2024-03-05", "2024-03-06", "2024-03-07", "2024-03-08", "2024-03-11", "2024-03-12", "2024-03-13", "2024-03-14", "2024-03-15", "2024-03-18", "2024-03-19", "2024-03-20", "2024-03-21", "2024-03-22", "2024-03-25", "2024-03-26", "2024-03-27", "2024-03-28", "2024-04-01", "2024-04-02", "2024-04-03", "2024-04-04", "2024-04-05", "2024-04-08", "2024-04-09", "2024-04-10", "2024-04-11", "2024-04-12", "2024-04-15", "2024-04-16", "2024-04-17", "2024-04-18", "2024-04-19", "2024-04-22", "2024-04-23", "2024-04-24", "2024-04-25", "2024-04-26", "2024-04-29", "2024-04-30", "2024-05-01", "2024-05-02", "2024-05-03", "2024-05-06", "2024-05-07", "2024-05-08", "2024-05-09", "2024-05-10", "2024-05-13", "2024-05-14", "2024-05-15", "2024-05-16", "2024-05-17", "2024-05-20", "2024-05-21", "2024-05-22", "2024-05-23", "2024-05-24", "2024-05-28", "2024-05-29", "2024-05-30", "2024-05-31", "2024-06-03", "2024-06-04", "2024-06-05", "2024-06-06", "2024-06-07", "2024-06-10", "2024-06-11", "2024-06-12", "2024-06-13", "2024-06-14", "2024-06-17", "2024-06-18", "2024-06-20", "2024-06-21", "2024-06-24", "2024-06-25", "2024-06-26", "2024-06-27", "2024-06-28", "2024-07-01", "2024-07-02", "2024-07-03", "2024-07-05", "2024-07-08", "2024-07-09", "2024-07-10", "2024-07-11", "2024-07-12", "2024-07-15", "2024-07-16", "2024-07-17", "2024-07-18", "2024-07-19", "2024-07-22", "2024-07-23", "2024-07-24", "2024-07-25", "2024-07-26", "2024-07-29", "2024-07-30", "2024-07-31", "2024-08-01", "2024-08-02", "2024-08-05", "2024-08-06", "2024-08-07", "2024-08-08", "2024-08-09", "2024-08-12", "2024-08-13", "2024-08-14", "2024-08-15", "2024-08-16", "2024-08-19", "2024-08-20", "2024-08-21", "2024-08-22", "2024-08-23", "2024-08-26", "2024-08-27", "2024-08-28", "2024-08-29", "2024-08-30", "2024-09-03", "2024-09-04", "2024-09-05", "2024-09-06", "2024-09-09", "2024-09-10", "2024-09-11", "2024-09-12", "2024-09-13", "2024-09-16", "2024-09-17", "2024-09-18", "2024-09-19", "2024-09-20", "2024-09-23", "2024-09-24", "2024-09-25", "2024-09-26", "2024-09-27", "2024-10-01", "2024-10-02", "2024-10-03", "2024-10-04", "2024-10-07", "2024-10-08", "2024-10-09", "2024-10-10", "2024-10-11", "2024-10-15", "2024-10-16", "2024-10-17", "2024-10-18", "2024-10-21", "2024-10-22", "2024-10-23", "2024-10-24", "2024-10-25", "2024-10-28", "2024-10-29", "2024-10-30", "2024-10-31"], "hovertemplate": "Date: %{customdata}<br>y: %{y:.4%}<br>x: %{x:.4%}<extra></extra>", "marker": {"size": 6}, "mode": "markers", "type": "scatter", "x": [0.04140999999999989, 0.041609000000000014, 0.042226999999999945, 0.04216100000000058, 0.0421800000000001, 0.042074999999999925, 0.04192599999999999, 0.04073300000000188, 0.03976600000000174, 0.04061400000000013, 0.042034000000000196, 0.041874000000001424, 0.04229500000000211, 0.042260000000000186, 0.04205000000000008, 0.042432000000000074, 0.04154900000000002, 0.04205399999999992, 0.04177099999999998, 0.041977000000360774, 0.040714999999999925, 0.04057900000000143, 0.04237000000000177, 0.0433620000003736, 0.04265599999980999, 0.04292599999982159, 0.04316700000000319, 0.043436000000003465, 0.04336499999961549, 0.04540899999982821, 0.04468599999974988, 0.04456500000000103, 0.045090000000001025, 0.044863000000000035, 0.04536200000016329, 0.04574000000000068, 0.045589000000000685, 0.045885000000144296, 0.045937999999999875, 0.04539499999985726, 0.04520500000000123, 0.0445010000000013, 0.0451409999996007, 0.0446469999996276, 0.04466199999985013, 0.04410500000000178, 0.0437950000000021, 0.044260000000167284, 0.04476999999999996, 0.04525800000000001, 0.04579499999999997, 0.04624500000000003, 0.04630299999999993, 0.0458949999999999, 0.04502300000000004, 0.045310999999999886, 0.04479600000000049, 0.0451969999998563, 0.045194185475363885, 0.04493600000000351, 0.045455, 0.04601511182734658, 0.046019999999784546, 0.04585199999981396, 0.045579000000000126, 0.04653800000000016, 0.046928000000141384, 0.046442000000188055, 0.048870999999999956, 0.048767000000000844, 0.0481900000000011, 0.048394999999999966, 0.049006185947620705, 0.04844999999999995, 0.04893800000000175, 0.048939000000002106, 0.04875400000000032, 0.048364999999999846, 0.04852299999999991, 0.04924999999999995, 0.04922299999999985, 0.04901999999999978, 0.04968499999999969, 0.048974999999999595, 0.04811599999982244, 0.04740599999960582, 0.0474790000002638, 0.04743700000025606, 0.04755800000036407, 0.047331, 0.047817999999999854, 0.04776999999981696, 0.04732999999980324, 0.046350999999775905, 0.04703700000000005, 0.04731999999999987, 0.04753999999985421, 0.0472769999997923, 0.047682999999618635, 0.048351000000000276, 0.048388000000000285, 0.04868500000017917, 0.04877900000000007, 0.04833000000000092, 0.047798000000000965, 0.04714000000035176, 0.04664999999999999, 0.046155, 0.046068999999999964, 0.04760500000000007, 0.04758099999999997, 0.04708999999999998, 0.04622000000000003, 0.04540500000017641, 0.045422000000141134, 0.04605199999979446, 0.045515999999613505, 0.04569499999999996, 0.0455699999999999, 0.04544399999985443, 0.04563099999985374, 0.0461390000002515, 0.04576699999999994, 0.04608200000000009, 0.0460171787606062, 0.045982000000000016, 0.04550500000000023, 0.04455200000000024, 0.044712999999736894, 0.044649999999661016, 0.04452499999967611, 0.0433130000000004, 0.04283500000000076, 0.04276199999999999, 0.04243000000000006, 0.042639, 0.04290900000000121, 0.043261000000001444, 0.0433160000000002, 0.043095999999823636, 0.04281300000023934, 0.042721999999999975, 0.042314999999849265, 0.04245500000000007, 0.0420039999999999, 0.041074999999999987, 0.03978600000000003, 0.03690799999999992, 0.037357999999827744, 0.037899999999818273, 0.037745999999708436, 0.03846099999999999, 0.03861499999999995, 0.038188000000140374, 0.037250000000152106, 0.03759600000000002, 0.03895999999999994, 0.03851100000000008, 0.03863200000000387, 0.037790000000000074, 0.03727499999999998, 0.03796400000000037, 0.03706000000000058, 0.037260000000000036, 0.036856000000000035, 0.03692599999999993, 0.03702200000000171, 0.037155000000002714, 0.03665900000000023, 0.03552600000000022, 0.0354260000000015, 0.03438500000000179, 0.03458200000000033, 0.033868999999999955, 0.034412000000000074, 0.034280000000000005, 0.033839999999999926, 0.03349799999999999, 0.034083, 0.03417300000000003, 0.03380800000000115, 0.03386700000000112, 0.03377999999983013, 0.03321399999982135, 0.033652999999728955, 0.0342159999999999, 0.03353999999999998, 0.03398500000015109, 0.03438799999999992, 0.035001000000000004, 0.03726500000000014, 0.03798899999961228, 0.03766999999959891, 0.038375999999652965, 0.03770500000000098, 0.03762000000000149, 0.03748599999999988, 0.037404999999999994, 0.037751000000001055, 0.03752200000000108, 0.038421999999999956, 0.03847100000000012, 0.03891000000000013, 0.038686000000001865, 0.03891000000000197, 0.039244000000000376, 0.03878100000000037, 0.039623999999999965, 0.03945800000000001], "y": [0.03258097377938437, 0.03257682513042641, 0.033571121685617863, 0.03404138025710864, 0.03417635413712938, 0.03389002160898683, 0.0339172302716906, 0.03333721205152281, 0.03321869664302013, 0.0344984596019507, 0.03495919832608189, 0.03551986878752649, 0.035377755550504986, 0.03510327969297866, 0.03543305125114239, 0.03593632269854877, 0.035380940416834566, 0.03551822127659065, 0.03485073574323057, 0.03435581151625163, 0.03301750212286415, 0.03275484349007038, 0.034196301062544524, 0.035641464903992365, 0.034886360141960025, 0.03510647546254352, 0.035731059665634564, 0.03592175775149239, 0.03594755640467156, 0.03736072286896855, 0.03676212944778081, 0.03648024266890839, 0.03692200839101377, 0.036871293157143034, 0.03730980596840634, 0.03726670597262914, 0.036339492289723274, 0.0365227364416726, 0.03675405837965979, 0.03639483423710929, 0.03636188304765023, 0.03565384988088942, 0.03589138641494742, 0.03521183536202006, 0.0347178471087734, 0.03452985110303904, 0.03444265848678114, 0.034669607442143896, 0.03521510173589048, 0.03557526129824953, 0.03675771074437988, 0.036912573843953536, 0.03710767583159827, 0.036823679673085, 0.03683860407213921, 0.03671033345923546, 0.035948431694846704, 0.03652330003601376, 0.03633382141807929, 0.03587869104399305, 0.03588363363819608, 0.03709916965175556, 0.03748117063071178, 0.03742579754548569, 0.03716154771654951, 0.037996058587703094, 0.0382346044656186, 0.037563160175322984, 0.03950666375956777, 0.04003803839266411, 0.03923840581857136, 0.040108507656846044, 0.04091498557793374, 0.04001619288777863, 0.04055526502681213, 0.04041711233592685, 0.040292624739127196, 0.04030630544126568, 0.04076632221564602, 0.04144096634941731, 0.0409541506402483, 0.040321400127432606, 0.0410413543486202, 0.04056410143501034, 0.03997730718150406, 0.03918721206273142, 0.038821201470283014, 0.03854439031045764, 0.03895547213406684, 0.03863303034693108, 0.03905762442393677, 0.03896721439559369, 0.0384446661769382, 0.03741992919492047, 0.037755691020321315, 0.03827438081401297, 0.03846686197486989, 0.038089703066851846, 0.03819981216506378, 0.03876320436796635, 0.038505550338750055, 0.03955988753553288, 0.04033058121609923, 0.03955869387997292, 0.03897781284619482, 0.03777001532052512, 0.03703590583873648, 0.0364887798916995, 0.03666181686588126, 0.038136899534002094, 0.03854297782440105, 0.037836720024178656, 0.03689734893407905, 0.03589779312190548, 0.035587632155256975, 0.036295491777753174, 0.035688399346611256, 0.03613917290263555, 0.036086340896774635, 0.03574577451851065, 0.03594359125345575, 0.03682818359634836, 0.03636410242333176, 0.037481077601370434, 0.03839564417952121, 0.03791275037188943, 0.03706517521517454, 0.036189738330281974, 0.036222331421187665, 0.036369609372008664, 0.036209621820410744, 0.03537861054565318, 0.03512740189389471, 0.03567680854825147, 0.0349433883420813, 0.03493207047307952, 0.03544695756947632, 0.035866486012788565, 0.03594184654872197, 0.03594926215450494, 0.036210462874568236, 0.03571155976649335, 0.0352604983317621, 0.03511661683135904, 0.03473609264309863, 0.03361331663297205, 0.03323013913102445, 0.03159212173588627, 0.03157158029732624, 0.032696872121241345, 0.033248130758515855, 0.03364169267924345, 0.03299924711067581, 0.03261438825840973, 0.032066815348784104, 0.03198371054707384, 0.032693605370382246, 0.032475222003777116, 0.032270199361544794, 0.031672620481594, 0.03173044498087493, 0.03215762206829597, 0.03168675469510355, 0.03173043864661807, 0.03181794258280076, 0.03199751966768107, 0.03216872406255281, 0.032546105190962286, 0.0318217447673208, 0.03119294501612258, 0.030950122621363707, 0.0308507101138623, 0.030615293844322357, 0.03007892533437267, 0.030056624772828448, 0.030393241203255292, 0.030305961421235546, 0.03005276501307905, 0.030305180942482135, 0.030999094861833026, 0.031236849616294568, 0.03158549842040583, 0.03173524565246245, 0.03151877031326912, 0.03203945717670869, 0.03207908572217274, 0.03167886889843654, 0.03125256561740879, 0.03177411945134774, 0.032453785095134864, 0.033588194394256185, 0.034193995715495265, 0.034066142998907305, 0.034705176157175964, 0.034635144070044255, 0.03499038388317024, 0.03428063785529388, 0.03415741472488284, 0.03491395845404277, 0.03489700007813786, 0.03611757729815084, 0.03627866711311197, 0.036723747108580604, 0.0363325263443175, 0.03659947367751598, 0.036974086219342485, 0.03666263430012561, 0.037137873389846275, 0.03690898499134068]}], "layout": {"height": 900, "shapes": [{"line": {"color": "rgba(128, 128, 128, 0.5)", "dash": "long<PERSON>h", "width": 1}, "type": "line", "x0": 0, "x1": 1, "xref": "paper", "y0": 0, "y1": 1, "yref": "paper"}], "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"size": 30}, "text": "", "x": 0.5, "xanchor": "center"}, "width": 900, "xaxis": {"range": [0.02955276501307905, 0.05018499999999969], "scaleanchor": "y", "tickformat": ".2%", "title": {"text": "USD_SOFR OIS 2y"}}, "yaxis": {"range": [0.02955276501307905, 0.05018499999999969], "scaleanchor": "x", "tickformat": ".2%", "title": {"text": "USD_SOFR OIS 2y5y"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_xy(result_ois)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}