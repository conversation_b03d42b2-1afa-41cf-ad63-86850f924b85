import pandas as pd
import QuantLib as ql
from .curve import oisCurve, iborCurve
from helpers.convexity_solver import calibrate_convexity_variance


class estrCurve(oisCurve):
    def __init__(self, context_key: str, **params):
        config = {
            "name": "EUR_ESTR",
            "context_key": context_key,
            "currency": "EUR",
            "fixed_day_counter": ql.Actual360(),
            "fixed_convention": ql.ModifiedFollowing,
            "swap_settlement_days": 2,
            "swap_payment_lag": 1,
        }
        swap_config = {
            "index": ql.Estr(),
            "paymentConvention": ql.ModifiedFollowing,
            "paymentFrequency": ql.Annual,
            "paymentCalendar": ql.TARGET(),
            "fixedPaymentFrequency": ql.Annual,
            "fixedCalendar": ql.TARGET(),
        }
        super().__init__(config, swap_config, **params)

    def set_up_helpers(
        self,
        benchmark_meta_data: pd.DataFrame | None = None,
        bbg_bar_data: pd.DataFrame | None = None,
    ):
        return super().set_up_helpers(
            benchmark_meta_data, bbg_bar_data, self.swap_config
        )


class euribor6MCurve(iborCurve):
    def __init__(self, context_key: str, **params):
        config = {
            "name": "EUR_EURIBOR_6M",
            "context_key": context_key,
            "currency": "EUR",
            "fixed_day_counter": ql.Thirty360(
                ql.Thirty360.USA
            ),  # fixed leg day counter
            "fixed_convention": ql.ModifiedFollowing,
            "swap_settlement_days": 2,
            "swap_payment_lag": 0,
        }
        swap_config = {
            "calendar": ql.TARGET(),  # 3rd arg
            "fixedFrequency": ql.Annual,
            "fixedConvention": ql.ModifiedFollowing,
            "fixedDayCount": ql.Thirty360(ql.Thirty360.USA),
            "index": ql.Euribor6M(),
            "spread": ql.QuoteHandle(),
            "fwdStart": ql.Period(),
            "discountingCurve": ql.RelinkableYieldTermStructureHandle(),
            "settlementDays": 2,  # 9th arg
        }
        params.setdefault("interp_method", "PiecewiseLogCubicDiscount")
        super().__init__(config, swap_config, **params)  # fixing lag: 2 days

    def set_up_helpers(
        self,
        benchmark_meta_data: pd.DataFrame | None = None,
        bbg_bar_data: pd.DataFrame | None = None,
    ):
        self.ois_curve = estrCurve(
            self.context_key,
            debug_mode=self.debug_mode,
        )
        self.ois_curve.calibrate()
        ois_ytm = self.swap_config["discountingCurve"]
        ois_ytm.linkTo(self.ois_curve.curve)
        return super().set_up_helpers(
            benchmark_meta_data, bbg_bar_data, self.swap_config
        )


class euribor3MCurve(iborCurve):
    def __init__(self, context_key: str, **params):
        config = {
            "name": "EUR_EURIBOR_3M",
            "context_key": context_key,
            "currency": "EUR",
            "fixed_day_counter": ql.Thirty360(
                ql.Thirty360.USA
            ),  # fixed leg day counter
            "fixed_convention": ql.ModifiedFollowing,
            "swap_settlement_days": 2,
            "swap_payment_lag": 0,
        }
        swap_config = {
            "calendar": ql.TARGET(),  # 3rd arg
            "fixedFrequency": ql.Annual,
            "fixedConvention": ql.ModifiedFollowing,
            "fixedDayCount": ql.Thirty360(ql.Thirty360.USA),
            "index": ql.Euribor3M(),
            "spread": ql.QuoteHandle(),
            "fwdStart": ql.Period(),
            "discountingCurve": ql.RelinkableYieldTermStructureHandle(),
            "settlementDays": 2,  # 9th arg
        }
        params.setdefault("interp_method", "PiecewiseLogCubicDiscount")
        super().__init__(config, swap_config, **params)  # fixing lag: 2 days

    def set_up_helpers(
        self,
        benchmark_meta_data: pd.DataFrame | None = None,
        bbg_bar_data: pd.DataFrame | None = None,
        convexity_calibration: bool = True,
        is_convexity_calibration_finished: bool = False,
    ):
        variance = None
        if not is_convexity_calibration_finished:
            self.ois_curve = estrCurve(
                self.context_key,
                debug_mode=self.debug_mode,
            )
            self.ois_curve.calibrate()
            ois_ytm = self.swap_config["discountingCurve"]
            ois_ytm.linkTo(self.ois_curve.curve)
            if benchmark_meta_data is None or bbg_bar_data is None:
                benchmark_meta_data, bbg_bar_data = super().fetch_curve_benchmarks()

            "**************** Calibrate futures convexity ****************"
            futures_maturities = benchmark_meta_data[
                benchmark_meta_data["instrument_type"] == "Future"
            ]["maturities"]
            # c1 = 0-3M, c9 = 24-27M
            cutoff_month = (
                futures_maturities.str.extract(r"[cC](\d+)", expand=False)
                .dropna()
                .astype(int)
                .max()
                * 3
            )
            swap_benchmark_condition = benchmark_meta_data.apply(
                lambda row: row["instrument_type"] == "VanillaSwap"
                and ql.Period(row["maturities"]) <= ql.Period(str(cutoff_month) + "M"),
                axis=1,
            )
            swap_benchmarks = benchmark_meta_data[swap_benchmark_condition]

            # First calibration, without convexity
            benchmark_meta_data = benchmark_meta_data[~swap_benchmark_condition]
            if not convexity_calibration:
                return super().set_up_helpers(
                    benchmark_meta_data,
                    bbg_bar_data,
                    self.swap_config,
                )

            self.calibrate(
                benchmark_meta_data,
                bbg_bar_data,
                is_convexity_calibration_finished=True,
            )

            # Swaps used for convexity calibration
            swap_helpers = super().set_up_helpers(
                swap_benchmarks, bbg_bar_data, self.swap_config
            )
            # The annoying part is QuantLib python doesn't expose SwapRateHelper::setTermStructure(YieldTermStructure*),
            # which means user have to recreate the swap using the calibrated ytm
            swaps = [None] * len(swap_helpers)
            for i, helper in enumerate(swap_helpers):
                swap = helper.swap()
                self._print(helper.quote().value())
                swaps[i] = ql.VanillaSwap(
                    ql.Swap.Receiver,
                    1e6,
                    swap.fixedSchedule(),
                    helper.quote().value(),
                    swap.fixedDayCount(),
                    swap.floatingSchedule(),
                    self.index.clone(ql.YieldTermStructureHandle(self.curve)),
                    0.0,
                    swap.floatingDayCount(),
                )
                swaps[i].setPricingEngine(ql.DiscountingSwapEngine(ois_ytm))
            variance = calibrate_convexity_variance(swaps, self.ois_curve.curve)
            self._print(variance)
        return super().set_up_helpers(
            benchmark_meta_data, bbg_bar_data, self.swap_config, variance
        )
