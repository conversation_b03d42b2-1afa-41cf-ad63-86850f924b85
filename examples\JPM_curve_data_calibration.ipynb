%load_ext autoreload
%autoreload 2

import sys
import os
investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), ".."))
sys.path.append(investment_parent_path)
import datetime as dt
from loader.curve_calibrator import curveCalibrator
from helpers.plot_tools import plot_curves, plot_discount_curves
from pricer.swap import oisSwapPricer, iborSwapPricer
import pandas as pd
import numpy as np

calibrator = curveCalibrator("USD.SOFR", data_source="JPM")

calibrator.recalibrate(dt.date(2025,1,1), dt.date(2025,1,10))

pd.read_parquet("c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_calibration_results\\USD_SOFR_JPM_calib.parquet").dtypes

pd.read_parquet("c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_calibration_results\\USD_SOFR_JPM_calib.parquet")

ois_pricer = oisSwapPricer("USD.SOFR", data_source="JPM")

ois_pricer.fairRate(dt.date(2022,1,1), dt.date(2025,6,10), ["1Y", "1Y1Y", "2Y", "2Y1Y", "3Y1Y"])

df = pd.DataFrame([[np.array([1,2]),[2],3], [(2,3),[3,4,5],4]], index=[dt.date(2025,1,1), dt.date(2025,1,2)])

df

def a(row):
    if len(row[1]) == 1:
        return pd.Series({
        "pillar_dates": [0],
        "discount_factors": [1]
    })
    else:
        return pd.Series({
        "pillar_dates": [1,2,],
        "discount_factors": [2,3,4]
    })


df.apply(lambda row: a(row), axis=1, result_type="expand")

df.to_parquet("a.parquet")

pd.Series([1, [1,2], (2,3)], index=["size","pillar_dates", "discount_factors"]).get("pillar_dates2", 0)

