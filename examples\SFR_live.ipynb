{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "import numpy as np\n", "import pandas as pd\n", "from xbbg import blp\n", "import QuantLib as ql\n", "from loader.ir_future_option_data_loader import interestRateFutureOptionLoader\n", "from pricer.ir_future_option_pricer import interestRateFutureOptionPricer\n", "pd.options.plotting.backend = \"plotly\"\n", "pd.set_option('display.max_rows', 200)  # Default is 60\n", "pd.set_option('display.max_columns', 200)  # Default is 20"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["loader = interestRateFutureOptionLoader(\"SFR\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["loader.get_periods(dt.date(2025,1,11),dt.date(2025,1,11))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(Date(13,1,2025), Date(17,1,2025))]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["loader.get_periods(dt.date(2025,1,17),dt.date(2025,1,17))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["date = dt.date(2025,1,11)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["data = pd.read_parquet(r\"c:\\\\Users\\\\<USER>\\\\Documents\\\\investment\\\\loader\\\\ir_future_option_data\\\\0Q_underlying\\\\0Q_20240916.parquet\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-16</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.190</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.140</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-17</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.115</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-18</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.095</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.075</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.055</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-19</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.025</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-20</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.030</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-23</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.015</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-24</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.040</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-25</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.995</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-26</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.960</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-27</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.010</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-09-30</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.025</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.960</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-01</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.060</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>97.025</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-02</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>97.020</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>97.010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.990</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-03</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.940</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.910</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-04</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.715</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.665</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.705</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-07</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.575</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.640</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-08</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.660</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.645</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-09</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.550</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.610</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-10</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.565</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.605</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2024-10-11</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>96.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>96.690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRU6 Comdty</th>\n", "      <td>96.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ5 Comdty</th>\n", "      <td>96.640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRZ6 Comdty</th>\n", "      <td>96.660</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          price\n", "date       ticker              \n", "2024-09-16 SFRH6 Comdty  97.195\n", "           SFRM6 Comdty  97.190\n", "           SFRU6 Comdty  97.170\n", "           SFRZ5 Comdty  97.180\n", "           SFRZ6 Comdty  97.140\n", "2024-09-17 SFRH6 Comdty  97.145\n", "           SFRM6 Comdty  97.145\n", "           SFRU6 Comdty  97.135\n", "           SFRZ5 Comdty  97.130\n", "           SFRZ6 Comdty  97.115\n", "2024-09-18 SFRH6 Comdty  97.095\n", "           SFRM6 Comdty  97.090\n", "           SFRU6 Comdty  97.075\n", "           SFRZ5 Comdty  97.085\n", "           SFRZ6 Comdty  97.055\n", "2024-09-19 SFRH6 Comdty  97.090\n", "           SFRM6 Comdty  97.085\n", "           SFRU6 Comdty  97.060\n", "           SFRZ5 Comdty  97.080\n", "           SFRZ6 Comdty  97.025\n", "2024-09-20 SFRH6 Comdty  97.105\n", "           SFRM6 Comdty  97.090\n", "           SFRU6 Comdty  97.065\n", "           SFRZ5 Comdty  97.105\n", "           SFRZ6 Comdty  97.030\n", "2024-09-23 SFRH6 Comdty  97.100\n", "           SFRM6 Comdty  97.080\n", "           SFRU6 Comdty  97.050\n", "           SFRZ5 Comdty  97.105\n", "           SFRZ6 Comdty  97.015\n", "2024-09-24 SFRH6 Comdty  97.140\n", "           SFRM6 Comdty  97.120\n", "           SFRU6 Comdty  97.085\n", "           SFRZ5 Comdty  97.140\n", "           SFRZ6 Comdty  97.040\n", "2024-09-25 SFRH6 Comdty  97.100\n", "           SFRM6 Comdty  97.080\n", "           SFRU6 Comdty  97.040\n", "           SFRZ5 Comdty  97.100\n", "           SFRZ6 Comdty  96.995\n", "2024-09-26 SFRH6 Comdty  97.040\n", "           SFRM6 Comdty  97.025\n", "           SFRU6 Comdty  96.995\n", "           SFRZ5 Comdty  97.030\n", "           SFRZ6 Comdty  96.960\n", "2024-09-27 SFRH6 Comdty  97.100\n", "           SFRM6 Comdty  97.085\n", "           SFRU6 Comdty  97.050\n", "           SFRZ5 Comdty  97.090\n", "           SFRZ6 Comdty  97.010\n", "2024-09-30 SFRH6 Comdty  97.025\n", "           SFRM6 Comdty  97.020\n", "           SFRU6 Comdty  96.995\n", "           SFRZ5 Comdty  97.000\n", "           SFRZ6 Comdty  96.960\n", "2024-10-01 SFRH6 Comdty  97.080\n", "           SFRM6 Comdty  97.080\n", "           SFRU6 Comdty  97.060\n", "           SFRZ5 Comdty  97.050\n", "           SFRZ6 Comdty  97.025\n", "2024-10-02 SFRH6 Comdty  97.040\n", "           SFRM6 Comdty  97.040\n", "           SFRU6 Comdty  97.020\n", "           SFRZ5 Comdty  97.010\n", "           SFRZ6 Comdty  96.990\n", "2024-10-03 SFRH6 Comdty  96.960\n", "           SFRM6 Comdty  96.960\n", "           SFRU6 Comdty  96.940\n", "           SFRZ5 Comdty  96.925\n", "           SFRZ6 Comdty  96.910\n", "2024-10-04 SFRH6 Comdty  96.715\n", "           SFRM6 Comdty  96.730\n", "           SFRU6 Comdty  96.720\n", "           SFRZ5 Comdty  96.665\n", "           SFRZ6 Comdty  96.705\n", "2024-10-07 SFRH6 Comdty  96.625\n", "           SFRM6 Comdty  96.645\n", "           SFRU6 Comdty  96.645\n", "           SFRZ5 Comdty  96.575\n", "           SFRZ6 Comdty  96.640\n", "2024-10-08 SFRH6 Comdty  96.645\n", "           SFRM6 Comdty  96.660\n", "           SFRU6 Comdty  96.655\n", "           SFRZ5 Comdty  96.600\n", "           SFRZ6 Comdty  96.645\n", "2024-10-09 SFRH6 Comdty  96.600\n", "           SFRM6 Comdty  96.620\n", "           SFRU6 Comdty  96.620\n", "           SFRZ5 Comdty  96.550\n", "           SFRZ6 Comdty  96.610\n", "2024-10-10 SFRH6 Comdty  96.610\n", "           SFRM6 Comdty  96.625\n", "           SFRU6 Comdty  96.620\n", "           SFRZ5 Comdty  96.565\n", "           SFRZ6 Comdty  96.605\n", "2024-10-11 SFRH6 Comdty  96.680\n", "           SFRM6 Comdty  96.690\n", "           SFRU6 Comdty  96.680\n", "           SFRZ5 Comdty  96.640\n", "           SFRZ6 Comdty  96.660"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['SFRH6 Comdty', 'SFRM6 Comdty', 'SFRU6 Comdty', 'SFRZ5 Comdty',\n", "       'SFRZ6 Comdty'],\n", "      dtype='object', name='ticker')"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["data.loc[dt.date(2024,9,17)].index.get_level_values(0)"]}, {"cell_type": "code", "execution_count": 327, "metadata": {}, "outputs": [], "source": ["tickers = [\"SFRM6 Comdty\", \"SFRH6 Comdty\"]"]}, {"cell_type": "code", "execution_count": 330, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 330, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([1, 2], index=tickers, columns=[\"px_last\"]).columns.nlevels"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               price\n", "ticker              \n", "SFRM6 Comdty  97.145\n", "SFRH6 Comdty  97.145"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["data.loc[dt.date(2024,9,17)].loc[tickers]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["new_data = data.loc[dt.date(2024,9,17)].loc[tickers].copy()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>currency</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>USD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GBP</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  currency\n", "0      USD\n", "1      GBP"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([\"USD\",\"GBP\"], columns=[\"currency\"])"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               price\n", "ticker              \n", "SFRM6 Comdty  97.145\n", "SFRH6 Comdty  97.145"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["new_data\n"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([259, 259, 259, 259, 259, 260, 260, 260, 260, 260, 261, 261, 261, 261,\n", "       261, 262, 262, 262, 262, 262, 263, 263, 263, 263, 263, 266, 266, 266,\n", "       266, 266, 267, 267, 267, 267, 267, 268, 268, 268, 268, 268, 269, 269,\n", "       269, 269, 269, 270, 270, 270, 270, 270, 273, 273, 273, 273, 273, 274,\n", "       274, 274, 274, 274, 275, 275, 275, 275, 275, 276, 276, 276, 276, 276,\n", "       277, 277, 277, 277, 277, 280, 280, 280, 280, 280, 281, 281, 281, 281,\n", "       281, 282, 282, 282, 282, 282, 283, 283, 283, 283, 283, 284, 284, 284,\n", "       284, 284],\n", "      dtype='object')"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["data.index.map(lambda x: ql.Date.from_date(x[0])) - ql.Date(1,1,2024)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([96.195, 96.19 , 96.17 , 96.18 , 96.14 , 96.145, 96.135, 96.13 ,\n", "       96.115, 96.095, 96.09 , 96.075, 96.085, 96.055, 96.06 , 96.08 ,\n", "       96.025, 96.105, 96.065, 96.03 , 96.1  , 96.05 , 96.015, 96.12 ,\n", "       96.04 , 95.995, 95.96 , 96.01 , 96.02 , 96.   , 95.99 , 95.94 ,\n", "       95.925, 95.91 , 95.715, 95.73 , 95.72 , 95.665, 95.705, 95.625,\n", "       95.645, 95.575, 95.64 , 95.66 , 95.655, 95.6  , 95.62 , 95.55 ,\n", "       95.61 , 95.565, 95.605, 95.68 , 95.69 ])"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"price\"].unique() -1"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">2024-09-16</th>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.190</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                          price\n", "date       ticker              \n", "2024-09-16 SFRH6 Comdty  97.195\n", "           SFRM6 Comdty  97.190"]}, "execution_count": 70, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head(2)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from loader.ir_future_option_data_scenario_analyzer import interestRateFutureOptionScenarioAnalyzer\n", "live_loader = interestRateFutureOptionScenarioAnalyzer(\"SFR\", debug_mode=True)"]}, {"cell_type": "code", "execution_count": 320, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code    option_expiry_date  tte_bus_days\n", "0        G5   February 14th, 2025            18\n", "1        H5      March 14th, 2025            37\n", "2        J5      April 11th, 2025            57\n", "3        K5        May 16th, 2025            81\n", "4        M5       June 13th, 2025           100\n", "5        N5       July 11th, 2025           118\n", "6        U5  September 12th, 2025           162\n", "7        Z5   December 12th, 2025           226\n", "8        H6      March 13th, 2026           287\n", "9        M6       June 12th, 2026           350\n", "10       U6  September 11th, 2026           412\n", "11       Z6   December 11th, 2026           476\n", "12       H7      March 12th, 2027           537\n", "13       M7       June 11th, 2027           600\n", "February 3rd, 2025\n", "February 18th, 2025\n", "{'SFRH5 Comdty', 'SFRM6 Comdty', 'SFRH6 Comdty'}\n", "{'USOSFR1C Curncy', 'USOSFR3Z Curncy', 'USOSFRA Curncy'}\n", "{'SFRG5P 95 Comdty', 'SFRH6P 97 Comdty', 'SFRH5C 95 Comdty', 'SFRM6C 96 Comdty', 'SFRH5P 95 Comdty', 'SFRG5C 95 Comdty', 'SFRH6C 97 Comdty', 'SFRM6P 96 Comdty'}\n", "['SFRG5C 96.8125 Comdty', 'SFRG5C 96.875 Comdty', 'SFRG5C 97 Comdty', 'SFRG5C 97.0625 Comdty', 'SFRH5C 97.125 Comdty', 'SFRH5C 97.1875 Comdty', 'SFRH5C 97.3125 Comdty', 'SFRH5C 97.375 Comdty', 'SFRH6C 99.25 Comdty', 'SFRH6C 99.3125 Comdty', 'SFRH6C 99.375 Comdty', 'SFRH6C 99.4375 Comdty', 'SFRM6C 99.0625 Comdty', 'SFRM6C 99.125 Comdty', 'SFRM6C 99.1875 Comdty', 'SFRM6C 99.25 Comdty'] 16\n"]}], "source": ["scenarios = live_loader.load_option_price(dt.date(2025,1,20), [\"SFRH5C 96 Comdty\", \"SFRM6C 97 \"], [94], [-10,-20], False)"]}, {"cell_type": "code", "execution_count": 321, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_vol_2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96</td>\n", "      <td>H5</td>\n", "      <td>27</td>\n", "      <td>G5</td>\n", "      <td>18</td>\n", "      <td>H5</td>\n", "      <td>37</td>\n", "      <td>39</td>\n", "      <td>25</td>\n", "      <td>53</td>\n", "      <td>94</td>\n", "      <td>95</td>\n", "      <td>0.995851</td>\n", "      <td>95</td>\n", "      <td>0.991244</td>\n", "      <td>SFRG5C 96.8125 Comdty</td>\n", "      <td>SFRG5C 96.875 Comdty</td>\n", "      <td>SFRH5C 97.125 Comdty</td>\n", "      <td>SFRH5C 97.1875 Comdty</td>\n", "      <td>1.648154</td>\n", "      <td>1.319893</td>\n", "      <td>1.443598</td>\n", "      <td>4.232550</td>\n", "      <td>96.8125</td>\n", "      <td>96.8750</td>\n", "      <td>97.1250</td>\n", "      <td>97.1875</td>\n", "      <td>-14.256341151019853</td>\n", "      <td>-13.681526239766843</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>97</td>\n", "      <td>M6</td>\n", "      <td>340</td>\n", "      <td>H6</td>\n", "      <td>287</td>\n", "      <td>M6</td>\n", "      <td>350</td>\n", "      <td>494</td>\n", "      <td>417</td>\n", "      <td>508</td>\n", "      <td>94</td>\n", "      <td>97</td>\n", "      <td>0.955718</td>\n", "      <td>96</td>\n", "      <td>0.946571</td>\n", "      <td>SFRH6C 99.25 Comdty</td>\n", "      <td>SFRH6C 99.3125 Comdty</td>\n", "      <td>SFRM6C 99.0625 Comdty</td>\n", "      <td>SFRM6C 99.125 Comdty</td>\n", "      <td>0.552970</td>\n", "      <td>0.674100</td>\n", "      <td>0.659163</td>\n", "      <td>3.918226</td>\n", "      <td>99.2500</td>\n", "      <td>99.3125</td>\n", "      <td>99.0625</td>\n", "      <td>99.1250</td>\n", "      <td>-7.317210971182277</td>\n", "      <td>-3.5574199389572527</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>96</td>\n", "      <td>H5</td>\n", "      <td>17</td>\n", "      <td>G5</td>\n", "      <td>18</td>\n", "      <td>H5</td>\n", "      <td>37</td>\n", "      <td>24</td>\n", "      <td>25</td>\n", "      <td>53</td>\n", "      <td>94</td>\n", "      <td>95</td>\n", "      <td>0.99654</td>\n", "      <td>95</td>\n", "      <td>0.992693</td>\n", "      <td>SFRG5C 97 Comdty</td>\n", "      <td>SFRG5C 97.0625 Comdty</td>\n", "      <td>SFRH5C 97.3125 Comdty</td>\n", "      <td>SFRH5C 97.375 Comdty</td>\n", "      <td>1.647014</td>\n", "      <td>1.317967</td>\n", "      <td>1.680599</td>\n", "      <td>4.581858</td>\n", "      <td>97.0000</td>\n", "      <td>97.0625</td>\n", "      <td>97.3125</td>\n", "      <td>97.3750</td>\n", "      <td>-26.76210332638759</td>\n", "      <td>-26.79046978913127</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>97</td>\n", "      <td>M6</td>\n", "      <td>330</td>\n", "      <td>H6</td>\n", "      <td>287</td>\n", "      <td>M6</td>\n", "      <td>350</td>\n", "      <td>479</td>\n", "      <td>417</td>\n", "      <td>508</td>\n", "      <td>94</td>\n", "      <td>97</td>\n", "      <td>0.955718</td>\n", "      <td>96</td>\n", "      <td>0.946571</td>\n", "      <td>SFRH6C 99.375 Comdty</td>\n", "      <td>SFRH6C 99.4375 Comdty</td>\n", "      <td>SFRM6C 99.1875 Comdty</td>\n", "      <td>SFRM6C 99.25 Comdty</td>\n", "      <td>0.552970</td>\n", "      <td>0.674100</td>\n", "      <td>0.642941</td>\n", "      <td>4.077494</td>\n", "      <td>99.3750</td>\n", "      <td>99.4375</td>\n", "      <td>99.1875</td>\n", "      <td>99.2500</td>\n", "      <td>-12.797002532890023</td>\n", "      <td>-20.190885482617173</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "0        C     96       H5            27         G5              18   \n", "1        C     97       M6           340         H6             287   \n", "2        C     96       H5            17         G5              18   \n", "3        C     97       M6           330         H6             287   \n", "\n", "  imm_code_2  tte_bus_days_2  tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "0         H5              37   39    25    53           94      95   \n", "1         M6             350  494   417   508           94      97   \n", "2         H5              37   24    25    53           94      95   \n", "3         M6             350  479   417   508           94      97   \n", "\n", "  discount_factor_1  atmf_2 discount_factor_2             ticker_1_0  \\\n", "0          0.995851      95          0.991244  SFRG5C 96.8125 Comdty   \n", "1          0.955718      96          0.946571    SFRH6C 99.25 Comdty   \n", "2           0.99654      95          0.992693       SFRG5C 97 Comdty   \n", "3          0.955718      96          0.946571   SFRH6C 99.375 Comdty   \n", "\n", "              ticker_1_1             ticker_2_0             ticker_2_1  \\\n", "0   SFRG5C 96.875 Comdty   SFRH5C 97.125 Comdty  SFRH5C 97.1875 Comdty   \n", "1  SFRH6C 99.3125 Comdty  SFRM6C 99.0625 Comdty   SFRM6C 99.125 Comdty   \n", "2  SFRG5C 97.0625 Comdty  SFRH5C 97.3125 Comdty   SFRH5C 97.375 Comdty   \n", "3  SFRH6C 99.4375 Comdty  SFRM6C 99.1875 Comdty    SFRM6C 99.25 Comdty   \n", "\n", "   atm_vol_1  atm_vol_2   atm_vol  moneyness  strike_1_0  strike_1_1  \\\n", "0   1.648154   1.319893  1.443598   4.232550     96.8125     96.8750   \n", "1   0.552970   0.674100  0.659163   3.918226     99.2500     99.3125   \n", "2   1.647014   1.317967  1.680599   4.581858     97.0000     97.0625   \n", "3   0.552970   0.674100  0.642941   4.077494     99.3750     99.4375   \n", "\n", "   strike_2_0  strike_2_1           skew_vol_1           skew_vol_2  \n", "0     97.1250     97.1875  -14.256341151019853  -13.681526239766843  \n", "1     99.0625     99.1250   -7.317210971182277  -3.5574199389572527  \n", "2     97.3125     97.3750   -26.76210332638759   -26.79046978913127  \n", "3     99.1875     99.2500  -12.797002532890023  -20.190885482617173  "]}, "execution_count": 321, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 602, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code    option_expiry_date  tte_bus_days\n", "0        G5   February 14th, 2025            19\n", "1        H5      March 14th, 2025            38\n", "2        J5      April 11th, 2025            58\n", "3        K5        May 16th, 2025            82\n", "4        M5       June 13th, 2025           101\n", "5        N5       July 11th, 2025           119\n", "6        U5  September 12th, 2025           163\n", "7        Z5   December 12th, 2025           227\n", "8        H6      March 13th, 2026           288\n", "9        M6       June 12th, 2026           351\n", "10       U6  September 11th, 2026           413\n", "11       Z6   December 11th, 2026           477\n", "12       H7      March 12th, 2027           538\n", "13       M7       June 11th, 2027           601\n", "February 3rd, 2025\n", "February 12th, 2025\n", "February 14th, 2025\n", "February 18th, 2025\n", "March 4th, 2025\n", "March 13th, 2025\n", "{'SFRH5 Comdty', 'SFRM5 Comdty'}\n", "{'USOSFRB Curncy', 'USOSFR3Z Curncy', 'USOSFR1Z Curncy', 'USOSFRA Curncy'}\n", "{'SFRJ5C 95.875 Comdty', 'SFRH5P 95.75 Comdty', 'SFRH5C 95.75 Comdty', 'SFRG5C 95.75 Comdty', 'SFRG5P 95.75 Comdty', 'SFRJ5P 95.875 Comdty'}\n", "['SFRG5C 96.25 Comdty', 'SFRG5C 96.3125 Comdty', 'SFRG5C 96.375 Comdty', 'SFRG5C 96.4375 Comdty', 'SFRH5C 96.25 Comdty', 'SFRH5C 96.3125 Comdty', 'SFRH5C 96.375 Comdty', 'SFRH5C 96.4375 Comdty', 'SFRJ5C 96.5 Comdty', 'SFRJ5C 96.5625 Comdty'] 10\n"]}], "source": ["scenarios = live_loader.load_option_price(dt.date(2025,1,17), [\"SFRH5C 95.875 Comdty\", \"SFRJ5C 96 Comdty\"], [95.3], [-10,-17, -19, -20, -30, -37], False)"]}, {"cell_type": "code", "execution_count": 653, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code    option_expiry_date  tte_bus_days\n", "0        G5   February 14th, 2025            19\n", "1        H5      March 14th, 2025            38\n", "2        J5      April 11th, 2025            58\n", "3        K5        May 16th, 2025            82\n", "4        M5       June 13th, 2025           101\n", "5        N5       July 11th, 2025           119\n", "6        U5  September 12th, 2025           163\n", "7        Z5   December 12th, 2025           227\n", "8        H6      March 13th, 2026           288\n", "9        M6       June 12th, 2026           351\n", "10       U6  September 11th, 2026           413\n", "11       Z6   December 11th, 2026           477\n", "12       H7      March 12th, 2027           538\n", "13       M7       June 11th, 2027           601\n", "February 3rd, 2025\n", "{'SFRH5 Comdty'}\n", "{'USOSFRA Curncy'}\n", "{'SFRG5P 95.75 Comdty', 'SFRG5C 95.75 Comdty', 'SFRH5P 95.75 Comdty', 'SFRH5C 95.75 Comdty'}\n", "['SFRG5C 96.1875 Comdty', 'SFRG5C 96.25 Comdty', 'SFRH5C 96.4375 Comdty', 'SFRH5C 96.5 Comdty']\n"]}], "source": ["scenarios = live_loader.load_option_price(dt.date(2025,1,17), [\"SFRH5C 95.875 Comdty\"], [95.3], [-10], False, moneyness_type=\"standardized-normal\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code    option_expiry_date  tte_bus_days\n", "0        G5   February 14th, 2025            19\n", "1        H5      March 14th, 2025            38\n", "2        J5      April 11th, 2025            58\n", "3        K5        May 16th, 2025            82\n", "4        M5       June 13th, 2025           101\n", "5        N5       July 11th, 2025           119\n", "6        U5  September 12th, 2025           163\n", "7        Z5   December 12th, 2025           227\n", "8        H6      March 13th, 2026           288\n", "9        M6       June 12th, 2026           351\n", "10       U6  September 11th, 2026           413\n", "11       Z6   December 11th, 2026           477\n", "12       H7      March 12th, 2027           538\n", "13       M7       June 11th, 2027           601\n", "{'SFRH5 Comdty', 'SFRM5 Comdty'}\n", "{'USOSFRA Curncy', 'USOSFRB Curncy', 'USOSFR1Z Curncy', 'USOSFR3Z Curncy'}\n", "{'SFRJ5C 95.875 Comdty', 'SFRJ5P 95.875 Comdty', 'SFRG5P 95.75 Comdty', 'SFRH5P 95.75 Comdty', 'SFRG5C 95.75 Comdty', 'SFRH5C 95.75 Comdty'}\n", "['SFRG5C 96.1875 Comdty', 'SFRG5C 96.25 Comdty', 'SFRG5C 96.3125 Comdty', 'SFRG5C 96.375 Comdty', 'SFRG5C 96.4375 Comdty', 'SFRH5C 96.1875 Comdty', 'SFRH5C 96.25 Comdty', 'SFRH5C 96.3125 Comdty', 'SFRH5C 96.375 Comdty', 'SFRH5C 96.4375 Comdty', 'SFRH5C 96.5 Comdty', 'SFRH5C 96.5625 Comdty', 'SFRH5C 96.625 Comdty', 'SFRH5C 96.6875 Comdty', 'SFRH5C 96.75 Comdty', 'SFRH5C 96.8125 Comdty', 'SFRJ5C 96.75 Comdty', 'SFRJ5C 96.8125 Comdty', 'SFRJ5C 97.0625 Comdty', 'SFRJ5C 97.125 Comdty', 'SFRJ5C 97.25 Comdty', 'SFRJ5C 97.3125 Comdty', 'SFRJ5C 97.375 Comdty', 'SFRJ5C 97.4375 Comdty']\n"]}], "source": ["scenarios = live_loader.load_option_price(dt.date(2025,1,17), [\"SFRH5C 95.875 Comdty\", \"SFRJ5C 96 Comdty\"], [95.3], [-10,-17, -19, -20, -30, -37], False, moneyness_type=\"standardized-normal\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>discount_factor</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>-10</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>39</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>0.99536</td>\n", "      <td>96.230347</td>\n", "      <td>SFRG5C 96.1875 Comdty</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>96.458818</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>5.009496</td>\n", "      <td>96.1875</td>\n", "      <td>96.2500</td>\n", "      <td>96.4375</td>\n", "      <td>96.5000</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9798045425856905</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.965222404646963</td>\n", "      <td>0.970455</td>\n", "      <td>0.004863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>95.3</td>\n", "      <td>-10</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>67</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>0.99205</td>\n", "      <td>96.196046</td>\n", "      <td>SFRH5C 96.1875 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>96.790947</td>\n", "      <td>SFRJ5C 96.75 Comdty</td>\n", "      <td>SFRJ5C 96.8125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.519993</td>\n", "      <td>3.084470</td>\n", "      <td>96.1875</td>\n", "      <td>96.2500</td>\n", "      <td>96.7500</td>\n", "      <td>96.8125</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.7090084492240317</td>\n", "      <td>0.0150</td>\n", "      <td>0.0150</td>\n", "      <td>1.2056529518338492</td>\n", "      <td>1.037882</td>\n", "      <td>0.011866</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>21</td>\n", "      <td>95.3</td>\n", "      <td>-17</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>30</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>0.996427</td>\n", "      <td>96.315405</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.586555</td>\n", "      <td>SFRH5C 96.5625 Comdty</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>5.945262</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.5625</td>\n", "      <td>96.6250</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1255191761598178</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1118687616345053</td>\n", "      <td>1.122932</td>\n", "      <td>0.004924</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>41</td>\n", "      <td>95.3</td>\n", "      <td>-17</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>0.99311</td>\n", "      <td>96.341065</td>\n", "      <td>SFRH5C 96.3125 Comdty</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>97.099534</td>\n", "      <td>SFRJ5C 97.0625 Comdty</td>\n", "      <td>SFRJ5C 97.125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.418494</td>\n", "      <td>4.146840</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>97.0625</td>\n", "      <td>97.1250</td>\n", "      <td>0.0075</td>\n", "      <td>0.0050</td>\n", "      <td>0.8627914714317086</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>1.3390042173672645</td>\n", "      <td>0.983311</td>\n", "      <td>0.006140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>19</td>\n", "      <td>95.3</td>\n", "      <td>-19</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>0.996664</td>\n", "      <td>96.350000</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.638508</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>SFRH5C 96.6875 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.331033</td>\n", "      <td>6.325863</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.6250</td>\n", "      <td>96.6875</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1836635059003213</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1706858470769776</td>\n", "      <td>1.183664</td>\n", "      <td>0.004996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>39</td>\n", "      <td>95.3</td>\n", "      <td>-19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>0.993346</td>\n", "      <td>96.420388</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>97.268325</td>\n", "      <td>SFRJ5C 97.25 Comdty</td>\n", "      <td>SFRJ5C 97.3125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.376352</td>\n", "      <td>4.727939</td>\n", "      <td>96.3750</td>\n", "      <td>96.4375</td>\n", "      <td>97.2500</td>\n", "      <td>97.3125</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9204504985321016</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.3997790287050944</td>\n", "      <td>0.964328</td>\n", "      <td>0.004797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>95.3</td>\n", "      <td>-20</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>52</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>0.993819</td>\n", "      <td>96.475000</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>97.384536</td>\n", "      <td>SFRJ5C 97.375 Comdty</td>\n", "      <td>SFRJ5C 97.4375 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.351526</td>\n", "      <td>5.128015</td>\n", "      <td>96.4375</td>\n", "      <td>96.5000</td>\n", "      <td>97.3750</td>\n", "      <td>97.4375</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.983972935499156</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.4997014900221275</td>\n", "      <td>0.983973</td>\n", "      <td>0.005000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>-30</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>0.995478</td>\n", "      <td>96.329335</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.607475</td>\n", "      <td>SFRH5C 96.5625 Comdty</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>6.098517</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.5625</td>\n", "      <td>96.6250</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.148931902200895</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.135608272967772</td>\n", "      <td>1.140385</td>\n", "      <td>0.004863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>21</td>\n", "      <td>95.3</td>\n", "      <td>-37</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>29</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>0.996546</td>\n", "      <td>96.432884</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>SFRG5C 96.4375 Comdty</td>\n", "      <td>96.762980</td>\n", "      <td>SFRH5C 96.75 Comdty</td>\n", "      <td>SFRH5C 96.8125 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>7.237711</td>\n", "      <td>96.3750</td>\n", "      <td>96.4375</td>\n", "      <td>96.7500</td>\n", "      <td>96.8125</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.3216510104117625</td>\n", "      <td>0.0025</td>\n", "      <td>0.0025</td>\n", "      <td>1.1787574976908937</td>\n", "      <td>1.295649</td>\n", "      <td>0.004435</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump imm_code_1  \\\n", "0         C  95.875       H5            28         95.3      -10         G5   \n", "1         C  96.000       J5            48         95.3      -10         H5   \n", "2         C  95.875       H5            21         95.3      -17         G5   \n", "3         C  96.000       J5            41         95.3      -17         H5   \n", "4         C  95.875       H5            19         95.3      -19         G5   \n", "5         C  96.000       J5            39         95.3      -19         H5   \n", "7         C  96.000       J5            38         95.3      -20         H5   \n", "9         C  96.000       J5            28         95.3      -30         G5   \n", "11        C  96.000       J5            21         95.3      -37         G5   \n", "\n", "    tte_bus_days_1 imm_code_2  tte_bus_days_2 tte tte_1 tte_2  atmf_1  \\\n", "0               19         H5              38  39    28    56  95.775   \n", "1               38         J5              58  67    56    84  95.775   \n", "2               19         H5              38  30    28    56  95.775   \n", "3               38         J5              58  58    56    84  95.775   \n", "4               19         H5              38  28    28    56  95.775   \n", "5               38         J5              58  56    56    84  95.775   \n", "7               38         J5              58  52    56    84  95.775   \n", "9               19         H5              38  38    28    56  95.775   \n", "11              19         H5              38  29    28    56  95.775   \n", "\n", "   discount_factor_1  atmf_2 discount_factor_2 discount_factor   strike_1  \\\n", "0           0.996664  95.775          0.993351         0.99536  96.230347   \n", "1           0.993346  95.895          0.990053         0.99205  96.196046   \n", "2           0.996664  95.775          0.993351        0.996427  96.315405   \n", "3           0.993346  95.895          0.990053         0.99311  96.341065   \n", "4           0.996664  95.775          0.993351        0.996664  96.350000   \n", "5           0.993346  95.895          0.990053        0.993346  96.420388   \n", "7           0.993346  95.895          0.990053        0.993819  96.475000   \n", "9           0.996664  95.775          0.993351        0.995478  96.329335   \n", "11          0.996664  95.775          0.993351        0.996546  96.432884   \n", "\n", "               ticker_1_0             ticker_1_1   strike_2  \\\n", "0   SFRG5C 96.1875 Comdty    SFRG5C 96.25 Comdty  96.458818   \n", "1   SFRH5C 96.1875 Comdty    SFRH5C 96.25 Comdty  96.790947   \n", "2   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.586555   \n", "3   SFRH5C 96.3125 Comdty   SFRH5C 96.375 Comdty  97.099534   \n", "4   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.638508   \n", "5    SFRH5C 96.375 Comdty  SFRH5C 96.4375 Comdty  97.268325   \n", "7   SFRH5C 96.4375 Comdty     SFRH5C 96.5 Comdty  97.384536   \n", "9   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.607475   \n", "11   SFRG5C 96.375 Comdty  SFRG5C 96.4375 Comdty  96.762980   \n", "\n", "               ticker_2_0             ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0   SFRH5C 96.4375 Comdty     SFRH5C 96.5 Comdty   0.331033   0.351524   \n", "1     SFRJ5C 96.75 Comdty  SFRJ5C 96.8125 Comdty   0.351526   0.605463   \n", "2   SFRH5C 96.5625 Comdty   SFRH5C 96.625 Comdty   0.331033   0.351524   \n", "3   SFRJ5C 97.0625 Comdty   SFRJ5C 97.125 Comdty   0.351526   0.605463   \n", "4    SFRH5C 96.625 Comdty  SFRH5C 96.6875 Comdty   0.331033   0.351524   \n", "5     SFRJ5C 97.25 Comdty  SFRJ5C 97.3125 Comdty   0.351526   0.605463   \n", "7    SFRJ5C 97.375 Comdty  SFRJ5C 97.4375 Comdty   0.351526   0.605463   \n", "9   SFRH5C 96.5625 Comdty   SFRH5C 96.625 Comdty   0.331033   0.351524   \n", "11    SFRH5C 96.75 Comdty  SFRH5C 96.8125 Comdty   0.331033   0.351524   \n", "\n", "     atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0   0.344346   5.009496     96.1875     96.2500     96.4375     96.5000   \n", "1   0.519993   3.084470     96.1875     96.2500     96.7500     96.8125   \n", "2   0.335033   5.945262     96.3125     96.3750     96.5625     96.6250   \n", "3   0.418494   4.146840     96.3125     96.3750     97.0625     97.1250   \n", "4   0.331033   6.325863     96.3125     96.3750     96.6250     96.6875   \n", "5   0.376352   4.727939     96.3750     96.4375     97.2500     97.3125   \n", "7   0.351526   5.128015     96.4375     96.5000     97.3750     97.4375   \n", "9   0.344346   6.098517     96.3125     96.3750     96.5625     96.6250   \n", "11  0.335033   7.237711     96.3750     96.4375     96.7500     96.8125   \n", "\n", "    skew_px_1_0  skew_px_1_1          skew_vol_1  skew_px_2_0  skew_px_2_1  \\\n", "0        0.0050       0.0050  0.9798045425856905       0.0050       0.0050   \n", "1        0.0075       0.0075  0.7090084492240317       0.0150       0.0150   \n", "2        0.0050       0.0050  1.1255191761598178       0.0050       0.0050   \n", "3        0.0075       0.0050  0.8627914714317086       0.0075       0.0075   \n", "4        0.0050       0.0050  1.1836635059003213       0.0050       0.0050   \n", "5        0.0050       0.0050  0.9204504985321016       0.0050       0.0050   \n", "7        0.0050       0.0050   0.983972935499156       0.0050       0.0050   \n", "9        0.0050       0.0050   1.148931902200895       0.0050       0.0050   \n", "11       0.0050       0.0050  1.3216510104117625       0.0025       0.0025   \n", "\n", "            skew_vol_2       vol  option_px  \n", "0    0.965222404646963  0.970455   0.004863  \n", "1   1.2056529518338492  1.037882   0.011866  \n", "2   1.1118687616345053  1.122932   0.004924  \n", "3   1.3390042173672645  0.983311   0.006140  \n", "4   1.1706858470769776  1.183664   0.004996  \n", "5   1.3997790287050944  0.964328   0.004797  \n", "7   1.4997014900221275  0.983973   0.005000  \n", "9    1.135608272967772  1.140385   0.004863  \n", "11  1.1787574976908937  1.295649   0.004435  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"scenarios\"]"]}, {"cell_type": "code", "execution_count": 617, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>call_px_final</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>39</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.230347</td>\n", "      <td>SFRG5C 96.1875 Comdty</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>96.458818</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>5.009496</td>\n", "      <td>96.1875</td>\n", "      <td>96.2500</td>\n", "      <td>96.4375</td>\n", "      <td>96.5000</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9798045425856905</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.965222404646963</td>\n", "      <td>0.970455</td>\n", "      <td>0.004870</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>67</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.196046</td>\n", "      <td>SFRH5C 96.1875 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>96.790947</td>\n", "      <td>SFRJ5C 96.75 Comdty</td>\n", "      <td>SFRJ5C 96.8125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.519993</td>\n", "      <td>3.084470</td>\n", "      <td>96.1875</td>\n", "      <td>96.2500</td>\n", "      <td>96.7500</td>\n", "      <td>96.8125</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.7090084492240317</td>\n", "      <td>0.0150</td>\n", "      <td>0.0150</td>\n", "      <td>1.2056529518338492</td>\n", "      <td>1.037882</td>\n", "      <td>0.011882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>21</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>30</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.315405</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.586555</td>\n", "      <td>SFRH5C 96.5625 Comdty</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>5.945262</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.5625</td>\n", "      <td>96.6250</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1255191761598178</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1118687616345053</td>\n", "      <td>1.122932</td>\n", "      <td>0.004925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>41</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.341065</td>\n", "      <td>SFRH5C 96.3125 Comdty</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>97.099534</td>\n", "      <td>SFRJ5C 97.0625 Comdty</td>\n", "      <td>SFRJ5C 97.125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.418494</td>\n", "      <td>4.146840</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>97.0625</td>\n", "      <td>97.1250</td>\n", "      <td>0.0075</td>\n", "      <td>0.0050</td>\n", "      <td>0.8627914714317086</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>1.3390042173672645</td>\n", "      <td>0.983311</td>\n", "      <td>0.006141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>19</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.350000</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.638508</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>SFRH5C 96.6875 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.331033</td>\n", "      <td>6.325863</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.6250</td>\n", "      <td>96.6875</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1836635059003213</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.1706858470769776</td>\n", "      <td>1.183664</td>\n", "      <td>0.004996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>39</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.420388</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>97.268325</td>\n", "      <td>SFRJ5C 97.25 Comdty</td>\n", "      <td>SFRJ5C 97.3125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.376352</td>\n", "      <td>4.727939</td>\n", "      <td>96.3750</td>\n", "      <td>96.4375</td>\n", "      <td>97.2500</td>\n", "      <td>97.3125</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9204504985321016</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.3997790287050944</td>\n", "      <td>0.964328</td>\n", "      <td>0.004797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>52</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.475000</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>97.384536</td>\n", "      <td>SFRJ5C 97.375 Comdty</td>\n", "      <td>SFRJ5C 97.4375 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.351526</td>\n", "      <td>5.128015</td>\n", "      <td>96.4375</td>\n", "      <td>96.5000</td>\n", "      <td>97.3750</td>\n", "      <td>97.4375</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.983972935499156</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.4997014900221275</td>\n", "      <td>0.983973</td>\n", "      <td>0.004997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>28</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.329335</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.607475</td>\n", "      <td>SFRH5C 96.5625 Comdty</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>6.098517</td>\n", "      <td>96.3125</td>\n", "      <td>96.3750</td>\n", "      <td>96.5625</td>\n", "      <td>96.6250</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.148931902200895</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.135608272967772</td>\n", "      <td>1.140385</td>\n", "      <td>0.004869</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>21</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>29</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.432884</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>SFRG5C 96.4375 Comdty</td>\n", "      <td>96.762980</td>\n", "      <td>SFRH5C 96.75 Comdty</td>\n", "      <td>SFRH5C 96.8125 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>7.237711</td>\n", "      <td>96.3750</td>\n", "      <td>96.4375</td>\n", "      <td>96.7500</td>\n", "      <td>96.8125</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>1.3216510104117625</td>\n", "      <td>0.0025</td>\n", "      <td>0.0025</td>\n", "      <td>1.1787574976908937</td>\n", "      <td>1.295649</td>\n", "      <td>0.004435</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "0         C  95.875       H5            28         G5              19   \n", "1         C  96.000       J5            48         H5              38   \n", "2         C  95.875       H5            21         G5              19   \n", "3         C  96.000       J5            41         H5              38   \n", "4         C  95.875       H5            19         G5              19   \n", "5         C  96.000       J5            39         H5              38   \n", "7         C  96.000       J5            38         H5              38   \n", "9         C  96.000       J5            28         G5              19   \n", "11        C  96.000       J5            21         G5              19   \n", "\n", "   imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "0          H5              38  39    28    56         95.3  95.775   \n", "1          J5              58  67    56    84         95.3  95.775   \n", "2          H5              38  30    28    56         95.3  95.775   \n", "3          J5              58  58    56    84         95.3  95.775   \n", "4          H5              38  28    28    56         95.3  95.775   \n", "5          J5              58  56    56    84         95.3  95.775   \n", "7          J5              58  52    56    84         95.3  95.775   \n", "9          H5              38  38    28    56         95.3  95.775   \n", "11         H5              38  29    28    56         95.3  95.775   \n", "\n", "   discount_factor_1  atmf_2 discount_factor_2   strike_1  \\\n", "0           0.996664  95.775          0.993351  96.230347   \n", "1           0.993346  95.895          0.990053  96.196046   \n", "2           0.996664  95.775          0.993351  96.315405   \n", "3           0.993346  95.895          0.990053  96.341065   \n", "4           0.996664  95.775          0.993351  96.350000   \n", "5           0.993346  95.895          0.990053  96.420388   \n", "7           0.993346  95.895          0.990053  96.475000   \n", "9           0.996664  95.775          0.993351  96.329335   \n", "11          0.996664  95.775          0.993351  96.432884   \n", "\n", "               ticker_1_0             ticker_1_1   strike_2  \\\n", "0   SFRG5C 96.1875 Comdty    SFRG5C 96.25 Comdty  96.458818   \n", "1   SFRH5C 96.1875 Comdty    SFRH5C 96.25 Comdty  96.790947   \n", "2   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.586555   \n", "3   SFRH5C 96.3125 Comdty   SFRH5C 96.375 Comdty  97.099534   \n", "4   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.638508   \n", "5    SFRH5C 96.375 Comdty  SFRH5C 96.4375 Comdty  97.268325   \n", "7   SFRH5C 96.4375 Comdty     SFRH5C 96.5 Comdty  97.384536   \n", "9   SFRG5C 96.3125 Comdty   SFRG5C 96.375 Comdty  96.607475   \n", "11   SFRG5C 96.375 Comdty  SFRG5C 96.4375 Comdty  96.762980   \n", "\n", "               ticker_2_0             ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0   SFRH5C 96.4375 Comdty     SFRH5C 96.5 Comdty   0.331033   0.351524   \n", "1     SFRJ5C 96.75 Comdty  SFRJ5C 96.8125 Comdty   0.351526   0.605463   \n", "2   SFRH5C 96.5625 Comdty   SFRH5C 96.625 Comdty   0.331033   0.351524   \n", "3   SFRJ5C 97.0625 Comdty   SFRJ5C 97.125 Comdty   0.351526   0.605463   \n", "4    SFRH5C 96.625 Comdty  SFRH5C 96.6875 Comdty   0.331033   0.351524   \n", "5     SFRJ5C 97.25 Comdty  SFRJ5C 97.3125 Comdty   0.351526   0.605463   \n", "7    SFRJ5C 97.375 Comdty  SFRJ5C 97.4375 Comdty   0.351526   0.605463   \n", "9   SFRH5C 96.5625 Comdty   SFRH5C 96.625 Comdty   0.331033   0.351524   \n", "11    SFRH5C 96.75 Comdty  SFRH5C 96.8125 Comdty   0.331033   0.351524   \n", "\n", "     atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0   0.344346   5.009496     96.1875     96.2500     96.4375     96.5000   \n", "1   0.519993   3.084470     96.1875     96.2500     96.7500     96.8125   \n", "2   0.335033   5.945262     96.3125     96.3750     96.5625     96.6250   \n", "3   0.418494   4.146840     96.3125     96.3750     97.0625     97.1250   \n", "4   0.331033   6.325863     96.3125     96.3750     96.6250     96.6875   \n", "5   0.376352   4.727939     96.3750     96.4375     97.2500     97.3125   \n", "7   0.351526   5.128015     96.4375     96.5000     97.3750     97.4375   \n", "9   0.344346   6.098517     96.3125     96.3750     96.5625     96.6250   \n", "11  0.335033   7.237711     96.3750     96.4375     96.7500     96.8125   \n", "\n", "    skew_px_1_0  skew_px_1_1          skew_vol_1  skew_px_2_0  skew_px_2_1  \\\n", "0        0.0050       0.0050  0.9798045425856905       0.0050       0.0050   \n", "1        0.0075       0.0075  0.7090084492240317       0.0150       0.0150   \n", "2        0.0050       0.0050  1.1255191761598178       0.0050       0.0050   \n", "3        0.0075       0.0050  0.8627914714317086       0.0075       0.0075   \n", "4        0.0050       0.0050  1.1836635059003213       0.0050       0.0050   \n", "5        0.0050       0.0050  0.9204504985321016       0.0050       0.0050   \n", "7        0.0050       0.0050   0.983972935499156       0.0050       0.0050   \n", "9        0.0050       0.0050   1.148931902200895       0.0050       0.0050   \n", "11       0.0050       0.0050  1.3216510104117625       0.0025       0.0025   \n", "\n", "            skew_vol_2       vol  call_px_final  \n", "0    0.965222404646963  0.970455       0.004870  \n", "1   1.2056529518338492  1.037882       0.011882  \n", "2   1.1118687616345053  1.122932       0.004925  \n", "3   1.3390042173672645  0.983311       0.006141  \n", "4   1.1706858470769776  1.183664       0.004996  \n", "5   1.3997790287050944  0.964328       0.004797  \n", "7   1.4997014900221275  0.983973       0.004997  \n", "9    1.135608272967772  1.140385       0.004869  \n", "11  1.1787574976908937  1.295649       0.004435  "]}, "execution_count": 617, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[0]"]}, {"cell_type": "code", "execution_count": 618, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>call_px_at_0</th>\n", "      <th>call_px_2</th>\n", "      <th>call_px</th>\n", "      <th>vol</th>\n", "      <th>call_px_final</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>18</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331033</td>\n", "      <td>0.331033</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.0852565071259068</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.580422</td>\n", "      <td>1.234029</td>\n", "      <td>0.005403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>8</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331030</td>\n", "      <td>0.331030</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.08525467220431</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.578614</td>\n", "      <td>1.720983</td>\n", "      <td>0.003602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>1</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331030</td>\n", "      <td>0.331030</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.08525467220431</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.576278</td>\n", "      <td>4.149278</td>\n", "      <td>0.001274</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "6         C  95.875       H5            18         G5               0   \n", "8         C  95.875       H5             8         G5               0   \n", "10        C  95.875       H5             1         G5               0   \n", "\n", "   imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "6          G5              19  24     0    28         95.3  95.775   \n", "8          G5              19  10     0    28         95.3  95.775   \n", "10         G5              19   1     0    28         95.3  95.775   \n", "\n", "   discount_factor_1  atmf_2 discount_factor_2  strike_1           ticker_1_0  \\\n", "6           0.996664  95.775          0.996664     95.75  SFRG5C 95.75 Comdty   \n", "8           0.996673  95.775          0.996673     95.75  SFRG5C 95.75 Comdty   \n", "10          0.996673  95.775          0.996673     95.75  SFRG5C 95.75 Comdty   \n", "\n", "             ticker_1_1   strike_2           ticker_2_0  \\\n", "6   SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "8   SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "10  SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "\n", "               ticker_2_1  atm_vol_1  atm_vol_2   atm_vol  moneyness  \\\n", "6   SFRG5C 96.3125 Comdty        0.0   0.331033  0.331033   0.130496   \n", "8   SFRG5C 96.3125 Comdty        0.0   0.331030  0.331030   0.130496   \n", "10  SFRG5C 96.3125 Comdty        0.0   0.331030  0.331030   0.130496   \n", "\n", "    strike_2_0  strike_2_1  skew_px_2_0  skew_px_2_1          skew_vol_2  \\\n", "6        96.25     96.3125        0.005        0.005  1.0852565071259068   \n", "8        96.25     96.3125        0.005        0.005    1.08525467220431   \n", "10       96.25     96.3125        0.005        0.005    1.08525467220431   \n", "\n", "    call_px_at_0  call_px_2   call_px       vol  call_px_final  \n", "6          0.575   0.521895  0.580422  1.234029       0.005403  \n", "8          0.575   0.521895  0.578614  1.720983       0.003602  \n", "10         0.575   0.521895  0.576278  4.149278       0.001274  "]}, "execution_count": 618, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[1]"]}, {"cell_type": "code", "execution_count": 619, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>call_px_final</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>28</td>\n", "      <td>0.970455</td>\n", "      <td>0.004870</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>48</td>\n", "      <td>1.037882</td>\n", "      <td>0.011882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>21</td>\n", "      <td>1.122932</td>\n", "      <td>0.004925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>41</td>\n", "      <td>0.983311</td>\n", "      <td>0.006141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>19</td>\n", "      <td>1.183664</td>\n", "      <td>0.004996</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>39</td>\n", "      <td>0.964328</td>\n", "      <td>0.004797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>18</td>\n", "      <td>1.234029</td>\n", "      <td>0.005403</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>38</td>\n", "      <td>0.983973</td>\n", "      <td>0.004997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>8</td>\n", "      <td>1.720983</td>\n", "      <td>0.003602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>28</td>\n", "      <td>1.140385</td>\n", "      <td>0.004869</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>95.3</td>\n", "      <td>1</td>\n", "      <td>4.149278</td>\n", "      <td>0.001274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>95.3</td>\n", "      <td>21</td>\n", "      <td>1.295649</td>\n", "      <td>0.004435</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  fut_px_bump  tte_bus_days       vol  \\\n", "0         C  95.875       H5         95.3            28  0.970455   \n", "1         C  96.000       J5         95.3            48  1.037882   \n", "2         C  95.875       H5         95.3            21  1.122932   \n", "3         C  96.000       J5         95.3            41  0.983311   \n", "4         C  95.875       H5         95.3            19  1.183664   \n", "5         C  96.000       J5         95.3            39  0.964328   \n", "6         C  95.875       H5         95.3            18  1.234029   \n", "7         C  96.000       J5         95.3            38  0.983973   \n", "8         C  95.875       H5         95.3             8  1.720983   \n", "9         C  96.000       J5         95.3            28  1.140385   \n", "10        C  95.875       H5         95.3             1  4.149278   \n", "11        C  96.000       J5         95.3            21  1.295649   \n", "\n", "    call_px_final  \n", "0        0.004870  \n", "1        0.011882  \n", "2        0.004925  \n", "3        0.006141  \n", "4        0.004996  \n", "5        0.004797  \n", "6        0.005403  \n", "7        0.004997  \n", "8        0.003602  \n", "9        0.004869  \n", "10       0.001274  \n", "11       0.004435  "]}, "execution_count": 619, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[2]"]}, {"cell_type": "code", "execution_count": 584, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>0.930737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>0.957602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>21</td>\n", "      <td>1.041865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>41</td>\n", "      <td>0.921511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>19</td>\n", "      <td>1.085257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>39</td>\n", "      <td>0.908479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>18</td>\n", "      <td>1.234029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>0.901371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>8</td>\n", "      <td>1.720983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>28</td>\n", "      <td>1.049731</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>1</td>\n", "      <td>4.149278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>21</td>\n", "      <td>1.211856</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days       vol\n", "0         C  95.875       H5            28  0.930737\n", "1         C  96.000       J5            48  0.957602\n", "2         C  95.875       H5            21  1.041865\n", "3         C  96.000       J5            41  0.921511\n", "4         C  95.875       H5            19  1.085257\n", "5         C  96.000       J5            39  0.908479\n", "6         C  95.875       H5            18  1.234029\n", "7         C  96.000       J5            38  0.901371\n", "8         C  95.875       H5             8  1.720983\n", "9         C  96.000       J5            28  1.049731\n", "10        C  95.875       H5             1  4.149278\n", "11        C  96.000       J5            21  1.211856"]}, "execution_count": 584, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[2]"]}, {"cell_type": "code", "execution_count": 581, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>39</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>SFRH5C 96.3125 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>0.130496</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.0852565182949663</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.8325923384244941</td>\n", "      <td>0.930737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>67</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRJ5C 96.5 Comdty</td>\n", "      <td>SFRJ5C 96.5625 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.519993</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.500</td>\n", "      <td>96.5625</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>0.9013708069994109</td>\n", "      <td>0.0225</td>\n", "      <td>0.0200</td>\n", "      <td>0.9927179882938271</td>\n", "      <td>0.957602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>21</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>30</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>SFRH5C 96.3125 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>0.130496</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.0852565182949663</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.8325923384244941</td>\n", "      <td>1.041865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>41</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRJ5C 96.5 Comdty</td>\n", "      <td>SFRJ5C 96.5625 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.418494</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.500</td>\n", "      <td>96.5625</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>0.9013708069994109</td>\n", "      <td>0.0225</td>\n", "      <td>0.0200</td>\n", "      <td>0.9927179882938271</td>\n", "      <td>0.921511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>19</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>SFRH5C 96.3125 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.331033</td>\n", "      <td>0.130496</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>96.250</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.0852565182949663</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.8325923384244941</td>\n", "      <td>1.085257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>39</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>56</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRJ5C 96.5 Comdty</td>\n", "      <td>SFRJ5C 96.5625 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.376352</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.500</td>\n", "      <td>96.5625</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>0.9013708069994109</td>\n", "      <td>0.0225</td>\n", "      <td>0.0200</td>\n", "      <td>0.9927179882938271</td>\n", "      <td>0.908479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>52</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRJ5C 96.5 Comdty</td>\n", "      <td>SFRJ5C 96.5625 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.351526</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.500</td>\n", "      <td>96.5625</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>0.9013708069994109</td>\n", "      <td>0.0225</td>\n", "      <td>0.0200</td>\n", "      <td>0.9927179882938271</td>\n", "      <td>0.901371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>28</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>38</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>SFRG5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.2739612351051421</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9013700802551669</td>\n", "      <td>1.049731</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>21</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>29</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>SFRG5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.375 Comdty</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.335033</td>\n", "      <td>0.161268</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>96.375</td>\n", "      <td>96.4375</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.2739612351051421</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.9013700802551669</td>\n", "      <td>1.211856</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "0         C  95.875       H5            28         G5              19   \n", "1         C  96.000       J5            48         H5              38   \n", "2         C  95.875       H5            21         G5              19   \n", "3         C  96.000       J5            41         H5              38   \n", "4         C  95.875       H5            19         G5              19   \n", "5         C  96.000       J5            39         H5              38   \n", "7         C  96.000       J5            38         H5              38   \n", "9         C  96.000       J5            28         G5              19   \n", "11        C  96.000       J5            21         G5              19   \n", "\n", "   imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "0          H5              38  39    28    56         95.3  95.775   \n", "1          J5              58  67    56    84         95.3  95.775   \n", "2          H5              38  30    28    56         95.3  95.775   \n", "3          J5              58  58    56    84         95.3  95.775   \n", "4          H5              38  28    28    56         95.3  95.775   \n", "5          J5              58  56    56    84         95.3  95.775   \n", "7          J5              58  52    56    84         95.3  95.775   \n", "9          H5              38  38    28    56         95.3  95.775   \n", "11         H5              38  29    28    56         95.3  95.775   \n", "\n", "   discount_factor_1  atmf_2 discount_factor_2            ticker_1_0  \\\n", "0           0.996664  95.775          0.993351   SFRG5C 96.25 Comdty   \n", "1           0.993346  95.895          0.990053  SFRH5C 96.375 Comdty   \n", "2           0.996664  95.775          0.993351   SFRG5C 96.25 Comdty   \n", "3           0.993346  95.895          0.990053  SFRH5C 96.375 Comdty   \n", "4           0.996664  95.775          0.993351   SFRG5C 96.25 Comdty   \n", "5           0.993346  95.895          0.990053  SFRH5C 96.375 Comdty   \n", "7           0.993346  95.895          0.990053  SFRH5C 96.375 Comdty   \n", "9           0.996664  95.775          0.993351  SFRG5C 96.375 Comdty   \n", "11          0.996664  95.775          0.993351  SFRG5C 96.375 Comdty   \n", "\n", "               ticker_1_1            ticker_2_0             ticker_2_1  \\\n", "0   SFRG5C 96.3125 Comdty   SFRH5C 96.25 Comdty  SFRH5C 96.3125 Comdty   \n", "1   SFRH5C 96.4375 Comdty    SFRJ5C 96.5 Comdty  SFRJ5C 96.5625 Comdty   \n", "2   SFRG5C 96.3125 Comdty   SFRH5C 96.25 Comdty  SFRH5C 96.3125 Comdty   \n", "3   SFRH5C 96.4375 Comdty    SFRJ5C 96.5 Comdty  SFRJ5C 96.5625 Comdty   \n", "4   SFRG5C 96.3125 Comdty   SFRH5C 96.25 Comdty  SFRH5C 96.3125 Comdty   \n", "5   SFRH5C 96.4375 Comdty    SFRJ5C 96.5 Comdty  SFRJ5C 96.5625 Comdty   \n", "7   SFRH5C 96.4375 Comdty    SFRJ5C 96.5 Comdty  SFRJ5C 96.5625 Comdty   \n", "9   SFRG5C 96.4375 Comdty  SFRH5C 96.375 Comdty  SFRH5C 96.4375 Comdty   \n", "11  SFRG5C 96.4375 Comdty  SFRH5C 96.375 Comdty  SFRH5C 96.4375 Comdty   \n", "\n", "    atm_vol_1  atm_vol_2   atm_vol  moneyness  strike_1_0  strike_1_1  \\\n", "0    0.331033   0.351524  0.344346   0.130496      96.250     96.3125   \n", "1    0.351526   0.605463  0.519993   0.161268      96.375     96.4375   \n", "2    0.331033   0.351524  0.335033   0.130496      96.250     96.3125   \n", "3    0.351526   0.605463  0.418494   0.161268      96.375     96.4375   \n", "4    0.331033   0.351524  0.331033   0.130496      96.250     96.3125   \n", "5    0.351526   0.605463  0.376352   0.161268      96.375     96.4375   \n", "7    0.351526   0.605463  0.351526   0.161268      96.375     96.4375   \n", "9    0.331033   0.351524  0.344346   0.161268      96.375     96.4375   \n", "11   0.331033   0.351524  0.335033   0.161268      96.375     96.4375   \n", "\n", "    strike_2_0  strike_2_1  skew_px_1_0  skew_px_1_1          skew_vol_1  \\\n", "0       96.250     96.3125        0.005        0.005  1.0852565182949663   \n", "1       96.500     96.5625        0.005        0.005  0.9013708069994109   \n", "2       96.250     96.3125        0.005        0.005  1.0852565182949663   \n", "3       96.500     96.5625        0.005        0.005  0.9013708069994109   \n", "4       96.250     96.3125        0.005        0.005  1.0852565182949663   \n", "5       96.500     96.5625        0.005        0.005  0.9013708069994109   \n", "7       96.500     96.5625        0.005        0.005  0.9013708069994109   \n", "9       96.375     96.4375        0.005        0.005  1.2739612351051421   \n", "11      96.375     96.4375        0.005        0.005  1.2739612351051421   \n", "\n", "    skew_px_2_0  skew_px_2_1          skew_vol_2       vol  \n", "0        0.0075       0.0075  0.8325923384244941  0.930737  \n", "1        0.0225       0.0200  0.9927179882938271  0.957602  \n", "2        0.0075       0.0075  0.8325923384244941  1.041865  \n", "3        0.0225       0.0200  0.9927179882938271  0.921511  \n", "4        0.0075       0.0075  0.8325923384244941  1.085257  \n", "5        0.0225       0.0200  0.9927179882938271  0.908479  \n", "7        0.0225       0.0200  0.9927179882938271  0.901371  \n", "9        0.0050       0.0050  0.9013700802551669  1.049731  \n", "11       0.0050       0.0050  0.9013700802551669  1.211856  "]}, "execution_count": 581, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[0]"]}, {"cell_type": "code", "execution_count": 582, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>call_px_at_0</th>\n", "      <th>call_px_2</th>\n", "      <th>call_px</th>\n", "      <th>vol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>18</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>24</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331033</td>\n", "      <td>0.331033</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.0852565071259068</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.580422</td>\n", "      <td>1.234029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>8</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331030</td>\n", "      <td>0.331030</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.08525467220431</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.578614</td>\n", "      <td>1.720983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>1</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>28</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.775</td>\n", "      <td>0.996673</td>\n", "      <td>95.75</td>\n", "      <td>SFRG5C 95.75 Comdty</td>\n", "      <td>SFRG5P 95.75 Comdty</td>\n", "      <td>96.291888</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.331030</td>\n", "      <td>0.331030</td>\n", "      <td>0.130496</td>\n", "      <td>96.25</td>\n", "      <td>96.3125</td>\n", "      <td>0.005</td>\n", "      <td>0.005</td>\n", "      <td>1.08525467220431</td>\n", "      <td>0.575</td>\n", "      <td>0.521895</td>\n", "      <td>0.576278</td>\n", "      <td>4.149278</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "6         C  95.875       H5            18         G5               0   \n", "8         C  95.875       H5             8         G5               0   \n", "10        C  95.875       H5             1         G5               0   \n", "\n", "   imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "6          G5              19  24     0    28         95.3  95.775   \n", "8          G5              19  10     0    28         95.3  95.775   \n", "10         G5              19   1     0    28         95.3  95.775   \n", "\n", "   discount_factor_1  atmf_2 discount_factor_2  strike_1           ticker_1_0  \\\n", "6           0.996664  95.775          0.996664     95.75  SFRG5C 95.75 Comdty   \n", "8           0.996673  95.775          0.996673     95.75  SFRG5C 95.75 Comdty   \n", "10          0.996673  95.775          0.996673     95.75  SFRG5C 95.75 Comdty   \n", "\n", "             ticker_1_1   strike_2           ticker_2_0  \\\n", "6   SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "8   SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "10  SFRG5P 95.75 Comdty  96.291888  SFRG5C 96.25 Comdty   \n", "\n", "               ticker_2_1  atm_vol_1  atm_vol_2   atm_vol  moneyness  \\\n", "6   SFRG5C 96.3125 Comdty        0.0   0.331033  0.331033   0.130496   \n", "8   SFRG5C 96.3125 Comdty        0.0   0.331030  0.331030   0.130496   \n", "10  SFRG5C 96.3125 Comdty        0.0   0.331030  0.331030   0.130496   \n", "\n", "    strike_2_0  strike_2_1  skew_px_2_0  skew_px_2_1          skew_vol_2  \\\n", "6        96.25     96.3125        0.005        0.005  1.0852565071259068   \n", "8        96.25     96.3125        0.005        0.005    1.08525467220431   \n", "10       96.25     96.3125        0.005        0.005    1.08525467220431   \n", "\n", "    call_px_at_0  call_px_2   call_px       vol  \n", "6          0.575   0.521895  0.580422  1.234029  \n", "8          0.575   0.521895  0.578614  1.720983  \n", "10         0.575   0.521895  0.576278  4.149278  "]}, "execution_count": 582, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[1]"]}, {"cell_type": "code", "execution_count": 583, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>0.930737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>0.957602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>21</td>\n", "      <td>1.041865</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>41</td>\n", "      <td>0.921511</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>19</td>\n", "      <td>1.085257</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>39</td>\n", "      <td>0.908479</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>18</td>\n", "      <td>1.234029</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>0.901371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>8</td>\n", "      <td>1.720983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>28</td>\n", "      <td>1.049731</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>1</td>\n", "      <td>4.149278</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>C</td>\n", "      <td>96.000</td>\n", "      <td>J5</td>\n", "      <td>21</td>\n", "      <td>1.211856</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   call_put  strike imm_code  tte_bus_days       vol\n", "0         C  95.875       H5            28  0.930737\n", "1         C  96.000       J5            48  0.957602\n", "2         C  95.875       H5            21  1.041865\n", "3         C  96.000       J5            41  0.921511\n", "4         C  95.875       H5            19  1.085257\n", "5         C  96.000       J5            39  0.908479\n", "6         C  95.875       H5            18  1.234029\n", "7         C  96.000       J5            38  0.901371\n", "8         C  95.875       H5             8  1.720983\n", "9         C  96.000       J5            28  1.049731\n", "10        C  95.875       H5             1  4.149278\n", "11        C  96.000       J5            21  1.211856"]}, "execution_count": 583, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[2]"]}, {"cell_type": "code", "execution_count": 551, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.014617051728503"]}, "execution_count": 551, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.bachelierBlackFormulaImpliedVol(\n", "                1,\n", "                100 - 95.875,\n", "                100 - 95.3,\n", "                1 / 252,\n", "                0.576,\n", "                1,\n", "            )"]}, {"cell_type": "code", "execution_count": 564, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.13939393939394007 0.13939384786651388\n", "0.5750000000000028 0.5168879999999945\n"]}], "source": ["print((95.875 - 95.3) / (100 - 95.875), (96.291888 - 95.775) / (100-96.291888))\n", "print(95.875 - 95.3, 96.291888 - 95.775)"]}, {"cell_type": "code", "execution_count": 565, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5749996224493698"]}, "execution_count": 565, "metadata": {}, "output_type": "execute_result"}], "source": ["0.5168879999999945 / (100 - 96.291888) * (100 - 95.875)"]}, {"cell_type": "code", "execution_count": 559, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.576277739914104"]}, "execution_count": 559, "metadata": {}, "output_type": "execute_result"}], "source": ["0.575 * (np.sqrt(19) - np.sqrt(1))/ np.sqrt(19) + 0.521895 / (100-96.291888) * np.sqrt(1)/ np.sqrt(19) * (100-95.875)"]}, {"cell_type": "code", "execution_count": 514, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5750000000000028"]}, "execution_count": 514, "metadata": {}, "output_type": "execute_result"}], "source": ["95.875-95.3"]}, {"cell_type": "code", "execution_count": 675, "metadata": {}, "outputs": [], "source": ["data = blp.bdp(['SFRG5C 96.1875 Comdty', 'SFRG5C 96.25 Comdty', 'SFRH5C 96.375 Comdty', 'SFRH5C 96.4375 Comdty'], \"PX_MID\")"]}, {"cell_type": "code", "execution_count": 677, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'SFRH5C 96.375 Comdty', 'SFRH5C 96.4375 Comdty'}"]}, "execution_count": 677, "metadata": {}, "output_type": "execute_result"}], "source": ["set(data.index)"]}, {"cell_type": "code", "execution_count": 724, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   imm_code    option_expiry_date  tte_bus_days\n", "0        G5   February 14th, 2025            14\n", "1        H5      March 14th, 2025            33\n", "2        J5      April 11th, 2025            53\n", "3        K5        May 16th, 2025            77\n", "4        M5       June 13th, 2025            96\n", "5        N5       July 11th, 2025           114\n", "6        U5  September 12th, 2025           158\n", "7        Z5   December 12th, 2025           222\n", "8        H6      March 13th, 2026           283\n", "9        M6       June 12th, 2026           346\n", "10       U6  September 11th, 2026           408\n", "11       Z6   December 11th, 2026           472\n", "12       H7      March 12th, 2027           533\n", "13       M7       June 11th, 2027           596\n", "{'SFRH5 Comdty'}\n", "{'USOSFR1Z Curncy'}\n", "{'SFRG5P 95.8125 Comdty', 'SFRG5C 95.8125 Comdty'}\n", "['SFRG5C 95.8125 Comdty', 'SFRG5C 95.875 Comdty', 'SFRG5P 95.8125 Comdty', 'SFRG5P 95.875 Comdty']\n"]}], "source": ["scenarios = live_loader.load_option_price(dt.date(2025,1,27), [\"SFRM5C 96 Comdty\", \"SFRM5P 96 Comdty\"], [95.965], [-95], True, moneyness_type=\"standardized-normal\")"]}, {"cell_type": "code", "execution_count": 696, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>89</td>\n", "      <td>-7</td>\n", "      <td>K5</td>\n", "      <td>77</td>\n", "      <td>M5</td>\n", "      <td>96</td>\n", "      <td>128</td>\n", "      <td>109</td>\n", "      <td>137</td>\n", "      <td>95.965</td>\n", "      <td>95.9675</td>\n", "      <td>0.987204</td>\n", "      <td>95.9675</td>\n", "      <td>0.983969</td>\n", "      <td>96.000537</td>\n", "      <td>SFRK5C 96 Comdty</td>\n", "      <td>SFRK5C 96.0625 Comdty</td>\n", "      <td>96.003596</td>\n", "      <td>SFRM5C 96 Comdty</td>\n", "      <td>SFRM5C 96.0625 Comdty</td>\n", "      <td>0.583140</td>\n", "      <td>0.570603</td>\n", "      <td>0.574629</td>\n", "      <td>0.102491</td>\n", "      <td>96.00</td>\n", "      <td>96.0625</td>\n", "      <td>96.0</td>\n", "      <td>96.0625</td>\n", "      <td>0.11750</td>\n", "      <td>0.0950</td>\n", "      <td>0.6107421964531272</td>\n", "      <td>0.1300</td>\n", "      <td>0.1100</td>\n", "      <td>0.6018497643491537</td>\n", "      <td>0.604698</td>\n", "      <td>0.124925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>89</td>\n", "      <td>-7</td>\n", "      <td>K5</td>\n", "      <td>77</td>\n", "      <td>M5</td>\n", "      <td>96</td>\n", "      <td>128</td>\n", "      <td>109</td>\n", "      <td>137</td>\n", "      <td>95.965</td>\n", "      <td>95.9675</td>\n", "      <td>0.987204</td>\n", "      <td>95.9675</td>\n", "      <td>0.983969</td>\n", "      <td>96.000537</td>\n", "      <td>SFRK5P 96 Comdty</td>\n", "      <td>SFRK5P 96.0625 Comdty</td>\n", "      <td>96.003596</td>\n", "      <td>SFRM5P 96 Comdty</td>\n", "      <td>SFRM5P 96.0625 Comdty</td>\n", "      <td>0.583140</td>\n", "      <td>0.570603</td>\n", "      <td>0.574629</td>\n", "      <td>0.102491</td>\n", "      <td>96.00</td>\n", "      <td>96.0625</td>\n", "      <td>96.0</td>\n", "      <td>96.0625</td>\n", "      <td>0.15000</td>\n", "      <td>0.1925</td>\n", "      <td>0.6127970152483408</td>\n", "      <td>0.1625</td>\n", "      <td>0.2050</td>\n", "      <td>0.6042567199263185</td>\n", "      <td>0.606992</td>\n", "      <td>0.160012</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>46</td>\n", "      <td>-50</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>J5</td>\n", "      <td>53</td>\n", "      <td>66</td>\n", "      <td>46</td>\n", "      <td>74</td>\n", "      <td>95.965</td>\n", "      <td>95.7875</td>\n", "      <td>0.994522</td>\n", "      <td>95.9675</td>\n", "      <td>0.991218</td>\n", "      <td>95.811080</td>\n", "      <td>SFRH5C 95.75 Comdty</td>\n", "      <td>SFRH5C 95.8125 Comdty</td>\n", "      <td>96.007315</td>\n", "      <td>SFRJ5C 96 Comdty</td>\n", "      <td>SFRJ5C 96.0625 Comdty</td>\n", "      <td>0.447391</td>\n", "      <td>0.596084</td>\n", "      <td>0.562457</td>\n", "      <td>0.145646</td>\n", "      <td>95.75</td>\n", "      <td>95.8125</td>\n", "      <td>96.0</td>\n", "      <td>96.0625</td>\n", "      <td>0.07500</td>\n", "      <td>0.0550</td>\n", "      <td>0.4625731962659509</td>\n", "      <td>0.0975</td>\n", "      <td>0.0800</td>\n", "      <td>0.6278685997076155</td>\n", "      <td>0.590730</td>\n", "      <td>0.083694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>46</td>\n", "      <td>-50</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>J5</td>\n", "      <td>53</td>\n", "      <td>66</td>\n", "      <td>46</td>\n", "      <td>74</td>\n", "      <td>95.965</td>\n", "      <td>95.7875</td>\n", "      <td>0.994522</td>\n", "      <td>95.9675</td>\n", "      <td>0.991218</td>\n", "      <td>95.811080</td>\n", "      <td>SFRH5P 95.75 Comdty</td>\n", "      <td>SFRH5P 95.8125 Comdty</td>\n", "      <td>96.007315</td>\n", "      <td>SFRJ5P 96 Comdty</td>\n", "      <td>SFRJ5P 96.0625 Comdty</td>\n", "      <td>0.447391</td>\n", "      <td>0.596084</td>\n", "      <td>0.562457</td>\n", "      <td>0.145646</td>\n", "      <td>95.75</td>\n", "      <td>95.8125</td>\n", "      <td>96.0</td>\n", "      <td>96.0625</td>\n", "      <td>0.03625</td>\n", "      <td>0.0750</td>\n", "      <td>0.4288347543524049</td>\n", "      <td>0.1325</td>\n", "      <td>0.1725</td>\n", "      <td>0.6403878945962956</td>\n", "      <td>0.594392</td>\n", "      <td>0.119117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  bd_bump imm_code_1  tte_bus_days_1  \\\n", "0        C    96.0       M5            89       -7         K5              77   \n", "1        P    96.0       M5            89       -7         K5              77   \n", "2        C    96.0       M5            46      -50         H5              33   \n", "3        P    96.0       M5            46      -50         H5              33   \n", "\n", "  imm_code_2  tte_bus_days_2  tte tte_1 tte_2  fut_px_bump   atmf_1  \\\n", "0         M5              96  128   109   137       95.965  95.9675   \n", "1         M5              96  128   109   137       95.965  95.9675   \n", "2         J5              53   66    46    74       95.965  95.7875   \n", "3         J5              53   66    46    74       95.965  95.7875   \n", "\n", "  discount_factor_1   atmf_2 discount_factor_2   strike_1  \\\n", "0          0.987204  95.9675          0.983969  96.000537   \n", "1          0.987204  95.9675          0.983969  96.000537   \n", "2          0.994522  95.9675          0.991218  95.811080   \n", "3          0.994522  95.9675          0.991218  95.811080   \n", "\n", "            ticker_1_0             ticker_1_1   strike_2        ticker_2_0  \\\n", "0     SFRK5C 96 Comdty  SFRK5C 96.0625 Comdty  96.003596  SFRM5C 96 Comdty   \n", "1     SFRK5P 96 Comdty  SFRK5P 96.0625 Comdty  96.003596  SFRM5P 96 Comdty   \n", "2  SFRH5C 95.75 Comdty  SFRH5C 95.8125 Comdty  96.007315  SFRJ5C 96 Comdty   \n", "3  SFRH5P 95.75 Comdty  SFRH5P 95.8125 Comdty  96.007315  SFRJ5P 96 Comdty   \n", "\n", "              ticker_2_1  atm_vol_1  atm_vol_2   atm_vol  moneyness  \\\n", "0  SFRM5C 96.0625 Comdty   0.583140   0.570603  0.574629   0.102491   \n", "1  SFRM5P 96.0625 Comdty   0.583140   0.570603  0.574629   0.102491   \n", "2  SFRJ5C 96.0625 Comdty   0.447391   0.596084  0.562457   0.145646   \n", "3  SFRJ5P 96.0625 Comdty   0.447391   0.596084  0.562457   0.145646   \n", "\n", "   strike_1_0  strike_1_1  strike_2_0  strike_2_1  skew_px_1_0  skew_px_1_1  \\\n", "0       96.00     96.0625        96.0     96.0625      0.11750       0.0950   \n", "1       96.00     96.0625        96.0     96.0625      0.15000       0.1925   \n", "2       95.75     95.8125        96.0     96.0625      0.07500       0.0550   \n", "3       95.75     95.8125        96.0     96.0625      0.03625       0.0750   \n", "\n", "           skew_vol_1  skew_px_2_0  skew_px_2_1          skew_vol_2       vol  \\\n", "0  0.6107421964531272       0.1300       0.1100  0.6018497643491537  0.604698   \n", "1  0.6127970152483408       0.1625       0.2050  0.6042567199263185  0.606992   \n", "2  0.4625731962659509       0.0975       0.0800  0.6278685997076155  0.590730   \n", "3  0.4288347543524049       0.1325       0.1725  0.6403878945962956  0.594392   \n", "\n", "   option_px  \n", "0   0.124925  \n", "1   0.160012  \n", "2   0.083694  \n", "3   0.119117  "]}, "execution_count": 696, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[0]"]}, {"cell_type": "code", "execution_count": 700, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>26</td>\n", "      <td>-70</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>37</td>\n", "      <td>18</td>\n", "      <td>46</td>\n", "      <td>95.965</td>\n", "      <td>95.7875</td>\n", "      <td>0.997847</td>\n", "      <td>95.7875</td>\n", "      <td>0.994516</td>\n", "      <td>95.81326</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5C 95.875 Comdty</td>\n", "      <td>95.826902</td>\n", "      <td>SFRH5C 95.8125 Comdty</td>\n", "      <td>SFRH5C 95.875 Comdty</td>\n", "      <td>0.440235</td>\n", "      <td>0.438587</td>\n", "      <td>0.438915</td>\n", "      <td>0.248257</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.030</td>\n", "      <td>0.01375</td>\n", "      <td>0.44034488091144286</td>\n", "      <td>0.0525</td>\n", "      <td>0.0325</td>\n", "      <td>0.45185555011728756</td>\n", "      <td>0.449595</td>\n", "      <td>0.041707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>26</td>\n", "      <td>-70</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>37</td>\n", "      <td>18</td>\n", "      <td>46</td>\n", "      <td>95.965</td>\n", "      <td>95.7875</td>\n", "      <td>0.997847</td>\n", "      <td>95.7875</td>\n", "      <td>0.994516</td>\n", "      <td>95.81326</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.875 Comdty</td>\n", "      <td>95.826902</td>\n", "      <td>SFRH5P 95.8125 Comdty</td>\n", "      <td>SFRH5P 95.875 Comdty</td>\n", "      <td>0.440235</td>\n", "      <td>0.438587</td>\n", "      <td>0.438915</td>\n", "      <td>0.248257</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.055</td>\n", "      <td>0.10000</td>\n", "      <td>0.44073907442751903</td>\n", "      <td>0.0750</td>\n", "      <td>0.1200</td>\n", "      <td>0.4399109538495151</td>\n", "      <td>0.440075</td>\n", "      <td>0.075450</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  bd_bump imm_code_1  tte_bus_days_1  \\\n", "0        C    96.0       M5            26      -70         G5              14   \n", "1        P    96.0       M5            26      -70         G5              14   \n", "\n", "  imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump   atmf_1  \\\n", "0         H5              33  37    18    46       95.965  95.7875   \n", "1         H5              33  37    18    46       95.965  95.7875   \n", "\n", "  discount_factor_1   atmf_2 discount_factor_2  strike_1  \\\n", "0          0.997847  95.7875          0.994516  95.81326   \n", "1          0.997847  95.7875          0.994516  95.81326   \n", "\n", "              ticker_1_0            ticker_1_1   strike_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5C 95.875 Comdty  95.826902   \n", "1  SFRG5P 95.8125 Comdty  SFRG5P 95.875 Comdty  95.826902   \n", "\n", "              ticker_2_0            ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  SFRH5C 95.8125 Comdty  SFRH5C 95.875 Comdty   0.440235   0.438587   \n", "1  SFRH5P 95.8125 Comdty  SFRH5P 95.875 Comdty   0.440235   0.438587   \n", "\n", "    atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0  0.438915   0.248257     95.8125      95.875     95.8125      95.875   \n", "1  0.438915   0.248257     95.8125      95.875     95.8125      95.875   \n", "\n", "   skew_px_1_0  skew_px_1_1           skew_vol_1  skew_px_2_0  skew_px_2_1  \\\n", "0        0.030      0.01375  0.44034488091144286       0.0525       0.0325   \n", "1        0.055      0.10000  0.44073907442751903       0.0750       0.1200   \n", "\n", "            skew_vol_2       vol  option_px  \n", "0  0.45185555011728756  0.449595   0.041707  \n", "1   0.4399109538495151  0.440075   0.075450  "]}, "execution_count": 700, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[0]"]}, {"cell_type": "code", "execution_count": 710, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>14</td>\n", "      <td>95.965</td>\n", "      <td>-82</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>21</td>\n", "      <td>18</td>\n", "      <td>46</td>\n", "      <td>95.7875</td>\n", "      <td>0.997847</td>\n", "      <td>95.7875</td>\n", "      <td>0.994517</td>\n", "      <td>95.8225</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5C 95.875 Comdty</td>\n", "      <td>95.841882</td>\n", "      <td>SFRH5C 95.8125 Comdty</td>\n", "      <td>SFRH5C 95.875 Comdty</td>\n", "      <td>0.433371</td>\n", "      <td>0.438587</td>\n", "      <td>0.433371</td>\n", "      <td>0.342645</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.02875</td>\n", "      <td>0.01375</td>\n", "      <td>0.433735948426706</td>\n", "      <td>0.0525</td>\n", "      <td>0.0325</td>\n", "      <td>0.4569902763684327</td>\n", "      <td>0.433736</td>\n", "      <td>0.025597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>14</td>\n", "      <td>95.965</td>\n", "      <td>-82</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>H5</td>\n", "      <td>33</td>\n", "      <td>21</td>\n", "      <td>18</td>\n", "      <td>46</td>\n", "      <td>95.7875</td>\n", "      <td>0.997847</td>\n", "      <td>95.7875</td>\n", "      <td>0.994517</td>\n", "      <td>95.8225</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.875 Comdty</td>\n", "      <td>95.841882</td>\n", "      <td>SFRH5P 95.8125 Comdty</td>\n", "      <td>SFRH5P 95.875 Comdty</td>\n", "      <td>0.433371</td>\n", "      <td>0.438587</td>\n", "      <td>0.433371</td>\n", "      <td>0.342645</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.05500</td>\n", "      <td>0.10250</td>\n", "      <td>0.4490569517923823</td>\n", "      <td>0.0750</td>\n", "      <td>0.1200</td>\n", "      <td>0.44995337566337984</td>\n", "      <td>0.449057</td>\n", "      <td>0.061880</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump imm_code_1  \\\n", "0        C    96.0       M5            14       95.965      -82         G5   \n", "1        P    96.0       M5            14       95.965      -82         G5   \n", "\n", "   tte_bus_days_1 imm_code_2  tte_bus_days_2 tte tte_1 tte_2   atmf_1  \\\n", "0              14         H5              33  21    18    46  95.7875   \n", "1              14         H5              33  21    18    46  95.7875   \n", "\n", "  discount_factor_1   atmf_2 discount_factor_2  strike_1  \\\n", "0          0.997847  95.7875          0.994517   95.8225   \n", "1          0.997847  95.7875          0.994517   95.8225   \n", "\n", "              ticker_1_0            ticker_1_1   strike_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5C 95.875 Comdty  95.841882   \n", "1  SFRG5P 95.8125 Comdty  SFRG5P 95.875 Comdty  95.841882   \n", "\n", "              ticker_2_0            ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  SFRH5C 95.8125 Comdty  SFRH5C 95.875 Comdty   0.433371   0.438587   \n", "1  SFRH5P 95.8125 Comdty  SFRH5P 95.875 Comdty   0.433371   0.438587   \n", "\n", "    atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0  0.433371   0.342645     95.8125      95.875     95.8125      95.875   \n", "1  0.433371   0.342645     95.8125      95.875     95.8125      95.875   \n", "\n", "   skew_px_1_0  skew_px_1_1          skew_vol_1  skew_px_2_0  skew_px_2_1  \\\n", "0      0.02875      0.01375   0.433735948426706       0.0525       0.0325   \n", "1      0.05500      0.10250  0.4490569517923823       0.0750       0.1200   \n", "\n", "            skew_vol_2       vol  option_px  \n", "0   0.4569902763684327  0.433736   0.025597  \n", "1  0.44995337566337984  0.449057   0.061880  "]}, "execution_count": 710, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"scenarios\"]"]}, {"cell_type": "code", "execution_count": 722, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>call_px_at_0</th>\n", "      <th>call_px_2</th>\n", "      <th>call_px</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>3</td>\n", "      <td>95.965</td>\n", "      <td>-93</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>18</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.8125</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>95.82404</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5C 95.875 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.433372</td>\n", "      <td>0.433372</td>\n", "      <td>0.008712</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.02875</td>\n", "      <td>0.01375</td>\n", "      <td>0.43484213413522566</td>\n", "      <td>0.035</td>\n", "      <td>0.061730</td>\n", "      <td>0.046170</td>\n", "      <td>0.570806</td>\n", "      <td>0.011145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>3</td>\n", "      <td>95.965</td>\n", "      <td>-93</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>18</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.8125</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>95.82404</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.875 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.433372</td>\n", "      <td>0.433372</td>\n", "      <td>0.008712</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.05500</td>\n", "      <td>0.10250</td>\n", "      <td>0.45031173284727943</td>\n", "      <td>0.035</td>\n", "      <td>0.063098</td>\n", "      <td>0.046776</td>\n", "      <td>0.587055</td>\n", "      <td>0.046675</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump imm_code_1  \\\n", "0        C    96.0       M5             3       95.965      -93         G5   \n", "1        P    96.0       M5             3       95.965      -93         G5   \n", "\n", "   tte_bus_days_1 imm_code_2  tte_bus_days_2 tte tte_1 tte_2   atmf_1  \\\n", "0               0         G5              14   3     0    18  95.7875   \n", "1               0         G5              14   3     0    18  95.7875   \n", "\n", "  discount_factor_1   atmf_2 discount_factor_2  strike_1  \\\n", "0          0.997845  95.7875          0.997845   95.8125   \n", "1          0.997845  95.7875          0.997845   95.8125   \n", "\n", "              ticker_1_0             ticker_1_1  strike_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5P 95.8125 Comdty  95.82404   \n", "1  SFRG5C 95.8125 Comdty  SFRG5P 95.8125 Comdty  95.82404   \n", "\n", "              ticker_2_0            ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5C 95.875 Comdty        0.0   0.433372   \n", "1  SFRG5P 95.8125 Comdty  SFRG5P 95.875 Comdty        0.0   0.433372   \n", "\n", "    atm_vol  moneyness  strike_2_0  strike_2_1  skew_px_2_0  skew_px_2_1  \\\n", "0  0.433372   0.008712     95.8125      95.875      0.02875      0.01375   \n", "1  0.433372   0.008712     95.8125      95.875      0.05500      0.10250   \n", "\n", "            skew_vol_2  call_px_at_0  call_px_2   call_px       vol  option_px  \n", "0  0.43484213413522566         0.035   0.061730  0.046170  0.570806   0.011145  \n", "1  0.45031173284727943         0.035   0.063098  0.046776  0.587055   0.046675  "]}, "execution_count": 722, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"scenarios_short_expiry\"]"]}, {"cell_type": "code", "execution_count": 725, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>call_px_at_0</th>\n", "      <th>call_px_2</th>\n", "      <th>call_px</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>1</td>\n", "      <td>95.965</td>\n", "      <td>-95</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>18</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.8125</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>95.82404</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5C 95.875 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.433372</td>\n", "      <td>0.433372</td>\n", "      <td>0.008712</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.02875</td>\n", "      <td>0.01375</td>\n", "      <td>0.43484213413522566</td>\n", "      <td>0.035</td>\n", "      <td>0.061730</td>\n", "      <td>0.041449</td>\n", "      <td>0.757970</td>\n", "      <td>0.006435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>1</td>\n", "      <td>95.965</td>\n", "      <td>-95</td>\n", "      <td>G5</td>\n", "      <td>0</td>\n", "      <td>G5</td>\n", "      <td>14</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>18</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.7875</td>\n", "      <td>0.997845</td>\n", "      <td>95.8125</td>\n", "      <td>SFRG5C 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>95.82404</td>\n", "      <td>SFRG5P 95.8125 Comdty</td>\n", "      <td>SFRG5P 95.875 Comdty</td>\n", "      <td>0.0</td>\n", "      <td>0.433372</td>\n", "      <td>0.433372</td>\n", "      <td>0.008712</td>\n", "      <td>95.8125</td>\n", "      <td>95.875</td>\n", "      <td>0.05500</td>\n", "      <td>0.10250</td>\n", "      <td>0.45031173284727943</td>\n", "      <td>0.035</td>\n", "      <td>0.063098</td>\n", "      <td>0.041799</td>\n", "      <td>0.776087</td>\n", "      <td>0.041709</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days  fut_px_bump  bd_bump imm_code_1  \\\n", "0        C    96.0       M5             1       95.965      -95         G5   \n", "1        P    96.0       M5             1       95.965      -95         G5   \n", "\n", "   tte_bus_days_1 imm_code_2  tte_bus_days_2 tte tte_1 tte_2   atmf_1  \\\n", "0               0         G5              14   1     0    18  95.7875   \n", "1               0         G5              14   1     0    18  95.7875   \n", "\n", "  discount_factor_1   atmf_2 discount_factor_2  strike_1  \\\n", "0          0.997845  95.7875          0.997845   95.8125   \n", "1          0.997845  95.7875          0.997845   95.8125   \n", "\n", "              ticker_1_0             ticker_1_1  strike_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5P 95.8125 Comdty  95.82404   \n", "1  SFRG5C 95.8125 Comdty  SFRG5P 95.8125 Comdty  95.82404   \n", "\n", "              ticker_2_0            ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  SFRG5C 95.8125 Comdty  SFRG5C 95.875 Comdty        0.0   0.433372   \n", "1  SFRG5P 95.8125 Comdty  SFRG5P 95.875 Comdty        0.0   0.433372   \n", "\n", "    atm_vol  moneyness  strike_2_0  strike_2_1  skew_px_2_0  skew_px_2_1  \\\n", "0  0.433372   0.008712     95.8125      95.875      0.02875      0.01375   \n", "1  0.433372   0.008712     95.8125      95.875      0.05500      0.10250   \n", "\n", "            skew_vol_2  call_px_at_0  call_px_2   call_px       vol  option_px  \n", "0  0.43484213413522566         0.035   0.061730  0.041449  0.757970   0.006435  \n", "1  0.45031173284727943         0.035   0.063098  0.041799  0.776087   0.041709  "]}, "execution_count": 725, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"scenarios_short_expiry\"]"]}, {"cell_type": "code", "execution_count": 716, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.025000000000005684"]}, "execution_count": 716, "metadata": {}, "output_type": "execute_result"}], "source": ["95.8125  - 95.7875"]}, {"cell_type": "code", "execution_count": 717, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.08750000000000568"]}, "execution_count": 717, "metadata": {}, "output_type": "execute_result"}], "source": ["95.875 - 95.7875"]}, {"cell_type": "code", "execution_count": 698, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>89</td>\n", "      <td>0.604698</td>\n", "      <td>0.124925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>89</td>\n", "      <td>0.606992</td>\n", "      <td>0.160012</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>46</td>\n", "      <td>0.590730</td>\n", "      <td>0.083694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>46</td>\n", "      <td>0.594392</td>\n", "      <td>0.119117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  fut_px_bump  tte_bus_days       vol  option_px\n", "0        C    96.0       M5       95.965            89  0.604698   0.124925\n", "1        P    96.0       M5       95.965            89  0.606992   0.160012\n", "2        C    96.0       M5       95.965            46  0.590730   0.083694\n", "3        P    96.0       M5       95.965            46  0.594392   0.119117"]}, "execution_count": 698, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[2]"]}, {"cell_type": "code", "execution_count": 701, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>26</td>\n", "      <td>0.449595</td>\n", "      <td>0.041707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>26</td>\n", "      <td>0.440075</td>\n", "      <td>0.075450</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  fut_px_bump  tte_bus_days       vol  option_px\n", "0        C    96.0       M5       95.965            26  0.449595   0.041707\n", "1        P    96.0       M5       95.965            26  0.440075   0.075450"]}, "execution_count": 701, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[2]"]}, {"cell_type": "code", "execution_count": 711, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-82</td>\n", "      <td>14</td>\n", "      <td>0.433736</td>\n", "      <td>0.025597</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-82</td>\n", "      <td>14</td>\n", "      <td>0.449057</td>\n", "      <td>0.061880</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-90</td>\n", "      <td>6</td>\n", "      <td>0.489228</td>\n", "      <td>0.015762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-90</td>\n", "      <td>6</td>\n", "      <td>0.504694</td>\n", "      <td>0.051543</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  fut_px_bump  bd_bump  tte_bus_days       vol  \\\n", "0        C    96.0       M5       95.965      -82            14  0.433736   \n", "1        P    96.0       M5       95.965      -82            14  0.449057   \n", "2        C    96.0       M5       95.965      -90             6  0.489228   \n", "3        P    96.0       M5       95.965      -90             6  0.504694   \n", "\n", "   option_px  \n", "0   0.025597  \n", "1   0.061880  \n", "2   0.015762  \n", "3   0.051543  "]}, "execution_count": 711, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"output\"]"]}, {"cell_type": "code", "execution_count": 723, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-93</td>\n", "      <td>3</td>\n", "      <td>0.570806</td>\n", "      <td>0.011145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-93</td>\n", "      <td>3</td>\n", "      <td>0.587055</td>\n", "      <td>0.046675</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  fut_px_bump  bd_bump  tte_bus_days       vol  \\\n", "0        C    96.0       M5       95.965      -93             3  0.570806   \n", "1        P    96.0       M5       95.965      -93             3  0.587055   \n", "\n", "   option_px  \n", "0   0.011145  \n", "1   0.046675  "]}, "execution_count": 723, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"output\"]"]}, {"cell_type": "code", "execution_count": 726, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>fut_px_bump</th>\n", "      <th>bd_bump</th>\n", "      <th>tte_bus_days</th>\n", "      <th>vol</th>\n", "      <th>option_px</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-95</td>\n", "      <td>1</td>\n", "      <td>0.757970</td>\n", "      <td>0.006435</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>P</td>\n", "      <td>96.0</td>\n", "      <td>M5</td>\n", "      <td>95.965</td>\n", "      <td>-95</td>\n", "      <td>1</td>\n", "      <td>0.776087</td>\n", "      <td>0.041709</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  fut_px_bump  bd_bump  tte_bus_days       vol  \\\n", "0        C    96.0       M5       95.965      -95             1  0.757970   \n", "1        P    96.0       M5       95.965      -95             1  0.776087   \n", "\n", "   option_px  \n", "0   0.006435  \n", "1   0.041709  "]}, "execution_count": 726, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[\"output\"]"]}, {"cell_type": "code", "execution_count": 712, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.03499999999999659"]}, "execution_count": 712, "metadata": {}, "output_type": "execute_result"}], "source": ["96-95.965"]}, {"cell_type": "code", "execution_count": 487, "metadata": {}, "outputs": [], "source": ["test = scenarios[1]"]}, {"cell_type": "code", "execution_count": 506, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([96.25, 96.25])"]}, "execution_count": 506, "metadata": {}, "output_type": "execute_result"}], "source": ["test[[\"strike_2_0\", \"strike_2_1\"]].to_numpy()[:,0]"]}, {"cell_type": "code", "execution_count": 399, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.33103000330474897, 0.35151772837682715, 0], dtype=object)"]}, "execution_count": 399, "metadata": {}, "output_type": "execute_result"}], "source": ["np.append(scenarios.iloc[4][[\"atm_vol_1\",\"atm_vol_2\"]].values,0)"]}, {"cell_type": "code", "execution_count": 409, "metadata": {}, "outputs": [], "source": ["from scipy.interpolate import interp1d\n", "var = np.append(scenarios.iloc[4][[\"atm_vol_1\",\"atm_vol_2\"]].values, 0) **2 * np.append(scenarios.iloc[4][[\"tte_bus_days_1\", \"tte_bus_days_2\"]].values,0)\n", "f = interp1d(np.append(scenarios.iloc[4][[\"tte_bus_days_1\", \"tte_bus_days_2\"]].values,0), var, kind=\"linear\", fill_value=\"extrapolate\"\n", ")  # Review this line"]}, {"cell_type": "code", "execution_count": 410, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.33103000330474897"]}, "execution_count": 410, "metadata": {}, "output_type": "execute_result"}], "source": ["np.sqrt(f(3) / 3)"]}, {"cell_type": "code", "execution_count": 383, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>call_put</th>\n", "      <th>strike</th>\n", "      <th>imm_code</th>\n", "      <th>tte_bus_days</th>\n", "      <th>imm_code_1</th>\n", "      <th>tte_bus_days_1</th>\n", "      <th>imm_code_2</th>\n", "      <th>tte_bus_days_2</th>\n", "      <th>tte</th>\n", "      <th>tte_1</th>\n", "      <th>tte_2</th>\n", "      <th>fut_px_bump</th>\n", "      <th>atmf_1</th>\n", "      <th>discount_factor_1</th>\n", "      <th>atmf_2</th>\n", "      <th>discount_factor_2</th>\n", "      <th>strike_1</th>\n", "      <th>ticker_1_0</th>\n", "      <th>ticker_1_1</th>\n", "      <th>strike_2</th>\n", "      <th>ticker_2_0</th>\n", "      <th>ticker_2_1</th>\n", "      <th>atm_vol_1</th>\n", "      <th>atm_vol_2</th>\n", "      <th>atm_vol</th>\n", "      <th>moneyness</th>\n", "      <th>strike_1_0</th>\n", "      <th>strike_1_1</th>\n", "      <th>strike_2_0</th>\n", "      <th>strike_2_1</th>\n", "      <th>skew_px_1_0</th>\n", "      <th>skew_vol_1_0</th>\n", "      <th>skew_px_1_1</th>\n", "      <th>skew_vol_1_1</th>\n", "      <th>skew_vol_1</th>\n", "      <th>skew_px_2_0</th>\n", "      <th>skew_vol_2_0</th>\n", "      <th>skew_px_2_1</th>\n", "      <th>skew_vol_2_1</th>\n", "      <th>skew_vol_2</th>\n", "      <th>skew_vol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>28</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>39</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.230347</td>\n", "      <td>SFRG5C 96.1875 Comdty</td>\n", "      <td>SFRG5C 96.25 Comdty</td>\n", "      <td>96.458818</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.344346</td>\n", "      <td>5.009496</td>\n", "      <td>96.1875</td>\n", "      <td>96.250</td>\n", "      <td>96.4375</td>\n", "      <td>96.5000</td>\n", "      <td>0.0050</td>\n", "      <td>0.905351</td>\n", "      <td>0.0050</td>\n", "      <td>1.013956</td>\n", "      <td>0.9798045425856905</td>\n", "      <td>0.005</td>\n", "      <td>0.940520</td>\n", "      <td>0.005</td>\n", "      <td>1.012940</td>\n", "      <td>0.965222404646963</td>\n", "      <td>0.970455</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>C</td>\n", "      <td>96</td>\n", "      <td>J5</td>\n", "      <td>48</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>67</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.196046</td>\n", "      <td>SFRH5C 96.1875 Comdty</td>\n", "      <td>SFRH5C 96.25 Comdty</td>\n", "      <td>96.790947</td>\n", "      <td>SFRJ5C 96.75 Comdty</td>\n", "      <td>SFRJ5C 96.8125 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.519993</td>\n", "      <td>3.084470</td>\n", "      <td>96.1875</td>\n", "      <td>96.250</td>\n", "      <td>96.7500</td>\n", "      <td>96.8125</td>\n", "      <td>0.0075</td>\n", "      <td>0.697884</td>\n", "      <td>0.0075</td>\n", "      <td>0.779243</td>\n", "      <td>0.7090084492240317</td>\n", "      <td>0.015</td>\n", "      <td>1.162484</td>\n", "      <td>0.015</td>\n", "      <td>1.228376</td>\n", "      <td>1.2056529518338492</td>\n", "      <td>1.037882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>C</td>\n", "      <td>95.875</td>\n", "      <td>H5</td>\n", "      <td>18</td>\n", "      <td>G5</td>\n", "      <td>19</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>24</td>\n", "      <td>28</td>\n", "      <td>56</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.996664</td>\n", "      <td>95.775</td>\n", "      <td>0.993351</td>\n", "      <td>96.369990</td>\n", "      <td>SFRG5C 96.3125 Comdty</td>\n", "      <td>SFRG5C 96.375 Comdty</td>\n", "      <td>96.668529</td>\n", "      <td>SFRH5C 96.625 Comdty</td>\n", "      <td>SFRH5C 96.6875 Comdty</td>\n", "      <td>0.331033</td>\n", "      <td>0.351524</td>\n", "      <td>0.328678</td>\n", "      <td>6.545786</td>\n", "      <td>96.3125</td>\n", "      <td>96.375</td>\n", "      <td>96.6250</td>\n", "      <td>96.6875</td>\n", "      <td>0.0050</td>\n", "      <td>1.120637</td>\n", "      <td>0.0050</td>\n", "      <td>1.225681</td>\n", "      <td>1.2172611536729885</td>\n", "      <td>0.005</td>\n", "      <td>1.155496</td>\n", "      <td>0.005</td>\n", "      <td>1.225777</td>\n", "      <td>1.2044439119367927</td>\n", "      <td>1.218677</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>C</td>\n", "      <td>96</td>\n", "      <td>J5</td>\n", "      <td>38</td>\n", "      <td>H5</td>\n", "      <td>38</td>\n", "      <td>J5</td>\n", "      <td>58</td>\n", "      <td>52</td>\n", "      <td>56</td>\n", "      <td>84</td>\n", "      <td>95.3</td>\n", "      <td>95.775</td>\n", "      <td>0.993346</td>\n", "      <td>95.895</td>\n", "      <td>0.990053</td>\n", "      <td>96.475000</td>\n", "      <td>SFRH5C 96.4375 Comdty</td>\n", "      <td>SFRH5C 96.5 Comdty</td>\n", "      <td>97.384536</td>\n", "      <td>SFRJ5C 97.375 Comdty</td>\n", "      <td>SFRJ5C 97.4375 Comdty</td>\n", "      <td>0.351526</td>\n", "      <td>0.605463</td>\n", "      <td>0.351526</td>\n", "      <td>5.128015</td>\n", "      <td>96.4375</td>\n", "      <td>96.500</td>\n", "      <td>97.3750</td>\n", "      <td>97.4375</td>\n", "      <td>0.0050</td>\n", "      <td>0.940521</td>\n", "      <td>0.0050</td>\n", "      <td>1.012941</td>\n", "      <td>0.983972935499156</td>\n", "      <td>0.005</td>\n", "      <td>1.491554</td>\n", "      <td>0.005</td>\n", "      <td>1.544955</td>\n", "      <td>1.4997014900221275</td>\n", "      <td>0.983973</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  call_put  strike imm_code  tte_bus_days imm_code_1  tte_bus_days_1  \\\n", "0        C  95.875       H5            28         G5              19   \n", "1        C      96       J5            48         H5              38   \n", "2        C  95.875       H5            18         G5              19   \n", "3        C      96       J5            38         H5              38   \n", "\n", "  imm_code_2  tte_bus_days_2 tte tte_1 tte_2  fut_px_bump  atmf_1  \\\n", "0         H5              38  39    28    56         95.3  95.775   \n", "1         J5              58  67    56    84         95.3  95.775   \n", "2         H5              38  24    28    56         95.3  95.775   \n", "3         J5              58  52    56    84         95.3  95.775   \n", "\n", "  discount_factor_1  atmf_2 discount_factor_2   strike_1  \\\n", "0          0.996664  95.775          0.993351  96.230347   \n", "1          0.993346  95.895          0.990053  96.196046   \n", "2          0.996664  95.775          0.993351  96.369990   \n", "3          0.993346  95.895          0.990053  96.475000   \n", "\n", "              ticker_1_0            ticker_1_1   strike_2  \\\n", "0  SFRG5C 96.1875 Comdty   SFRG5C 96.25 Comdty  96.458818   \n", "1  SFRH5C 96.1875 Comdty   SFRH5C 96.25 Comdty  96.790947   \n", "2  SFRG5C 96.3125 Comdty  SFRG5C 96.375 Comdty  96.668529   \n", "3  SFRH5C 96.4375 Comdty    SFRH5C 96.5 Comdty  97.384536   \n", "\n", "              ticker_2_0             ticker_2_1  atm_vol_1  atm_vol_2  \\\n", "0  SFRH5C 96.4375 Comdty     SFRH5C 96.5 Comdty   0.331033   0.351524   \n", "1    SFRJ5C 96.75 Comdty  SFRJ5C 96.8125 Comdty   0.351526   0.605463   \n", "2   SFRH5C 96.625 Comdty  SFRH5C 96.6875 Comdty   0.331033   0.351524   \n", "3   SFRJ5C 97.375 Comdty  SFRJ5C 97.4375 Comdty   0.351526   0.605463   \n", "\n", "    atm_vol  moneyness  strike_1_0  strike_1_1  strike_2_0  strike_2_1  \\\n", "0  0.344346   5.009496     96.1875      96.250     96.4375     96.5000   \n", "1  0.519993   3.084470     96.1875      96.250     96.7500     96.8125   \n", "2  0.328678   6.545786     96.3125      96.375     96.6250     96.6875   \n", "3  0.351526   5.128015     96.4375      96.500     97.3750     97.4375   \n", "\n", "   skew_px_1_0  skew_vol_1_0  skew_px_1_1  skew_vol_1_1          skew_vol_1  \\\n", "0       0.0050      0.905351       0.0050      1.013956  0.9798045425856905   \n", "1       0.0075      0.697884       0.0075      0.779243  0.7090084492240317   \n", "2       0.0050      1.120637       0.0050      1.225681  1.2172611536729885   \n", "3       0.0050      0.940521       0.0050      1.012941   0.983972935499156   \n", "\n", "   skew_px_2_0  skew_vol_2_0  skew_px_2_1  skew_vol_2_1          skew_vol_2  \\\n", "0        0.005      0.940520        0.005      1.012940   0.965222404646963   \n", "1        0.015      1.162484        0.015      1.228376  1.2056529518338492   \n", "2        0.005      1.155496        0.005      1.225777  1.2044439119367927   \n", "3        0.005      1.491554        0.005      1.544955  1.4997014900221275   \n", "\n", "   skew_vol  \n", "0  0.970455  \n", "1  1.037882  \n", "2  1.218677  \n", "3  0.983973  "]}, "execution_count": 383, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios"]}, {"cell_type": "code", "execution_count": 385, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.7000000000000028 0.6999999999999886\n"]}], "source": ["print(96-95.3, 96.475000-95.775\t)"]}, {"cell_type": "code", "execution_count": 381, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.45534699999998907 0.683817999999988\n", "0.5750000000000028 0.4124999999999943 0.4749999999999943 0.6624999999999943 0.7249999999999943\n"]}], "source": ["print(96.230347 -95.775, 96.458818-95.775\t)\n", "print(95.875 - 95.3, 96.1875-95.775, 96.25-95.775,  96.4375 - 95.775, 96.5 -95.775)"]}, {"cell_type": "code", "execution_count": 368, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.009496262480205"]}, "execution_count": 368, "metadata": {}, "output_type": "execute_result"}], "source": ["(95.875-95.3)/(0.344346\t* np.sqrt(28/252))"]}, {"cell_type": "code", "execution_count": 369, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.538121644067259"]}, "execution_count": 369, "metadata": {}, "output_type": "execute_result"}], "source": ["(96.1875-95.775)/(0.331033\t* np.sqrt(19/252))"]}, {"cell_type": "code", "execution_count": 370, "metadata": {}, "outputs": [{"data": {"text/plain": ["5.225715832562308"]}, "execution_count": 370, "metadata": {}, "output_type": "execute_result"}], "source": ["(96.25 -95.775)/(0.331033\t* np.sqrt(19/252))"]}, {"cell_type": "code", "execution_count": 350, "metadata": {}, "outputs": [{"data": {"text/plain": ["28"]}, "execution_count": 350, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.Date(14,2,2025) - ql.Date(17,1,2025)"]}, {"cell_type": "code", "execution_count": 374, "metadata": {}, "outputs": [], "source": ["from scipy.interpolate import interp1d"]}, {"cell_type": "code", "execution_count": 375, "metadata": {}, "outputs": [], "source": ["f = interp1d([0, 1, 2], [3,4,5], kind='linear', fill_value=\"extrapolate\")"]}, {"cell_type": "code", "execution_count": 378, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(1.)"]}, "execution_count": 378, "metadata": {}, "output_type": "execute_result"}], "source": ["f(-2)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mpd\u001b[49m\u001b[38;5;241m.\u001b[39mread_parquet(\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mfchen\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mDocuments\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124minvestment\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mloader\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mir_future_option_data_live\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mSFR\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mSFR_202501_close.parquet\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'pd' is not defined"]}], "source": ["pd.read_parquet(r\"c:\\\\Users\\\\<USER>\\\\Documents\\\\investment\\\\loader\\\\ir_future_option_data_live\\\\SFR\\\\SFR_202501_close.parquet\")"]}, {"cell_type": "code", "execution_count": 295, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.232549970736167"]}, "execution_count": 295, "metadata": {}, "output_type": "execute_result"}], "source": ["(96-94)/(144.359806/100 * np.sqrt(27/252))"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"data": {"text/plain": ["53"]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.Date(14,3,2025) - ql.Date(20,1,2025)"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.995850622406639"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["1/(1+6/100*25/360)"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.9965397923875432"]}, "execution_count": 165, "metadata": {}, "output_type": "execute_result"}], "source": ["1/(1+5/100*25/360)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["a = pd.DataFrame([3,2,1], columns=[\"a\"])"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   a\n", "2  1\n", "1  2\n", "0  3"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["a_1    3\n", "Name: 0, dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["a.iloc[-1].rename(lambda x: f\"{x}_1\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    3\n", "a    1\n", "dtype: int64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.concat([a.iloc[-1], a.iloc[-3]])"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>ticker</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>SFRH6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SFRM6 Comdty</th>\n", "      <td>97.145</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               price\n", "ticker              \n", "SFRH6 Comdty  97.145\n", "SFRM6 Comdty  97.145"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["data.loc[dt.date(2024,9,17)].loc[[\"SFRH6 Comdty\", \"SFRM6 Comdty\"]]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from loader.ir_future_option_data_scenario_analyzer import interestRateFutureOptionScenarioAnalyzer"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["live_analyzer = interestRateFutureOptionScenarioAnalyzer(\"us\", debug_mode=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code option_expiry_date tte  tte_bus_days\n", "0     4CK5     May 23rd, 2025   1             1\n", "1     5CK5     May 30th, 2025   8             5\n", "2     1CM5     June 6th, 2025  15            10\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>imm_code</th>\n", "      <th>option_expiry_date</th>\n", "      <th>tte</th>\n", "      <th>tte_bus_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4CK5</td>\n", "      <td>May 23rd, 2025</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5CK5</td>\n", "      <td>May 30th, 2025</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1CM5</td>\n", "      <td>June 6th, 2025</td>\n", "      <td>15</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  imm_code option_expiry_date tte  tte_bus_days\n", "0     4CK5     May 23rd, 2025   1             1\n", "1     5CK5     May 30th, 2025   8             5\n", "2     1CM5     June 6th, 2025  15            10"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["live_analyzer.prepare_weeklies_candidates(ql.Date(22,5,2025), \"C\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  imm_code option_expiry_date tte  tte_bus_days\n", "0     4CK5     May 23rd, 2025   1             1\n", "1     5CK5     May 30th, 2025   8             5\n", "2     1CM5     June 6th, 2025  15            10\n", "  imm_code    option_expiry_date  tte  tte_bus_days\n", "0       M5        May 23rd, 2025    1             1\n", "1     5CK5        May 30th, 2025    8             5\n", "2     1CM5        June 6th, 2025   15            10\n", "3       N5       June 20th, 2025   29            19\n", "4       Q5       July 25th, 2025   64            43\n", "5       U5     August 22nd, 2025   92            63\n", "6       V5  September 26th, 2025  127            87\n", "7       Z5   November 21st, 2025  183           127\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>imm_code</th>\n", "      <th>option_expiry_date</th>\n", "      <th>tte</th>\n", "      <th>tte_bus_days</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>M5</td>\n", "      <td>May 23rd, 2025</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5CK5</td>\n", "      <td>May 30th, 2025</td>\n", "      <td>8</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1CM5</td>\n", "      <td>June 6th, 2025</td>\n", "      <td>15</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>N5</td>\n", "      <td>June 20th, 2025</td>\n", "      <td>29</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Q5</td>\n", "      <td>July 25th, 2025</td>\n", "      <td>64</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>U5</td>\n", "      <td>August 22nd, 2025</td>\n", "      <td>92</td>\n", "      <td>63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>V5</td>\n", "      <td>September 26th, 2025</td>\n", "      <td>127</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Z5</td>\n", "      <td>November 21st, 2025</td>\n", "      <td>183</td>\n", "      <td>127</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  imm_code    option_expiry_date  tte  tte_bus_days\n", "0       M5        May 23rd, 2025    1             1\n", "1     5CK5        May 30th, 2025    8             5\n", "2     1CM5        June 6th, 2025   15            10\n", "3       N5       June 20th, 2025   29            19\n", "4       Q5       July 25th, 2025   64            43\n", "5       U5     August 22nd, 2025   92            63\n", "6       V5  September 26th, 2025  127            87\n", "7       Z5   November 21st, 2025  183           127"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["live_analyzer.prepare_option_candidates(ql.Date(22,5,2025))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(28,3,2024)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.NYSE).endOfMonth(ql.Date(1,3,2024))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(27,3,2024)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.NYSE).advance(ql.Date(28,3,2024), -1, ql.Days)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(29,1,2021)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.NYSE).endOfMonth(ql.Date(15,1,2021))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(31,12,2015)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.NYSE).advance(ql.Date(1,1,2016), 0, ql.Days, ql.Preceding)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["'F6'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.IMM.nextCode(ql.Date.startOfMonth(ql.Date(1,1,2016)), False)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(21,5,2025)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.IMM.date(\"K5\", ql.Date(1,6,2025)-ql.Period(1, ql.Months))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["29"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.Date.endOf<PERSON><PERSON>h(ql.Date(28,5,2025)) - ql.Date.nthWeekday(1, ql.Friday, 5, 2025)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["(ql.Date.endOfMonth(ql.Date(28,3,2025)) - ql.Date.nthWeekday(1, ql.Friday, 3, 2025))//7+1"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["'USU5 Comdty'"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["interestRateFutureOptionLoader._check_weekly_underlying_ticker(\"5CK5\", \"USM5 Comdty\")"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["from addin.excel_addin import get_udl_future_ticker"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/plain": ["['USU5 Comdty']"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["get_udl_future_ticker(\"5CK5\", dt.date(2025,5,22))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}