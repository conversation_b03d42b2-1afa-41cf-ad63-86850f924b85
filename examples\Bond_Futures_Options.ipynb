{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import datetime as dt\n", "import numpy as np\n", "import pandas as pd\n", "from xbbg import blp\n", "import QuantLib as ql\n", "from loader.ir_future_option_data_loader import interestRateFutureOptionLoader\n", "from pricer.ir_future_option_pricer import interestRateFutureOptionPricer\n", "pd.options.plotting.backend = \"plotly\"\n", "pd.set_option('display.max_rows', 200)  # Default is 60\n", "pd.set_option('display.max_columns', 200)  # Default is 20"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["loader = interestRateFutureOptionLoader(\"TU\")"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(Date(25,9,2023), Date(27,10,2023)),\n", " (Date(30,10,2023), Date(24,11,2023)),\n", " (Date(27,11,2023), Date(22,12,2023)),\n", " (Date(25,12,2023), Date(26,1,2024)),\n", " (Date(29,1,2024), Date(23,2,2024)),\n", " (Date(26,2,2024), Date(22,3,2024)),\n", " (Date(25,3,2024), Date(26,4,2024)),\n", " (Date(29,4,2024), Date(24,5,2024)),\n", " (Date(27,5,2024), Date(21,6,2024)),\n", " (Date(24,6,2024), Date(26,7,2024)),\n", " (Date(29,7,2024), Date(23,8,2024)),\n", " (Date(26,8,2024), Date(20,9,2024)),\n", " (Date(23,9,2024), Date(25,10,2024)),\n", " (Date(28,10,2024), Date(22,11,2024)),\n", " (Date(25,11,2024), Date(27,12,2024)),\n", " (Date(30,12,2024), Date(30,12,2024))]"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["loader.get_periods(dt.date(2023,10,1), dt.date(2024,12,30))"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading option price from 2023-09-25 to 2023-10-27\n", "Load data time: 0.03200364112854004\n", "Process data time: 0.0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strike</th>\n", "      <th>100</th>\n", "      <th>100.125</th>\n", "      <th>100.25</th>\n", "      <th>100.375</th>\n", "      <th>100.5</th>\n", "      <th>100.625</th>\n", "      <th>100.75</th>\n", "      <th>100.875</th>\n", "      <th>101</th>\n", "      <th>101.125</th>\n", "      <th>101.25</th>\n", "      <th>101.375</th>\n", "      <th>101.5</th>\n", "      <th>101.625</th>\n", "      <th>101.75</th>\n", "      <th>101.875</th>\n", "      <th>102</th>\n", "      <th>102.125</th>\n", "      <th>102.25</th>\n", "      <th>102.375</th>\n", "      <th>102.5</th>\n", "      <th>102.625</th>\n", "      <th>102.75</th>\n", "      <th>102.875</th>\n", "      <th>103</th>\n", "      <th>103.125</th>\n", "      <th>103.25</th>\n", "      <th>103.375</th>\n", "      <th>103.5</th>\n", "      <th>103.625</th>\n", "      <th>103.75</th>\n", "      <th>103.875</th>\n", "      <th>104</th>\n", "      <th>104.125</th>\n", "      <th>104.25</th>\n", "      <th>104.375</th>\n", "      <th>104.5</th>\n", "      <th>104.625</th>\n", "      <th>104.75</th>\n", "      <th>104.875</th>\n", "      <th>105</th>\n", "      <th>98.5</th>\n", "      <th>98.625</th>\n", "      <th>98.75</th>\n", "      <th>98.875</th>\n", "      <th>99</th>\n", "      <th>99.125</th>\n", "      <th>99.25</th>\n", "      <th>99.375</th>\n", "      <th>99.5</th>\n", "      <th>99.625</th>\n", "      <th>99.75</th>\n", "      <th>99.875</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker_root</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-09-25</th>\n", "      <th>TUF4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.789062</td>\n", "      <td>0.703125</td>\n", "      <td>0.617188</td>\n", "      <td>0.546875</td>\n", "      <td>0.476562</td>\n", "      <td>0.414062</td>\n", "      <td>0.359375</td>\n", "      <td>0.312500</td>\n", "      <td>0.265625</td>\n", "      <td>0.226562</td>\n", "      <td>0.195312</td>\n", "      <td>0.164062</td>\n", "      <td>0.132812</td>\n", "      <td>0.117188</td>\n", "      <td>0.093750</td>\n", "      <td>0.078125</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.046875</td>\n", "      <td>0.039062</td>\n", "      <td>0.031250</td>\n", "      <td>0.023438</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUF4P</th>\n", "      <td>0.023438</td>\n", "      <td>0.031250</td>\n", "      <td>0.046875</td>\n", "      <td>0.054688</td>\n", "      <td>0.070312</td>\n", "      <td>0.085938</td>\n", "      <td>0.109375</td>\n", "      <td>0.132812</td>\n", "      <td>0.164062</td>\n", "      <td>0.195312</td>\n", "      <td>0.234375</td>\n", "      <td>0.273438</td>\n", "      <td>0.328125</td>\n", "      <td>0.382812</td>\n", "      <td>0.445312</td>\n", "      <td>0.507812</td>\n", "      <td>0.585938</td>\n", "      <td>0.664062</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUG4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.820312</td>\n", "      <td>0.734375</td>\n", "      <td>0.656250</td>\n", "      <td>0.585938</td>\n", "      <td>0.515625</td>\n", "      <td>0.453125</td>\n", "      <td>0.398438</td>\n", "      <td>0.343750</td>\n", "      <td>0.296875</td>\n", "      <td>0.257812</td>\n", "      <td>0.218750</td>\n", "      <td>0.187500</td>\n", "      <td>0.156250</td>\n", "      <td>0.132812</td>\n", "      <td>0.109375</td>\n", "      <td>0.085938</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.046875</td>\n", "      <td>0.039062</td>\n", "      <td>0.031250</td>\n", "      <td>0.023438</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUG4P</th>\n", "      <td>0.039062</td>\n", "      <td>0.046875</td>\n", "      <td>0.062500</td>\n", "      <td>0.078125</td>\n", "      <td>0.093750</td>\n", "      <td>0.109375</td>\n", "      <td>0.140625</td>\n", "      <td>0.164062</td>\n", "      <td>0.195312</td>\n", "      <td>0.234375</td>\n", "      <td>0.273438</td>\n", "      <td>0.320312</td>\n", "      <td>0.367188</td>\n", "      <td>0.421875</td>\n", "      <td>0.484375</td>\n", "      <td>0.546875</td>\n", "      <td>0.617188</td>\n", "      <td>0.695312</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "      <td>0.031250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUH4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.867188</td>\n", "      <td>0.781250</td>\n", "      <td>0.703125</td>\n", "      <td>0.632812</td>\n", "      <td>0.570312</td>\n", "      <td>0.507812</td>\n", "      <td>0.445312</td>\n", "      <td>0.390625</td>\n", "      <td>0.343750</td>\n", "      <td>0.304688</td>\n", "      <td>0.265625</td>\n", "      <td>0.226562</td>\n", "      <td>0.195312</td>\n", "      <td>0.171875</td>\n", "      <td>0.140625</td>\n", "      <td>0.125000</td>\n", "      <td>0.101562</td>\n", "      <td>0.085938</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.039062</td>\n", "      <td>0.039062</td>\n", "      <td>0.031250</td>\n", "      <td>0.023438</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.007812</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-10-27</th>\n", "      <th>TUM4P</th>\n", "      <td>0.117188</td>\n", "      <td>0.132812</td>\n", "      <td>0.148438</td>\n", "      <td>0.171875</td>\n", "      <td>0.195312</td>\n", "      <td>0.218750</td>\n", "      <td>0.242188</td>\n", "      <td>0.273438</td>\n", "      <td>0.304688</td>\n", "      <td>0.335938</td>\n", "      <td>0.375000</td>\n", "      <td>0.414062</td>\n", "      <td>0.453125</td>\n", "      <td>0.500000</td>\n", "      <td>0.554688</td>\n", "      <td>0.609375</td>\n", "      <td>0.664062</td>\n", "      <td>0.726562</td>\n", "      <td>0.789062</td>\n", "      <td>0.859375</td>\n", "      <td>0.929688</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.039062</td>\n", "      <td>0.039062</td>\n", "      <td>0.046875</td>\n", "      <td>0.054688</td>\n", "      <td>0.070312</td>\n", "      <td>0.078125</td>\n", "      <td>0.085938</td>\n", "      <td>0.101562</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUX3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.460938</td>\n", "      <td>0.335938</td>\n", "      <td>0.210938</td>\n", "      <td>0.085938</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUX3P</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.046875</td>\n", "      <td>0.171875</td>\n", "      <td>0.296875</td>\n", "      <td>0.421875</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.531250</td>\n", "      <td>0.445312</td>\n", "      <td>0.367188</td>\n", "      <td>0.304688</td>\n", "      <td>0.257812</td>\n", "      <td>0.218750</td>\n", "      <td>0.179688</td>\n", "      <td>0.148438</td>\n", "      <td>0.125000</td>\n", "      <td>0.101562</td>\n", "      <td>0.085938</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.046875</td>\n", "      <td>0.039062</td>\n", "      <td>0.031250</td>\n", "      <td>0.031250</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>TUZ3P</th>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "      <td>0.039062</td>\n", "      <td>0.054688</td>\n", "      <td>0.078125</td>\n", "      <td>0.117188</td>\n", "      <td>0.164062</td>\n", "      <td>0.226562</td>\n", "      <td>0.296875</td>\n", "      <td>0.382812</td>\n", "      <td>0.468750</td>\n", "      <td>0.562500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.007812</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>300 rows × 53 columns</p>\n", "</div>"], "text/plain": ["strike                       100   100.125    100.25   100.375     100.5  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C             NaN       NaN       NaN       NaN       NaN   \n", "           TUF4P        0.023438  0.031250  0.046875  0.054688  0.070312   \n", "           TUG4C             NaN       NaN       NaN       NaN       NaN   \n", "           TUG4P        0.039062  0.046875  0.062500  0.078125  0.093750   \n", "           TUH4C             NaN       NaN       NaN       NaN       NaN   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.117188  0.132812  0.148438  0.171875  0.195312   \n", "           TUX3C             NaN       NaN       NaN       NaN       NaN   \n", "           TUX3P        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUZ3C             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3P        0.007812  0.007812  0.007812  0.015625  0.023438   \n", "\n", "strike                   100.625    100.75   100.875       101   101.125  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C             NaN       NaN       NaN       NaN  0.789062   \n", "           TUF4P        0.085938  0.109375  0.132812  0.164062  0.195312   \n", "           TUG4C             NaN       NaN       NaN       NaN  0.820312   \n", "           TUG4P        0.109375  0.140625  0.164062  0.195312  0.234375   \n", "           TUH4C             NaN       NaN       NaN       NaN  0.867188   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.218750  0.242188  0.273438  0.304688  0.335938   \n", "           TUX3C             NaN       NaN  0.460938  0.335938  0.210938   \n", "           TUX3P        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUZ3C             NaN       NaN  0.531250  0.445312  0.367188   \n", "           TUZ3P        0.039062  0.054688  0.078125  0.117188  0.164062   \n", "\n", "strike                    101.25   101.375     101.5   101.625    101.75  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C        0.703125  0.617188  0.546875  0.476562  0.414062   \n", "           TUF4P        0.234375  0.273438  0.328125  0.382812  0.445312   \n", "           TUG4C        0.734375  0.656250  0.585938  0.515625  0.453125   \n", "           TUG4P        0.273438  0.320312  0.367188  0.421875  0.484375   \n", "           TUH4C        0.781250  0.703125  0.632812  0.570312  0.507812   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.375000  0.414062  0.453125  0.500000  0.554688   \n", "           TUX3C        0.085938  0.000000  0.000000  0.000000  0.000000   \n", "           TUX3P        0.000000  0.046875  0.171875  0.296875  0.421875   \n", "           TUZ3C        0.304688  0.257812  0.218750  0.179688  0.148438   \n", "           TUZ3P        0.226562  0.296875  0.382812  0.468750  0.562500   \n", "\n", "strike                   101.875       102   102.125    102.25   102.375  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C        0.359375  0.312500  0.265625  0.226562  0.195312   \n", "           TUF4P        0.507812  0.585938  0.664062       NaN       NaN   \n", "           TUG4C        0.398438  0.343750  0.296875  0.257812  0.218750   \n", "           TUG4P        0.546875  0.617188  0.695312       NaN       NaN   \n", "           TUH4C        0.445312  0.390625  0.343750  0.304688  0.265625   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.609375  0.664062  0.726562  0.789062  0.859375   \n", "           TUX3C        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUX3P             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3C        0.125000  0.101562  0.085938  0.070312  0.062500   \n", "           TUZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                     102.5   102.625    102.75   102.875       103  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C        0.164062  0.132812  0.117188  0.093750  0.078125   \n", "           TUF4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUG4C        0.187500  0.156250  0.132812  0.109375  0.085938   \n", "           TUG4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUH4C        0.226562  0.195312  0.171875  0.140625  0.125000   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.929688  1.000000       NaN       NaN       NaN   \n", "           TUX3C        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUX3P             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3C        0.054688  0.046875  0.039062  0.031250  0.031250   \n", "           TUZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                   103.125    103.25   103.375     103.5   103.625  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C        0.062500  0.054688  0.046875  0.039062  0.031250   \n", "           TUF4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUG4C        0.070312  0.062500  0.046875  0.039062  0.031250   \n", "           TUG4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUH4C        0.101562  0.085938  0.070312  0.062500  0.054688   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUX3C        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUX3P             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3C        0.023438  0.023438  0.023438  0.015625  0.015625   \n", "           TUZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                    103.75   103.875       104   104.125    104.25  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C        0.023438  0.015625  0.015625  0.015625  0.007812   \n", "           TUF4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUG4C        0.023438  0.015625  0.015625  0.007812  0.007812   \n", "           TUG4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUH4C        0.039062  0.039062  0.031250  0.023438  0.015625   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P             NaN       NaN       NaN       NaN       NaN   \n", "           TUX3C        0.000000  0.000000  0.000000  0.000000       NaN   \n", "           TUX3P             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3C        0.015625  0.015625  0.015625  0.015625       NaN   \n", "           TUZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                   104.375     104.5   104.625  104.75  104.875  105  \\\n", "date       ticker_root                                                       \n", "2023-09-25 TUF4C        0.007812  0.007812  0.007812     NaN      NaN  NaN   \n", "           TUF4P             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUG4C        0.007812  0.007812  0.000000     NaN      NaN  NaN   \n", "           TUG4P             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUH4C        0.015625  0.015625  0.007812     NaN      NaN  NaN   \n", "...                          ...       ...       ...     ...      ...  ...   \n", "2023-10-27 TUM4P             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUX3C             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUX3P             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUZ3C             NaN       NaN       NaN     NaN      NaN  NaN   \n", "           TUZ3P             NaN       NaN       NaN     NaN      NaN  NaN   \n", "\n", "strike                  98.5  98.625  98.75  98.875        99    99.125  \\\n", "date       ticker_root                                                    \n", "2023-09-25 TUF4C         NaN     NaN    NaN     NaN       NaN       NaN   \n", "           TUF4P         NaN     0.0    0.0     0.0  0.000000  0.000000   \n", "           TUG4C         NaN     NaN    NaN     NaN       NaN       NaN   \n", "           TUG4P         NaN     0.0    0.0     0.0  0.007812  0.007812   \n", "           TUH4C         NaN     NaN    NaN     NaN       NaN       NaN   \n", "...                      ...     ...    ...     ...       ...       ...   \n", "2023-10-27 TUM4P         NaN     NaN    NaN     NaN  0.039062  0.039062   \n", "           TUX3C         NaN     NaN    NaN     NaN       NaN       NaN   \n", "           TUX3P         0.0     0.0    0.0     0.0  0.000000  0.000000   \n", "           TUZ3C         NaN     NaN    NaN     NaN       NaN       NaN   \n", "           TUZ3P         0.0     0.0    0.0     0.0  0.000000  0.000000   \n", "\n", "strike                     99.25    99.375      99.5    99.625     99.75  \\\n", "date       ticker_root                                                     \n", "2023-09-25 TUF4C             NaN       NaN       NaN       NaN       NaN   \n", "           TUF4P        0.007812  0.007812  0.007812  0.007812  0.015625   \n", "           TUG4C             NaN       NaN       NaN       NaN       NaN   \n", "           TUG4P        0.007812  0.007812  0.015625  0.015625  0.023438   \n", "           TUH4C             NaN       NaN       NaN       NaN       NaN   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 TUM4P        0.046875  0.054688  0.070312  0.078125  0.085938   \n", "           TUX3C             NaN       NaN       NaN       NaN       NaN   \n", "           TUX3P        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "           TUZ3C             NaN       NaN       NaN       NaN       NaN   \n", "           TUZ3P        0.000000  0.000000  0.000000  0.000000  0.000000   \n", "\n", "strike                    99.875  \n", "date       ticker_root            \n", "2023-09-25 TUF4C             NaN  \n", "           TUF4P        0.023438  \n", "           TUG4C             NaN  \n", "           TUG4P        0.031250  \n", "           TUH4C             NaN  \n", "...                          ...  \n", "2023-10-27 TUM4P        0.101562  \n", "           TUX3C             NaN  \n", "           TUX3P        0.000000  \n", "           TUZ3C             NaN  \n", "           TUZ3P        0.007812  \n", "\n", "[300 rows x 53 columns]"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["loader.load_option_price(dt.date(2023,10,1), dt.date(2023,10,28))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["loader_5y = interestRateFutureOptionLoader(\"FV\")\n", "loader_us = interestRateFutureOptionLoader(\"US\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2023-09-25 to 2023-10-27\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-09-25 to 2023-10-27: ['FVH4 Comdty', 'FVZ23 Comdty', 'FVM4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV_underlying\\FV_20230925.parquet\n", "Loading underlying price from 2023-10-30 to 2023-11-24\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-10-30 to 2023-11-24: ['FVM4 Comdty', 'FVH4 Comdty', 'FVZ23 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV_underlying\\FV_20231030.parquet\n", "Loading underlying price from 2023-11-27 to 2023-12-22\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-11-27 to 2023-12-22: ['FVM4 Comdty', 'FVH4 Comdty', 'FVU4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV_underlying\\FV_20231127.parquet\n", "Loading underlying price from 2023-12-25 to 2024-01-26\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-12-25 to 2024-01-26: ['FVM4 Comdty', 'FVH4 Comdty', 'FVU4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV_underlying\\FV_20231225.parquet\n", "Loading underlying price from 2024-01-29 to 2024-02-23\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2024-01-29 to 2024-02-23: ['FVM4 Comdty', 'FVU4 Comdty', 'FVH4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV_underlying\\FV_20240129.parquet\n"]}], "source": ["loader_5y.load_option_price(dt.date(2023,10,1), dt.date(2024,2,24))"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading option price from 2023-09-25 to 2023-10-27\n", "Whole option price history missing (tickers num: 410), read data from bbg from 2023-09-25 to 2023-10-27\n", "Storing new option (FV) price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\FV\\FV_20230925.parquet\n", "Load data time: 1.9009828567504883\n", "Process data time: 0.0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strike</th>\n", "      <th>100</th>\n", "      <th>100.25</th>\n", "      <th>100.5</th>\n", "      <th>100.75</th>\n", "      <th>101</th>\n", "      <th>101.25</th>\n", "      <th>101.5</th>\n", "      <th>101.75</th>\n", "      <th>102</th>\n", "      <th>102.25</th>\n", "      <th>102.5</th>\n", "      <th>102.75</th>\n", "      <th>103</th>\n", "      <th>103.25</th>\n", "      <th>103.5</th>\n", "      <th>103.75</th>\n", "      <th>104</th>\n", "      <th>104.25</th>\n", "      <th>104.5</th>\n", "      <th>104.75</th>\n", "      <th>105</th>\n", "      <th>105.25</th>\n", "      <th>105.5</th>\n", "      <th>105.75</th>\n", "      <th>106</th>\n", "      <th>106.25</th>\n", "      <th>106.5</th>\n", "      <th>106.75</th>\n", "      <th>107</th>\n", "      <th>107.25</th>\n", "      <th>107.5</th>\n", "      <th>107.75</th>\n", "      <th>108</th>\n", "      <th>108.25</th>\n", "      <th>108.5</th>\n", "      <th>108.75</th>\n", "      <th>109</th>\n", "      <th>109.25</th>\n", "      <th>109.5</th>\n", "      <th>109.75</th>\n", "      <th>110</th>\n", "      <th>110.25</th>\n", "      <th>110.5</th>\n", "      <th>110.75</th>\n", "      <th>111</th>\n", "      <th>111.25</th>\n", "      <th>111.5</th>\n", "      <th>111.75</th>\n", "      <th>112</th>\n", "      <th>112.25</th>\n", "      <th>112.5</th>\n", "      <th>112.75</th>\n", "      <th>97.75</th>\n", "      <th>98</th>\n", "      <th>98.25</th>\n", "      <th>98.5</th>\n", "      <th>98.75</th>\n", "      <th>99</th>\n", "      <th>99.25</th>\n", "      <th>99.5</th>\n", "      <th>99.75</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker_root</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-09-25</th>\n", "      <th>FVF4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.125000</td>\n", "      <td>1.945312</td>\n", "      <td>1.765625</td>\n", "      <td>1.593750</td>\n", "      <td>1.437500</td>\n", "      <td>1.281250</td>\n", "      <td>1.148438</td>\n", "      <td>1.015625</td>\n", "      <td>0.906250</td>\n", "      <td>0.796875</td>\n", "      <td>0.703125</td>\n", "      <td>0.617188</td>\n", "      <td>0.539062</td>\n", "      <td>0.468750</td>\n", "      <td>0.414062</td>\n", "      <td>0.359375</td>\n", "      <td>0.312500</td>\n", "      <td>0.273438</td>\n", "      <td>0.242188</td>\n", "      <td>0.210938</td>\n", "      <td>0.187500</td>\n", "      <td>0.164062</td>\n", "      <td>0.140625</td>\n", "      <td>0.125000</td>\n", "      <td>0.109375</td>\n", "      <td>0.101562</td>\n", "      <td>0.085938</td>\n", "      <td>0.078125</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.046875</td>\n", "      <td>0.046875</td>\n", "      <td>0.039062</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVF4P</th>\n", "      <td>0.031250</td>\n", "      <td>0.031250</td>\n", "      <td>0.039062</td>\n", "      <td>0.046875</td>\n", "      <td>0.062500</td>\n", "      <td>0.070312</td>\n", "      <td>0.085938</td>\n", "      <td>0.101562</td>\n", "      <td>0.117188</td>\n", "      <td>0.132812</td>\n", "      <td>0.156250</td>\n", "      <td>0.187500</td>\n", "      <td>0.218750</td>\n", "      <td>0.250000</td>\n", "      <td>0.296875</td>\n", "      <td>0.343750</td>\n", "      <td>0.398438</td>\n", "      <td>0.453125</td>\n", "      <td>0.523438</td>\n", "      <td>0.601562</td>\n", "      <td>0.695312</td>\n", "      <td>0.789062</td>\n", "      <td>0.898438</td>\n", "      <td>1.015625</td>\n", "      <td>1.148438</td>\n", "      <td>1.289062</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVG4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.234375</td>\n", "      <td>2.054688</td>\n", "      <td>1.890625</td>\n", "      <td>1.726562</td>\n", "      <td>1.570312</td>\n", "      <td>1.429688</td>\n", "      <td>1.289062</td>\n", "      <td>1.164062</td>\n", "      <td>1.046875</td>\n", "      <td>0.937500</td>\n", "      <td>0.835938</td>\n", "      <td>0.750000</td>\n", "      <td>0.664062</td>\n", "      <td>0.593750</td>\n", "      <td>0.523438</td>\n", "      <td>0.468750</td>\n", "      <td>0.414062</td>\n", "      <td>0.367188</td>\n", "      <td>0.320312</td>\n", "      <td>0.281250</td>\n", "      <td>0.250000</td>\n", "      <td>0.218750</td>\n", "      <td>0.195312</td>\n", "      <td>0.171875</td>\n", "      <td>0.148438</td>\n", "      <td>0.132812</td>\n", "      <td>0.117188</td>\n", "      <td>0.101562</td>\n", "      <td>0.093750</td>\n", "      <td>0.078125</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.046875</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVG4P</th>\n", "      <td>0.046875</td>\n", "      <td>0.054688</td>\n", "      <td>0.062500</td>\n", "      <td>0.070312</td>\n", "      <td>0.085938</td>\n", "      <td>0.101562</td>\n", "      <td>0.117188</td>\n", "      <td>0.140625</td>\n", "      <td>0.164062</td>\n", "      <td>0.195312</td>\n", "      <td>0.226562</td>\n", "      <td>0.257812</td>\n", "      <td>0.296875</td>\n", "      <td>0.343750</td>\n", "      <td>0.390625</td>\n", "      <td>0.445312</td>\n", "      <td>0.507812</td>\n", "      <td>0.578125</td>\n", "      <td>0.656250</td>\n", "      <td>0.742188</td>\n", "      <td>0.828125</td>\n", "      <td>0.929688</td>\n", "      <td>1.046875</td>\n", "      <td>1.164062</td>\n", "      <td>1.296875</td>\n", "      <td>1.429688</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.031250</td>\n", "      <td>0.039062</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVH4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.523438</td>\n", "      <td>2.343750</td>\n", "      <td>2.171875</td>\n", "      <td>2.007812</td>\n", "      <td>1.843750</td>\n", "      <td>1.695312</td>\n", "      <td>1.554688</td>\n", "      <td>1.421875</td>\n", "      <td>1.296875</td>\n", "      <td>1.179688</td>\n", "      <td>1.070312</td>\n", "      <td>0.968750</td>\n", "      <td>0.875000</td>\n", "      <td>0.789062</td>\n", "      <td>0.710938</td>\n", "      <td>0.640625</td>\n", "      <td>0.578125</td>\n", "      <td>0.515625</td>\n", "      <td>0.468750</td>\n", "      <td>0.421875</td>\n", "      <td>0.375000</td>\n", "      <td>0.343750</td>\n", "      <td>0.304688</td>\n", "      <td>0.281250</td>\n", "      <td>0.250000</td>\n", "      <td>0.226562</td>\n", "      <td>0.203125</td>\n", "      <td>0.187500</td>\n", "      <td>0.164062</td>\n", "      <td>0.148438</td>\n", "      <td>0.132812</td>\n", "      <td>0.117188</td>\n", "      <td>0.101562</td>\n", "      <td>0.093750</td>\n", "      <td>0.085938</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-10-27</th>\n", "      <th>FVM4P</th>\n", "      <td>0.140625</td>\n", "      <td>0.164062</td>\n", "      <td>0.187500</td>\n", "      <td>0.218750</td>\n", "      <td>0.250000</td>\n", "      <td>0.281250</td>\n", "      <td>0.320312</td>\n", "      <td>0.359375</td>\n", "      <td>0.406250</td>\n", "      <td>0.453125</td>\n", "      <td>0.507812</td>\n", "      <td>0.562500</td>\n", "      <td>0.625000</td>\n", "      <td>0.695312</td>\n", "      <td>0.773438</td>\n", "      <td>0.851562</td>\n", "      <td>0.937500</td>\n", "      <td>1.023438</td>\n", "      <td>1.125000</td>\n", "      <td>1.226562</td>\n", "      <td>1.335938</td>\n", "      <td>1.453125</td>\n", "      <td>1.570312</td>\n", "      <td>1.695312</td>\n", "      <td>1.828125</td>\n", "      <td>1.968750</td>\n", "      <td>2.117188</td>\n", "      <td>2.265625</td>\n", "      <td>2.421875</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.046875</td>\n", "      <td>0.054688</td>\n", "      <td>0.070312</td>\n", "      <td>0.078125</td>\n", "      <td>0.093750</td>\n", "      <td>0.109375</td>\n", "      <td>0.125000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVX3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.187500</td>\n", "      <td>0.937500</td>\n", "      <td>0.687500</td>\n", "      <td>0.437500</td>\n", "      <td>0.187500</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVX3P</th>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.062500</td>\n", "      <td>0.312500</td>\n", "      <td>0.562500</td>\n", "      <td>0.812500</td>\n", "      <td>1.062500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVZ3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.375000</td>\n", "      <td>1.187500</td>\n", "      <td>1.015625</td>\n", "      <td>0.867188</td>\n", "      <td>0.726562</td>\n", "      <td>0.609375</td>\n", "      <td>0.507812</td>\n", "      <td>0.421875</td>\n", "      <td>0.351562</td>\n", "      <td>0.289062</td>\n", "      <td>0.242188</td>\n", "      <td>0.203125</td>\n", "      <td>0.164062</td>\n", "      <td>0.140625</td>\n", "      <td>0.117188</td>\n", "      <td>0.093750</td>\n", "      <td>0.078125</td>\n", "      <td>0.070312</td>\n", "      <td>0.062500</td>\n", "      <td>0.054688</td>\n", "      <td>0.046875</td>\n", "      <td>0.039062</td>\n", "      <td>0.039062</td>\n", "      <td>0.031250</td>\n", "      <td>0.031250</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>FVZ3P</th>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.015625</td>\n", "      <td>0.023438</td>\n", "      <td>0.023438</td>\n", "      <td>0.031250</td>\n", "      <td>0.039062</td>\n", "      <td>0.054688</td>\n", "      <td>0.078125</td>\n", "      <td>0.101562</td>\n", "      <td>0.140625</td>\n", "      <td>0.187500</td>\n", "      <td>0.250000</td>\n", "      <td>0.328125</td>\n", "      <td>0.429688</td>\n", "      <td>0.539062</td>\n", "      <td>0.671875</td>\n", "      <td>0.820312</td>\n", "      <td>0.984375</td>\n", "      <td>1.164062</td>\n", "      <td>1.351562</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "      <td>0.007812</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>300 rows × 61 columns</p>\n", "</div>"], "text/plain": ["strike                       100    100.25     100.5    100.75       101  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVF4P        0.031250  0.031250  0.039062  0.046875  0.062500   \n", "           FVG4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4P        0.046875  0.054688  0.062500  0.070312  0.085938   \n", "           FVH4C             NaN       NaN       NaN       NaN       NaN   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        0.140625  0.164062  0.187500  0.218750  0.250000   \n", "           FVX3C             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3P        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVZ3C             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3P        0.007812  0.007812  0.015625  0.015625  0.015625   \n", "\n", "strike                    101.25     101.5    101.75       102    102.25  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVF4P        0.070312  0.085938  0.101562  0.117188  0.132812   \n", "           FVG4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4P        0.101562  0.117188  0.140625  0.164062  0.195312   \n", "           FVH4C             NaN       NaN       NaN       NaN       NaN   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        0.281250  0.320312  0.359375  0.406250  0.453125   \n", "           FVX3C             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3P        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVZ3C             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3P        0.015625  0.023438  0.023438  0.031250  0.039062   \n", "\n", "strike                     102.5    102.75       103    103.25     103.5  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVF4P        0.156250  0.187500  0.218750  0.250000  0.296875   \n", "           FVG4C             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4P        0.226562  0.257812  0.296875  0.343750  0.390625   \n", "           FVH4C             NaN       NaN       NaN       NaN       NaN   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        0.507812  0.562500  0.625000  0.695312  0.773438   \n", "           FVX3C             NaN       NaN       NaN       NaN  1.187500   \n", "           FVX3P        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVZ3C             NaN       NaN       NaN       NaN  1.375000   \n", "           FVZ3P        0.054688  0.078125  0.101562  0.140625  0.187500   \n", "\n", "strike                    103.75       104    104.25     104.5    104.75  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C             NaN  2.125000  1.945312  1.765625  1.593750   \n", "           FVF4P        0.343750  0.398438  0.453125  0.523438  0.601562   \n", "           FVG4C             NaN  2.234375  2.054688  1.890625  1.726562   \n", "           FVG4P        0.445312  0.507812  0.578125  0.656250  0.742188   \n", "           FVH4C        2.523438  2.343750  2.171875  2.007812  1.843750   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        0.851562  0.937500  1.023438  1.125000  1.226562   \n", "           FVX3C        0.937500  0.687500  0.437500  0.187500  0.007812   \n", "           FVX3P        0.007812  0.007812  0.007812  0.007812  0.062500   \n", "           FVZ3C        1.187500  1.015625  0.867188  0.726562  0.609375   \n", "           FVZ3P        0.250000  0.328125  0.429688  0.539062  0.671875   \n", "\n", "strike                       105    105.25     105.5    105.75       106  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        1.437500  1.281250  1.148438  1.015625  0.906250   \n", "           FVF4P        0.695312  0.789062  0.898438  1.015625  1.148438   \n", "           FVG4C        1.570312  1.429688  1.289062  1.164062  1.046875   \n", "           FVG4P        0.828125  0.929688  1.046875  1.164062  1.296875   \n", "           FVH4C        1.695312  1.554688  1.421875  1.296875  1.179688   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        1.335938  1.453125  1.570312  1.695312  1.828125   \n", "           FVX3C        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVX3P        0.312500  0.562500  0.812500  1.062500       NaN   \n", "           FVZ3C        0.507812  0.421875  0.351562  0.289062  0.242188   \n", "           FVZ3P        0.820312  0.984375  1.164062  1.351562       NaN   \n", "\n", "strike                    106.25     106.5    106.75       107    107.25  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        0.796875  0.703125  0.617188  0.539062  0.468750   \n", "           FVF4P        1.289062       NaN       NaN       NaN       NaN   \n", "           FVG4C        0.937500  0.835938  0.750000  0.664062  0.593750   \n", "           FVG4P        1.429688       NaN       NaN       NaN       NaN   \n", "           FVH4C        1.070312  0.968750  0.875000  0.789062  0.710938   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P        1.968750  2.117188  2.265625  2.421875       NaN   \n", "           FVX3C        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVX3P             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3C        0.203125  0.164062  0.140625  0.117188  0.093750   \n", "           FVZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                     107.5    107.75       108    108.25     108.5  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        0.414062  0.359375  0.312500  0.273438  0.242188   \n", "           FVF4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4C        0.523438  0.468750  0.414062  0.367188  0.320312   \n", "           FVG4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVH4C        0.640625  0.578125  0.515625  0.468750  0.421875   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3C        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVX3P             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3C        0.078125  0.070312  0.062500  0.054688  0.046875   \n", "           FVZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                    108.75       109    109.25     109.5    109.75  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        0.210938  0.187500  0.164062  0.140625  0.125000   \n", "           FVF4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4C        0.281250  0.250000  0.218750  0.195312  0.171875   \n", "           FVG4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVH4C        0.375000  0.343750  0.304688  0.281250  0.250000   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3C        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVX3P             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3C        0.039062  0.039062  0.031250  0.031250  0.023438   \n", "           FVZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                       110    110.25     110.5    110.75       111  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        0.109375  0.101562  0.085938  0.078125  0.070312   \n", "           FVF4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4C        0.148438  0.132812  0.117188  0.101562  0.093750   \n", "           FVG4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVH4C        0.226562  0.203125  0.187500  0.164062  0.148438   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3C        0.007812  0.007812  0.007812  0.007812  0.007812   \n", "           FVX3P             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3C        0.023438  0.023438  0.023438  0.015625  0.015625   \n", "           FVZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                    111.25     111.5    111.75       112    112.25  \\\n", "date       ticker_root                                                     \n", "2023-09-25 FVF4C        0.062500  0.054688  0.046875  0.046875  0.039062   \n", "           FVF4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVG4C        0.078125  0.070312  0.062500  0.054688  0.046875   \n", "           FVG4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVH4C        0.132812  0.117188  0.101562  0.093750  0.085938   \n", "...                          ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P             NaN       NaN       NaN       NaN       NaN   \n", "           FVX3C        0.007812  0.007812       NaN       NaN       NaN   \n", "           FVX3P             NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3C        0.015625  0.015625       NaN       NaN       NaN   \n", "           FVZ3P             NaN       NaN       NaN       NaN       NaN   \n", "\n", "strike                  112.5  112.75     97.75        98     98.25      98.5  \\\n", "date       ticker_root                                                          \n", "2023-09-25 FVF4C          NaN     NaN       NaN       NaN       NaN       NaN   \n", "           FVF4P          NaN     NaN       NaN       NaN  0.007812  0.007812   \n", "           FVG4C          NaN     NaN       NaN       NaN       NaN       NaN   \n", "           FVG4P          NaN     NaN       NaN       NaN       NaN  0.015625   \n", "           FVH4C          NaN     NaN       NaN       NaN       NaN       NaN   \n", "...                       ...     ...       ...       ...       ...       ...   \n", "2023-10-27 FVM4P          NaN     NaN       NaN       NaN  0.046875  0.054688   \n", "           FVX3C          NaN     NaN       NaN       NaN       NaN       NaN   \n", "           FVX3P          NaN     NaN  0.007812  0.007812  0.007812  0.007812   \n", "           FVZ3C          NaN     NaN       NaN       NaN       NaN       NaN   \n", "           FVZ3P          NaN     NaN  0.000000  0.000000  0.000000  0.007812   \n", "\n", "strike                     98.75        99     99.25      99.5     99.75  \n", "date       ticker_root                                                    \n", "2023-09-25 FVF4C             NaN       NaN       NaN       NaN       NaN  \n", "           FVF4P        0.007812  0.015625  0.015625  0.023438  0.023438  \n", "           FVG4C             NaN       NaN       NaN       NaN       NaN  \n", "           FVG4P        0.015625  0.023438  0.023438  0.031250  0.039062  \n", "           FVH4C             NaN       NaN       NaN       NaN       NaN  \n", "...                          ...       ...       ...       ...       ...  \n", "2023-10-27 FVM4P        0.070312  0.078125  0.093750  0.109375  0.125000  \n", "           FVX3C             NaN       NaN       NaN       NaN       NaN  \n", "           FVX3P        0.007812  0.007812  0.007812  0.007812  0.007812  \n", "           FVZ3C             NaN       NaN       NaN       NaN       NaN  \n", "           FVZ3P        0.007812  0.007812  0.007812  0.007812  0.007812  \n", "\n", "[300 rows x 61 columns]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["loader_5y.load_option_price(dt.date(2023,10,1), dt.date(2023,10,28))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2024-01-29 to 2024-02-23\n", "Loading option price from 2023-09-25 to 2023-10-27\n", "Whole option price history missing (tickers num: 420), read data from bbg from 2023-09-25 to 2023-10-27\n", "Loading option price from 2023-10-30 to 2023-11-24\n", "Whole option price history missing (tickers num: 420), read data from bbg from 2023-10-30 to 2023-11-24\n", "Loading option price from 2023-11-27 to 2023-12-22\n", "Whole option price history missing (tickers num: 372), read data from bbg from 2023-11-27 to 2023-12-22\n", "Loading option price from 2023-12-25 to 2024-01-26\n", "Whole option price history missing (tickers num: 406), read data from bbg from 2023-12-25 to 2024-01-26\n", "Loading option price from 2024-01-29 to 2024-02-23\n", "Whole option price history missing (tickers num: 464), read data from bbg from 2024-01-29 to 2024-02-23\n"]}, {"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[38], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m loader_5y\u001b[38;5;241m.\u001b[39mload_option_price(dt\u001b[38;5;241m.\u001b[39mdate(\u001b[38;5;241m2023\u001b[39m,\u001b[38;5;241m10\u001b[39m,\u001b[38;5;241m1\u001b[39m), dt\u001b[38;5;241m.\u001b[39mdate(\u001b[38;5;241m2024\u001b[39m,\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m24\u001b[39m))\n", "File \u001b[1;32mc:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data_loader.py:904\u001b[0m, in \u001b[0;36mload_option_price\u001b[1;34m(self, start_date, end_date, **params)\u001b[0m\n\u001b[0;32m    901\u001b[0m whole_option_px_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mconcat(whole_option_px_data, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m    902\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m whole_option_px_data\u001b[38;5;241m.\u001b[39mempty\n\u001b[0;32m    903\u001b[0m whole_option_px_data \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m--> 904\u001b[0m     whole_option_px_data[\u001b[38;5;241m~\u001b[39mwhole_option_px_data\u001b[38;5;241m.\u001b[39mindex\u001b[38;5;241m.\u001b[39mduplicated(keep\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfirst\u001b[39m\u001b[38;5;124m\"\u001b[39m)]\n\u001b[0;32m    905\u001b[0m     \u001b[38;5;241m.\u001b[39mreset_index()\n\u001b[0;32m    906\u001b[0m     \u001b[38;5;241m.\u001b[39mpivot(index\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mticker_root\u001b[39m\u001b[38;5;124m\"\u001b[39m], columns\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstrike\u001b[39m\u001b[38;5;124m\"\u001b[39m, values\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprice\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    907\u001b[0m )  \u001b[38;5;66;03m# remove duplicated\u001b[39;00m\n\u001b[0;32m    909\u001b[0m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m**************** Finish loading option px data and the underlying data ****************\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    911\u001b[0m calculate_start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n", "\u001b[1;31mAssertionError\u001b[0m: "]}], "source": ["loader_5y.load_option_price(dt.date(2023,10,1), dt.date(2024,2,24))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2023-09-25 to 2023-10-27\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-09-25 to 2023-10-27: ['USH4 Comdty', 'USZ23 Comdty', 'USM4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\US_underlying\\US_20230925.parquet\n", "Loading underlying price from 2023-10-30 to 2023-11-24\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-10-30 to 2023-11-24: ['USM4 Comdty', 'USH4 Comdty', 'USZ23 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\US_underlying\\US_20231030.parquet\n", "Loading underlying price from 2023-11-27 to 2023-12-22\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-11-27 to 2023-12-22: ['USM4 Comdty', 'USH4 Comdty', 'USU4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\US_underlying\\US_20231127.parquet\n", "Loading underlying price from 2023-12-25 to 2024-01-26\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2023-12-25 to 2024-01-26: ['USM4 Comdty', 'USH4 Comdty', 'USU4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\US_underlying\\US_20231225.parquet\n", "Loading underlying price from 2024-01-29 to 2024-02-23\n", "Whole underlying price history missing, read ['PX_SETTLE', 'CONVENTIONAL_CTD_FORWARD_FRSK'] data from bbg from 2024-01-29 to 2024-02-23: ['USM4 Comdty', 'USU4 Comdty', 'USH4 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\US_underlying\\US_20240129.parquet\n"]}], "source": ["loader_us.load_option_price(dt.date(2023,10,1), dt.date(2024,2,24))"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2024-01-29 to 2024-02-23\n", "Loading option price from 2023-09-25 to 2023-10-27\n", "Whole option price history missing (tickers num: 469), read data from bbg from 2023-09-25 to 2023-10-27\n", "Loading option price from 2023-10-30 to 2023-11-24\n", "Whole option price history missing (tickers num: 456), read data from bbg from 2023-10-30 to 2023-11-24\n", "Loading option price from 2023-11-27 to 2023-12-22\n", "Whole option price history missing (tickers num: 459), read data from bbg from 2023-11-27 to 2023-12-22\n", "Loading option price from 2023-12-25 to 2024-01-26\n", "Whole option price history missing (tickers num: 445), read data from bbg from 2023-12-25 to 2024-01-26\n", "Loading option price from 2024-01-29 to 2024-02-23\n", "Whole option price history missing (tickers num: 452), read data from bbg from 2024-01-29 to 2024-02-23\n"]}, {"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[37], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m loader_us\u001b[38;5;241m.\u001b[39mload_option_price(dt\u001b[38;5;241m.\u001b[39mdate(\u001b[38;5;241m2023\u001b[39m,\u001b[38;5;241m10\u001b[39m,\u001b[38;5;241m1\u001b[39m), dt\u001b[38;5;241m.\u001b[39mdate(\u001b[38;5;241m2024\u001b[39m,\u001b[38;5;241m2\u001b[39m,\u001b[38;5;241m24\u001b[39m))\n", "File \u001b[1;32mc:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data_loader.py:904\u001b[0m, in \u001b[0;36mload_option_price\u001b[1;34m(self, start_date, end_date, **params)\u001b[0m\n\u001b[0;32m    901\u001b[0m whole_option_px_data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mconcat(whole_option_px_data, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m    902\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m whole_option_px_data\u001b[38;5;241m.\u001b[39mempty\n\u001b[0;32m    903\u001b[0m whole_option_px_data \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m--> 904\u001b[0m     whole_option_px_data[\u001b[38;5;241m~\u001b[39mwhole_option_px_data\u001b[38;5;241m.\u001b[39mindex\u001b[38;5;241m.\u001b[39mduplicated(keep\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfirst\u001b[39m\u001b[38;5;124m\"\u001b[39m)]\n\u001b[0;32m    905\u001b[0m     \u001b[38;5;241m.\u001b[39mreset_index()\n\u001b[0;32m    906\u001b[0m     \u001b[38;5;241m.\u001b[39mpivot(index\u001b[38;5;241m=\u001b[39m[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdate\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mticker_root\u001b[39m\u001b[38;5;124m\"\u001b[39m], columns\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstrike\u001b[39m\u001b[38;5;124m\"\u001b[39m, values\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprice\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    907\u001b[0m )  \u001b[38;5;66;03m# remove duplicated\u001b[39;00m\n\u001b[0;32m    909\u001b[0m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m**************** Finish loading option px data and the underlying data ****************\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    911\u001b[0m calculate_start_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n", "\u001b[1;31mAssertionError\u001b[0m: "]}], "source": ["loader_us.load_option_price(dt.date(2023,10,1), dt.date(2024,2,24))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["loader_er = interestRateFutureOptionLoader(\"ER\")\n", "loader_0r = interestRateFutureOptionLoader(\"0R\")\n", "loader_2r = interestRateFutureOptionLoader(\"2R\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["[(Date(16,10,2023), Date(10,11,2023)),\n", " (Date(13,11,2023), Date(15,12,2023)),\n", " (Date(18,12,2023), Date(12,1,2024)),\n", " (Date(15,1,2024), Date(16,2,2024)),\n", " (Date(19,2,2024), Date(15,3,2024)),\n", " (Date(18,3,2024), Date(12,4,2024)),\n", " (Date(15,4,2024), Date(10,5,2024)),\n", " (Date(13,5,2024), Date(14,6,2024)),\n", " (Date(17,6,2024), Date(12,7,2024)),\n", " (Date(15,7,2024), Date(16,8,2024)),\n", " (Date(19,8,2024), Date(13,9,2024)),\n", " (Date(16,9,2024), Date(11,10,2024)),\n", " (Date(14,10,2024), Date(15,11,2024)),\n", " (Date(18,11,2024), Date(13,12,2024)),\n", " (Date(16,12,2024), Date(30,12,2024))]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["loader_0r.get_periods(dt.date(2023,10,1), dt.date(2024,12,30))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2023-10-16 to 2023-11-10\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-10-16 to 2023-11-10: ['ERM5 Comdty', 'ERH5 Comdty', 'ERZ4 Comdty', 'ERU5 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0R_underlying\\0R_20231016.parquet\n", "Whole underlying price history missing, read ['PX_LAST'] data from bbg from 2023-10-16 to 2023-11-10: ['EESWEA Curncy', 'EESWE2Z Curncy', 'EESWE3Z Curncy', 'EESWEB Curncy', 'EESWE1Z Curncy', 'EESWEK Curncy', 'EESWED Curncy', 'EESWEJ Curncy', 'EESWEH Curncy', 'EESWEF Curncy', 'EESWEE Curncy', 'EESWEG Curncy', 'EESWEC Curncy']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\EUR_discount\\EUR_20231016.parquet\n", "Loading underlying price from 2023-11-13 to 2023-12-15\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-11-13 to 2023-12-15: ['ERM5 Comdty', 'ERH5 Comdty', 'ERZ4 Comdty', 'ERU5 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0R_underlying\\0R_20231113.parquet\n", "Whole underlying price history missing, read ['PX_LAST'] data from bbg from 2023-11-13 to 2023-12-15: ['EESWE3Z Curncy', 'EESWE2Z Curncy', 'EESWEA Curncy', 'EESWEB Curncy', 'EESWE1Z Curncy', 'EESWED Curncy', 'EESWEJ Curncy', 'EESWEF Curncy', 'EESWEI Curncy', 'EESWEE Curncy', 'EESWEG Curncy', 'EESWEC Curncy']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\EUR_discount\\EUR_20231113.parquet\n", "Loading underlying price from 2023-12-18 to 2024-01-12\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-12-18 to 2024-01-12: ['ERM5 Comdty', 'ERZ5 Comdty', 'ERH5 Comdty', 'ERU5 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0R_underlying\\0R_20231218.parquet\n", "Whole underlying price history missing, read ['PX_LAST'] data from bbg from 2023-12-18 to 2024-01-12: ['EESWE1 Curncy', 'EESWE3Z Curncy', 'EESWE2Z Curncy', 'EESWEA Curncy', 'EESWEB Curncy', 'EESWE1Z Curncy', 'EESWEK Curncy', 'EESWED Curncy', 'EESWEH Curncy', 'EESWEF Curncy', 'EESWEI Curncy', 'EESWEE Curncy', 'EESWEC Curncy']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\EUR_discount\\EUR_20231218.parquet\n", "Loading underlying price from 2024-01-15 to 2024-02-16\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2024-01-15 to 2024-02-16: ['ERM5 Comdty', 'ERU5 Comdty', 'ERZ5 Comdty', 'ERH5 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0R_underlying\\0R_20240115.parquet\n", "Whole underlying price history missing, read ['PX_LAST'] data from bbg from 2024-01-15 to 2024-02-16: ['EESWEA Curncy', 'EESWE2Z Curncy', 'EESWE3Z Curncy', 'EESWEB Curncy', 'EESWE1Z Curncy', 'EESWEK Curncy', 'EESWED Curncy', 'EESWEJ Curncy', 'EESWEH Curncy', 'EESWEF Curncy', 'EESWEE Curncy', 'EESWEG Curncy', 'EESWEC Curncy']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\EUR_discount\\EUR_20240115.parquet\n", "Loading underlying price from 2024-02-19 to 2024-03-15\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2024-02-19 to 2024-03-15: ['ERU5 Comdty', 'ERM5 Comdty', 'ERZ5 Comdty', 'ERH5 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\0R_underlying\\0R_20240219.parquet\n", "Whole underlying price history missing, read ['PX_LAST'] data from bbg from 2024-02-19 to 2024-03-15: ['EESWE3Z Curncy', 'EESWE2Z Curncy', 'EESWEA Curncy', 'EESWEB Curncy', 'EESWE1Z Curncy', 'EESWED Curncy', 'EESWEJ Curncy', 'EESWEF Curncy', 'EESWEI Curncy', 'EESWEE Curncy', 'EESWEG Curncy', 'EESWEC Curncy']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\EUR_discount\\EUR_20240219.parquet\n"]}], "source": ["loader_0r.load_option_price(dt.date(2023,10,1), dt.date(2024,3,16))"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading underlying price from 2023-10-16 to 2023-11-10\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-10-16 to 2023-11-10: ['ERM6 Comdty', 'ERH6 Comdty', 'ERZ5 Comdty', 'ERU6 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R_underlying\\2R_20231016.parquet\n", "Loading underlying price from 2023-11-13 to 2023-12-15\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-11-13 to 2023-12-15: ['ERM6 Comdty', 'ERH6 Comdty', 'ERZ5 Comdty', 'ERU6 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R_underlying\\2R_20231113.parquet\n", "Loading underlying price from 2023-12-18 to 2024-01-12\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2023-12-18 to 2024-01-12: ['ERM6 Comdty', 'ERZ6 Comdty', 'ERH6 Comdty', 'ERU6 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R_underlying\\2R_20231218.parquet\n", "Loading underlying price from 2024-01-15 to 2024-02-16\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2024-01-15 to 2024-02-16: ['ERM6 Comdty', 'ERU6 Comdty', 'ERZ6 Comdty', 'ERH6 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R_underlying\\2R_20240115.parquet\n", "Loading underlying price from 2024-02-19 to 2024-03-15\n", "Whole underlying price history missing, read ['PX_SETTLE'] data from bbg from 2024-02-19 to 2024-03-15: ['ERU6 Comdty', 'ERM6 Comdty', 'ERZ6 Comdty', 'ERH6 Comdty']\n", "Storing new underlying price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R_underlying\\2R_20240219.parquet\n"]}], "source": ["loader_2r.load_option_price(dt.date(2023,10,1), dt.date(2024,3,16))"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading option price from 2023-10-16 to 2023-11-10\n", "Whole option price history missing (tickers num: 332), read data from bbg from 2023-10-16 to 2023-11-10\n", "Storing new option (2R) price data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\ir_future_option_data\\2R\\2R_20231016.parquet\n", "Load data time: 1.4708845615386963\n", "Process data time: 0.0\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strike</th>\n", "      <th>95.25</th>\n", "      <th>95.375</th>\n", "      <th>95.5</th>\n", "      <th>95.625</th>\n", "      <th>95.75</th>\n", "      <th>95.875</th>\n", "      <th>96</th>\n", "      <th>96.125</th>\n", "      <th>96.25</th>\n", "      <th>96.375</th>\n", "      <th>96.5</th>\n", "      <th>96.625</th>\n", "      <th>96.75</th>\n", "      <th>96.875</th>\n", "      <th>97</th>\n", "      <th>97.125</th>\n", "      <th>97.25</th>\n", "      <th>97.375</th>\n", "      <th>97.5</th>\n", "      <th>97.625</th>\n", "      <th>97.75</th>\n", "      <th>97.875</th>\n", "      <th>98</th>\n", "      <th>98.125</th>\n", "      <th>98.25</th>\n", "      <th>98.375</th>\n", "      <th>98.5</th>\n", "      <th>98.625</th>\n", "      <th>98.75</th>\n", "      <th>98.875</th>\n", "      <th>99</th>\n", "      <th>99.25</th>\n", "      <th>99.5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>date</th>\n", "      <th>ticker_root</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-10-16</th>\n", "      <th>2RF4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.4000</td>\n", "      <td>0.3175</td>\n", "      <td>0.2450</td>\n", "      <td>0.1875</td>\n", "      <td>0.1400</td>\n", "      <td>0.1025</td>\n", "      <td>0.0750</td>\n", "      <td>0.0550</td>\n", "      <td>0.0400</td>\n", "      <td>0.0300</td>\n", "      <td>0.025</td>\n", "      <td>0.0200</td>\n", "      <td>0.0150</td>\n", "      <td>0.0125</td>\n", "      <td>0.010</td>\n", "      <td>0.0100</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RF4P</th>\n", "      <td>0.0025</td>\n", "      <td>0.0025</td>\n", "      <td>0.0025</td>\n", "      <td>0.005</td>\n", "      <td>0.0050</td>\n", "      <td>0.0100</td>\n", "      <td>0.0125</td>\n", "      <td>0.0175</td>\n", "      <td>0.0275</td>\n", "      <td>0.0400</td>\n", "      <td>0.0550</td>\n", "      <td>0.0800</td>\n", "      <td>0.1100</td>\n", "      <td>0.1525</td>\n", "      <td>0.2050</td>\n", "      <td>0.2725</td>\n", "      <td>0.3500</td>\n", "      <td>0.4375</td>\n", "      <td>0.5350</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RG4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.4375</td>\n", "      <td>0.3575</td>\n", "      <td>0.2850</td>\n", "      <td>0.2250</td>\n", "      <td>0.1750</td>\n", "      <td>0.1350</td>\n", "      <td>0.1050</td>\n", "      <td>0.0800</td>\n", "      <td>0.0625</td>\n", "      <td>0.0500</td>\n", "      <td>0.040</td>\n", "      <td>0.0325</td>\n", "      <td>0.0275</td>\n", "      <td>0.0225</td>\n", "      <td>0.020</td>\n", "      <td>0.0175</td>\n", "      <td>0.0150</td>\n", "      <td>0.0125</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RG4P</th>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.0075</td>\n", "      <td>0.010</td>\n", "      <td>0.0150</td>\n", "      <td>0.0200</td>\n", "      <td>0.0250</td>\n", "      <td>0.0350</td>\n", "      <td>0.0475</td>\n", "      <td>0.0625</td>\n", "      <td>0.0850</td>\n", "      <td>0.1125</td>\n", "      <td>0.1475</td>\n", "      <td>0.1925</td>\n", "      <td>0.2450</td>\n", "      <td>0.3100</td>\n", "      <td>0.3850</td>\n", "      <td>0.4700</td>\n", "      <td>0.5650</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RH4C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.4625</td>\n", "      <td>0.3825</td>\n", "      <td>0.3125</td>\n", "      <td>0.2525</td>\n", "      <td>0.2000</td>\n", "      <td>0.1600</td>\n", "      <td>0.1250</td>\n", "      <td>0.1000</td>\n", "      <td>0.0775</td>\n", "      <td>0.0625</td>\n", "      <td>0.050</td>\n", "      <td>0.0425</td>\n", "      <td>0.0350</td>\n", "      <td>0.0300</td>\n", "      <td>0.025</td>\n", "      <td>0.0225</td>\n", "      <td>0.0200</td>\n", "      <td>0.0175</td>\n", "      <td>0.0150</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">2023-11-10</th>\n", "      <th>2RU4P</th>\n", "      <td>0.0150</td>\n", "      <td>0.0175</td>\n", "      <td>0.0225</td>\n", "      <td>0.030</td>\n", "      <td>0.0375</td>\n", "      <td>0.0475</td>\n", "      <td>0.0600</td>\n", "      <td>0.0750</td>\n", "      <td>0.0925</td>\n", "      <td>0.1150</td>\n", "      <td>0.1425</td>\n", "      <td>0.1725</td>\n", "      <td>0.2100</td>\n", "      <td>0.2500</td>\n", "      <td>0.3000</td>\n", "      <td>0.3525</td>\n", "      <td>0.4150</td>\n", "      <td>0.4825</td>\n", "      <td>0.5550</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RX3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.6350</td>\n", "      <td>0.5100</td>\n", "      <td>0.3850</td>\n", "      <td>0.2600</td>\n", "      <td>0.1350</td>\n", "      <td>0.0100</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RX3P</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.1150</td>\n", "      <td>0.2400</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RZ3C</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.6425</td>\n", "      <td>0.5225</td>\n", "      <td>0.4075</td>\n", "      <td>0.3025</td>\n", "      <td>0.2100</td>\n", "      <td>0.1375</td>\n", "      <td>0.0850</td>\n", "      <td>0.0525</td>\n", "      <td>0.0325</td>\n", "      <td>0.0200</td>\n", "      <td>0.0150</td>\n", "      <td>0.010</td>\n", "      <td>0.0075</td>\n", "      <td>0.0075</td>\n", "      <td>0.0050</td>\n", "      <td>0.005</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.0050</td>\n", "      <td>0.0025</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2RZ3P</th>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0025</td>\n", "      <td>0.0025</td>\n", "      <td>0.0075</td>\n", "      <td>0.0125</td>\n", "      <td>0.0225</td>\n", "      <td>0.0425</td>\n", "      <td>0.0750</td>\n", "      <td>0.1275</td>\n", "      <td>0.2000</td>\n", "      <td>0.2925</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>320 rows × 33 columns</p>\n", "</div>"], "text/plain": ["strike                   95.25  95.375    95.5  95.625   95.75  95.875  \\\n", "date       ticker_root                                                   \n", "2023-10-16 2RF4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RF4P        0.0025  0.0025  0.0025   0.005  0.0050  0.0100   \n", "           2RG4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RG4P        0.0050  0.0050  0.0075   0.010  0.0150  0.0200   \n", "           2RH4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "...                        ...     ...     ...     ...     ...     ...   \n", "2023-11-10 2RU4P        0.0150  0.0175  0.0225   0.030  0.0375  0.0475   \n", "           2RX3C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RX3P        0.0000  0.0000  0.0000   0.000  0.0000  0.0000   \n", "           2RZ3C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RZ3P        0.0000  0.0000  0.0000   0.000  0.0000  0.0000   \n", "\n", "strike                      96  96.125   96.25  96.375    96.5  96.625  \\\n", "date       ticker_root                                                   \n", "2023-10-16 2RF4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RF4P        0.0125  0.0175  0.0275  0.0400  0.0550  0.0800   \n", "           2RG4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "           2RG4P        0.0250  0.0350  0.0475  0.0625  0.0850  0.1125   \n", "           2RH4C           NaN     NaN     NaN     NaN     NaN     NaN   \n", "...                        ...     ...     ...     ...     ...     ...   \n", "2023-11-10 2RU4P        0.0600  0.0750  0.0925  0.1150  0.1425  0.1725   \n", "           2RX3C           NaN     NaN     NaN     NaN     NaN  0.6350   \n", "           2RX3P        0.0000  0.0000  0.0000  0.0000  0.0000  0.0000   \n", "           2RZ3C           NaN     NaN     NaN     NaN     NaN  0.6425   \n", "           2RZ3P        0.0000  0.0000  0.0000  0.0025  0.0025  0.0075   \n", "\n", "strike                   96.75  96.875      97  97.125   97.25  97.375  \\\n", "date       ticker_root                                                   \n", "2023-10-16 2RF4C        0.4000  0.3175  0.2450  0.1875  0.1400  0.1025   \n", "           2RF4P        0.1100  0.1525  0.2050  0.2725  0.3500  0.4375   \n", "           2RG4C        0.4375  0.3575  0.2850  0.2250  0.1750  0.1350   \n", "           2RG4P        0.1475  0.1925  0.2450  0.3100  0.3850  0.4700   \n", "           2RH4C        0.4625  0.3825  0.3125  0.2525  0.2000  0.1600   \n", "...                        ...     ...     ...     ...     ...     ...   \n", "2023-11-10 2RU4P        0.2100  0.2500  0.3000  0.3525  0.4150  0.4825   \n", "           2RX3C        0.5100  0.3850  0.2600  0.1350  0.0100  0.0000   \n", "           2RX3P        0.0000  0.0000  0.0000  0.0000  0.0000  0.1150   \n", "           2RZ3C        0.5225  0.4075  0.3025  0.2100  0.1375  0.0850   \n", "           2RZ3P        0.0125  0.0225  0.0425  0.0750  0.1275  0.2000   \n", "\n", "strike                    97.5  97.625   97.75  97.875     98  98.125   98.25  \\\n", "date       ticker_root                                                          \n", "2023-10-16 2RF4C        0.0750  0.0550  0.0400  0.0300  0.025  0.0200  0.0150   \n", "           2RF4P        0.5350     NaN     NaN     NaN    NaN     NaN     NaN   \n", "           2RG4C        0.1050  0.0800  0.0625  0.0500  0.040  0.0325  0.0275   \n", "           2RG4P        0.5650     NaN     NaN     NaN    NaN     NaN     NaN   \n", "           2RH4C        0.1250  0.1000  0.0775  0.0625  0.050  0.0425  0.0350   \n", "...                        ...     ...     ...     ...    ...     ...     ...   \n", "2023-11-10 2RU4P        0.5550     NaN     NaN     NaN    NaN     NaN     NaN   \n", "           2RX3C        0.0000  0.0000  0.0000  0.0000  0.000  0.0000  0.0000   \n", "           2RX3P        0.2400     NaN     NaN     NaN    NaN     NaN     NaN   \n", "           2RZ3C        0.0525  0.0325  0.0200  0.0150  0.010  0.0075  0.0075   \n", "           2RZ3P        0.2925     NaN     NaN     NaN    NaN     NaN     NaN   \n", "\n", "strike                  98.375   98.5  98.625   98.75  98.875      99  99.25  \\\n", "date       ticker_root                                                         \n", "2023-10-16 2RF4C        0.0125  0.010  0.0100  0.0075  0.0075     NaN    NaN   \n", "           2RF4P           NaN    NaN     NaN     NaN     NaN     NaN    NaN   \n", "           2RG4C        0.0225  0.020  0.0175  0.0150  0.0125     NaN    NaN   \n", "           2RG4P           NaN    NaN     NaN     NaN     NaN     NaN    NaN   \n", "           2RH4C        0.0300  0.025  0.0225  0.0200  0.0175  0.0150    NaN   \n", "...                        ...    ...     ...     ...     ...     ...    ...   \n", "2023-11-10 2RU4P           NaN    NaN     NaN     NaN     NaN     NaN    NaN   \n", "           2RX3C        0.0000  0.000  0.0000  0.0000  0.0000  0.0000    NaN   \n", "           2RX3P           NaN    NaN     NaN     NaN     NaN     NaN    NaN   \n", "           2RZ3C        0.0050  0.005  0.0050  0.0050  0.0050  0.0025    NaN   \n", "           2RZ3P           NaN    NaN     NaN     NaN     NaN     NaN    NaN   \n", "\n", "strike                  99.5  \n", "date       ticker_root        \n", "2023-10-16 2RF4C         NaN  \n", "           2RF4P         NaN  \n", "           2RG4C         NaN  \n", "           2RG4P         NaN  \n", "           2RH4C         NaN  \n", "...                      ...  \n", "2023-11-10 2RU4P         NaN  \n", "           2RX3C         NaN  \n", "           2RX3P         NaN  \n", "           2RZ3C         NaN  \n", "           2RZ3P         NaN  \n", "\n", "[320 rows x 33 columns]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["loader_2r.load_option_price(dt.date(2023,10,1), dt.date(2023,11,10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}