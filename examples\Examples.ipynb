{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "import QuantLib as ql\n", "import pandas as pd\n", "import numpy as np\n", "import datetime as dt\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['c:\\\\Users\\\\<USER>\\\\Documents\\\\investment\\\\examples',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\python312.zip',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\DLLs',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib',\n", " 'c:\\\\ProgramData\\\\anaconda3',\n", " '',\n", " 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python312\\\\site-packages',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\win32',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\win32\\\\lib',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\Pythonwin',\n", " 'c:\\\\Users\\\\<USER>\\\\Documents\\\\investment']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["sys.path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2021-10-15, 2021-10-15]\n", "Loading close price from 2020-12-17\n", "Opening cached curve benchmark data from bbg, FILE:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20201217_marketclose.parquet\n", "OIS term: 3W , Maturity: 2021-11-09, Quote: 0.0525, Pillar date: 2021-11-03\n", "OIS term: 18M, Maturity: 2023-04-19, Quote: 0.2400, Pillar date: 2023-04-19\n", "OIS term: 2Y , Maturity: 2023-10-19, Quote: 0.3903, Pillar date: 2023-10-19\n", "OIS term: 3Y , Maturity: 2024-10-21, Quote: 0.6508, Pillar date: 2024-10-21\n", "OIS term: 4Y , Maturity: 2025-10-20, Quote: 0.8367, Pillar date: 2025-10-20\n", "OIS term: 5Y , Maturity: 2026-10-19, Quote: 0.9703, Pillar date: 2026-10-19\n", "OIS term: 6Y , Maturity: 2027-10-19, Quote: 1.0757, Pillar date: 2027-10-19\n", "OIS term: 7Y , Maturity: 2028-10-19, Quote: 1.1583, Pillar date: 2028-10-19\n", "OIS term: 8Y , Maturity: 2029-10-19, Quote: 1.2242, Pillar date: 2029-10-19\n", "OIS term: 9Y , Maturity: 2030-10-21, Quote: 1.2770, Pillar date: 2030-10-21\n", "OIS term: 10Y, Maturity: 2031-10-20, Quote: 1.3230, Pillar date: 2031-10-20\n", "OIS term: 11Y, Maturity: 2032-10-19, Quote: 1.3628, Pillar date: 2032-10-19\n", "OIS term: 12Y, Maturity: 2033-10-19, Quote: 1.3963, Pillar date: 2033-10-19\n", "OIS term: 15Y, Maturity: 2036-10-20, Quote: 1.4688, Pillar date: 2036-10-20\n", "OIS term: 20Y, Maturity: 2041-10-21, Quote: 1.5319, Pillar date: 2041-10-21\n", "OIS term: 25Y, Maturity: 2046-10-19, Quote: 1.5427, Pillar date: 2046-10-19\n", "OIS term: 30Y, Maturity: 2051-10-19, Quote: 1.5365, Pillar date: 2051-10-19\n", "OIS term: 40Y, Maturity: 2061-10-19, Quote: 1.4715, Pillar date: 2061-10-19\n", "OIS term: 50Y, Maturity: 2071-10-19, Quote: 1.3845, Pillar date: 2071-10-19\n", "Meeting dated swap term s1, 2021-11-03 - 2021-12-15, Maturity: 2021-12-15, Quote: 0.0520, Pillar date: 2021-12-15\n", "Meeting dated swap term s2, 2021-12-15 - 2022-01-26, Maturity: 2022-01-26, Quote: 0.0540, Pillar date: 2022-01-26\n", "Meeting dated swap term s3, 2022-01-26 - 2022-03-16, Maturity: 2022-03-16, Quote: 0.0620, Pillar date: 2022-03-16\n", "Meeting dated swap term s4, 2022-03-16 - 2022-05-04, Maturity: 2022-05-04, Quote: 0.0750, Pillar date: 2022-05-04\n", "Meeting dated swap term s5, 2022-05-04 - 2022-06-15, Maturity: 2022-06-15, Quote: 0.1020, Pillar date: 2022-06-15\n", "Meeting dated swap term s6, 2022-06-15 - 2022-07-27, Maturity: 2022-07-27, Quote: 0.1455, Pillar date: 2022-07-27\n", "Meeting dated swap term s7, 2022-07-27 - 2022-09-21, Maturity: 2022-09-21, Quote: nan, Pillar date: 2022-09-21\n"]}], "source": ["from curve import sofrCurve\n", "from helpers.date_helpers import to_ql_date\n", "sofr = sofrCurve(\"2021-10-15\")\n", "ql.Settings.instance().evaluationDate = to_ql_date(sofr.date)\n", "helpers = sofr.set_up_helpers()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-10-16, 2024-10-16]\n", "Loading close price from 2024-01-01\n", "Opening cached curve benchmark data from bbg, FILE:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\CAD_CORRA_20240101_marketclose.parquet\n", "OIS term: 1W , Maturity: 2024-10-24, Quote: 4.3000, Pillar date: 2024-10-24\n", "Meeting dated swap term s1, 2024-10-24 - 2024-12-12, Maturity: 2024-12-12, Quote: 3.8500, Pillar date: 2024-12-12\n", "Meeting dated swap term s2, 2024-12-12 - 2025-01-30, Maturity: 2025-01-30, Quote: 3.4675, Pillar date: 2025-01-30\n", "Meeting dated swap term s3, 2025-01-30 - 2025-03-13, Maturity: 2025-03-13, Quote: 3.2160, Pillar date: 2025-03-13\n", "Meeting dated swap term s4, 2025-03-13 - 2025-04-17, Maturity: 2025-04-17, Quote: 3.0300, Pillar date: 2025-04-17\n", "Meeting dated swap term s5, 2025-04-17 - 2025-06-05, Maturity: 2025-06-05, Quote: 2.9000, Pillar date: 2025-06-05\n", "Meeting dated swap term s6, 2025-06-05 - 2025-07-31, Maturity: 2025-07-31, Quote: 2.8090, Pillar date: 2025-07-31\n", "Meeting dated swap term s7, 2025-07-31 - 2025-09-18, Maturity: 2025-09-18, Quote: 2.7675, Pillar date: 2025-09-18\n", "OIS term: 18M, Maturity: 2026-04-17, Quote: 3.0019, Pillar date: 2026-04-17\n", "OIS term: 2Y , Maturity: 2026-10-19, Quote: 2.9075, Pillar date: 2026-10-19\n", "OIS term: 3Y , Maturity: 2027-10-18, Quote: 2.8090, Pillar date: 2027-10-18\n", "OIS term: 4Y , Maturity: 2028-10-17, Quote: 2.7675, Pillar date: 2028-10-17\n", "OIS term: 5Y , Maturity: 2029-10-17, Quote: 2.7630, Pillar date: 2029-10-17\n", "OIS term: 6Y , Maturity: 2030-10-17, Quote: 2.7880, Pillar date: 2030-10-17\n", "OIS term: 7Y , Maturity: 2031-10-17, Quote: 2.8295, Pillar date: 2031-10-17\n", "OIS term: 8Y , Maturity: 2032-10-18, Quote: 2.8760, Pillar date: 2032-10-18\n", "OIS term: 9Y , Maturity: 2033-10-17, Quote: 2.9245, Pillar date: 2033-10-17\n", "OIS term: 10Y, Maturity: 2034-10-17, Quote: 2.9715, Pillar date: 2034-10-17\n", "OIS term: 12Y, Maturity: 2036-10-17, Quote: 3.0565, <PERSON>llar date: 2036-10-17\n", "OIS term: 15Y, Maturity: 2039-10-17, Quote: 3.1520, Pillar date: 2039-10-17\n", "OIS term: 20Y, Maturity: 2044-10-17, Quote: 3.2086, Pillar date: 2044-10-17\n", "OIS term: 25Y, Maturity: 2049-10-18, Quote: 3.1549, Pillar date: 2049-10-18\n", "OIS term: 30Y, Maturity: 2054-10-19, Quote: 3.0863, Pillar date: 2054-10-19\n", "OIS term: 40Y, Maturity: 2064-10-17, Quote: 2.9239, Pillar date: 2064-10-17\n"]}], "source": ["from curve import corraCurve\n", "from helpers.date_helpers import to_ql_date\n", "corra_curve = corraCurve(\"2024-10-16\")\n", "ql.Settings.instance().evaluationDate = to_ql_date(corra_curve.date)\n", "helpers = corra_curve.set_up_helpers()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(18,12,2024), Date(5,2,2025))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from utils import get_next_2_meeting_dates\n", "get_next_2_meeting_dates(dt.date(2024,10,30),1,'EUR')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(23,10,2024), Date(18,12,2024))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["get_next_2_meeting_dates(dt.date(2024,10,20),1,'EUR')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(18,12,2024), Date(5,2,2025))"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["get_next_2_meeting_dates(dt.date(2024,10,21),1,'EUR')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(18,12,2024), Date(5,2,2025))"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["get_next_2_meeting_dates(dt.date(2024,12,15),1,'EUR')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(5,2,2025), Date(12,3,2025))"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["get_next_2_meeting_dates(dt.date(2024,12,16),1,'EUR')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(20,3,2024), Date(29,4,2024))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["get_next_2_meeting_dates(dt.date(2024,1,24),1,'JPY')"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["ql.Settings.instance().evaluationDate = ql.Date(16, ql.October, 2024)"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [], "source": ["helpers = [\n", "    ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ql.<PERSON>uo<PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(rate/100)),\n", "        ql.Sofr())\n", "    for rate, fixingDays in [(4.81, 0), (4.81,2)]\n", "]"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["helpers = [\n", "    ql.DatedOISRateHelper(start_date, end_date,\n", "                          ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(rate/100)), ql.<PERSON>fr())\n", "    for rate, start_date, end_date in [\n", "        (4.8444, ql.Date(7, 11, 2024), ql.Date(18, 12, 2024))\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["helpers += [\n", "    ql.SofrFutureRateHelper(ql.Quo<PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(price)), *tenor, ql.Quarterly)\n", "    for price, tenor in [(96.1, (3, 2025)),\n", "                         (96.395, (6, 2025)),\n", "                         (96.595, (9, 2025)),\n", "                        ]\n", "]"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [], "source": ["helpers += [\n", "    ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(2, ql.Period(*tenor),\n", "        ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(rate/100)), ql.<PERSON>fr())\n", "    for rate, tenor in [(3.853, (18,ql.Months)),\n", "                        (3.7322, (2,ql.Years)),\n", "                        (3.6026, (3,ql.Years)),\n", "                        (3.53955, (4,ql.Years)),\n", "                        (3.51205, (5,ql.Years)),\n", "                        (3.5055, (6,ql.Years)), \n", "                        (3.508, (7,ql.Years)),\n", "                        (3.517, (8,ql.Years)), \n", "                        (3.529, (9,ql.Years)),\n", "                        (3.54285, (10,ql.Years)), \n", "                        (3.5589, (11,ql.Years)),\n", "                        (3.575, (12,ql.Years)),\n", "                        (3.61325, (15,ql.Years)),\n", "                        (3.6209, (20,ql.Years)),\n", "                        (3.5619, (25,ql.Years)),\n", "                        (3.4862, (30,ql.Years)),\n", "                        (3.3022, (40,ql.Years)),\n", "                        (3.1209, (50,ql.Years)),\n", "        ]\n", "]\n", "\n"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.435175 % Actual/360 continuous compounding\n"]}], "source": ["sofr_curve_c = ql.PiecewiseLogLinearDiscount(0, ql.UnitedStates(ql.UnitedStates.SOFR), helpers, ql.Actual360())\n", "sofr_curve_c.enableExtrapolation()\n", "print(sofr_curve_c.zeroRate(0.3, ql.Continuous))"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["sofr_curve_c_2 = ql.PiecewiseLogLinearDiscount(ql.Date(16, ql.October, 2024), helpers, ql.Actual360())\n", "sofr_curve_c_2.enableExtrapolation()"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["October 16th, 2024 October 18th, 2074\n", "October 16th, 2024 October 18th, 2074\n"]}], "source": ["print(sofr_curve_c.referenceDate(), sofr_curve_c.maxDate())\n", "print(sofr_curve_c_2.referenceDate(), sofr_curve_c_2.maxDate())"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["ql.Settings.instance().evaluationDate = ql.Date(16, ql.October, 2024)"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(16,10,2024), Date(18,10,2074))"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["sofr_curve_c.referenceDate(), sofr_curve_c.maxDate()"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.991917 % Actual/360 continuous compounding\n"]}], "source": ["print(sofr_curve_c_2.zeroRate(ql.Date(7, ql.September, 2025),ql.Actual360(), ql.Continuous))"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3.791730 % Actual/360 continuous compounding\n"]}], "source": ["ql.Settings.instance().evaluationDate = ql.Date(16, ql.October, 2014)\n", "print(sofr_curve_c.zeroRate(0.3, ql.Continuous))"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.435175 % Actual/360 continuous compounding\n"]}], "source": ["ql.Settings.instance().evaluationDate = ql.Date(16, ql.October, 2024)\n", "print(sofr_curve_c.zeroRate(0.3, ql.Continuous))"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import math\n", "import utils"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["today = sofr_curve_c.referenceDate()\n", "end = today + ql.Period(2,ql.Years)\n", "dates = [ ql.Date(serial) for serial in range(today.serialNumber(),\n", "end.serialNumber()+1) ]\n", "rates_c = [ sofr_curve_c.forwardRate(d, ql.UnitedStates(ql.UnitedStates.SOFR).advance(d,1,ql.Days),\n", "ql.Actual360(), ql.Simple).rate()\n", "for d in dates ]"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import math\n", "import utils\n", "_, ax = utils.plot()\n", "utils.highlight_x_axis(ax)\n", "utils.plot_curve(ax, dates, [(rates_c,'-')], format_rates=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [], "source": ["def quick_plot_curve(curve):\n", "    today = curve.referenceDate()\n", "    end = today + ql.Period(5,ql.Years)\n", "    dates = [ ql.Date(serial) for serial in range(today.serialNumber(),\n", "    end.serialNumber()+1) ]\n", "    rates = [ curve.forwardRate(d, ql.UnitedStates(ql.UnitedStates.SOFR).advance(d,1,ql.Days),\n", "    ql.Actual360(), ql.Simple).rate()\n", "    for d in dates ]\n", "    _, ax = utils.plot()\n", "    utils.highlight_x_axis(ax)\n", "    utils.plot_curve(ax, dates, [(rates,'-')], format_rates=True)"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Date(3,10,2014),\n", " Date(7,10,2016),\n", " Date(9,10,2017),\n", " Date(7,10,2019),\n", " Date(7,10,2024),\n", " Date(8,10,2029))"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["dates"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1.0,\n", " 0.9959741956740784,\n", " 0.9922773860954109,\n", " 0.9770157433457669,\n", " 0.8895693789825089,\n", " 0.7821475796617587)"]}, "execution_count": 152, "metadata": {}, "output_type": "execute_result"}], "source": ["rates"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["curve2 = ql.DiscountCurve()\n", "quick_plot_curve(curve2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Discount Factor on January 1st, 2023 from curve on January 1st, 2020: 1.0023695879275767\n", "Discount Factor on January 1st, 2023 from curve on January 1st, 2021: 0.9992161872286717\n", "Discount Factor on January 1st, 2023 from curve on January 1st, 2022: 0.9995262301338873\n", "Discount Factor on January 1st, 2023 from curve on January 1st, 2020: 0.9947544854166883\n", "Discount Factor on January 1st, 2023 from curve on January 1st, 2021: 0.9962715368769681\n"]}, {"ename": "RuntimeError", "evalue": "1st iteration: failed at 1st alive instrument, pillar January 6th, 2026, maturity January 6th, 2026, reference date January 1st, 2022: 2nd leg: negative time (-0.490411) given", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "\u001b[1;32m~\\AppData\\Local\\Temp\\ipykernel_172836\\2915121757.py\u001b[0m in \u001b[0;36m?\u001b[1;34m()\u001b[0m\n\u001b[0;32m     67\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33mf\"\u001b[0m\u001b[1;33mDiscount Factor on \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mquery_date\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m from curve on \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mhist_date\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m: \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mdiscount_factor\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     68\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     69\u001b[0m \u001b[0mql\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mSettings\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0minstance\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mevaluationDate\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mql\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mDate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;36m2021\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     70\u001b[0m \u001b[1;32mfor\u001b[0m \u001b[0mhist_date\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mcurve\u001b[0m \u001b[1;32min\u001b[0m \u001b[0mhistorical_curves\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mitems\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 71\u001b[1;33m     \u001b[0mdiscount_factor\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mcurve\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mdiscount\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mquery_date\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     72\u001b[0m     \u001b[0mprint\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33mf\"\u001b[0m\u001b[1;33mDiscount Factor on \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mquery_date\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m from curve on \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mhist_date\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m: \u001b[0m\u001b[1;33m{\u001b[0m\u001b[0mdiscount_factor\u001b[0m\u001b[1;33m}\u001b[0m\u001b[1;33m\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\AppData\\Roaming\\Python\\Python312\\site-packages\\QuantLib\\QuantLib.py\u001b[0m in \u001b[0;36m?\u001b[1;34m(self, *args)\u001b[0m\n\u001b[0;32m   8678\u001b[0m         r\"\"\"\n\u001b[0;32m   8679\u001b[0m         \u001b[0mdiscount\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mYieldTermStructure\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mDate\u001b[0m \u001b[0marg2\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mbool\u001b[0m \u001b[0mextrapolate\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mDiscountFactor\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   8680\u001b[0m         \u001b[0mdiscount\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mYieldTermStructure\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mTime\u001b[0m \u001b[0marg2\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mbool\u001b[0m \u001b[0mextrapolate\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;32mFalse\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m->\u001b[0m \u001b[0mDiscountFactor\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   8681\u001b[0m         \"\"\"\n\u001b[1;32m-> 8682\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0m_QuantLib\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mYieldTermStructure_discount\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m*\u001b[0m\u001b[0margs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m: 1st iteration: failed at 1st alive instrument, pillar January 6th, 2026, maturity January 6th, 2026, reference date January 1st, 2022: 2nd leg: negative time (-0.490411) given"]}], "source": ["import QuantLib as ql\n", "\n", "# Function to build a curve on a given date using sample deposit and swap rates\n", "def build_curve(evaluation_date, deposit_rates, swap_rates):\n", "    ql.Settings.instance().evaluationDate = evaluation_date\n", "    \n", "    # Define deposit helpers (short-term instruments)\n", "    deposit_helpers = [\n", "        ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ql.<PERSON>uo<PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(rate / 100)),\n", "                             ql.Period(1, ql.Months), 2,\n", "                             ql.TARGE<PERSON>(), ql.<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ql.Actual360())\n", "        for rate in deposit_rates\n", "    ]\n", "\n", "    # Define swap helpers (long-term instruments)\n", "    swap_helpers = [\n", "        ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(ql.<PERSON>uo<PERSON><PERSON><PERSON><PERSON>(ql.SimpleQuote(rate / 100)),\n", "                          ql.Period(tenor, ql.Years), ql.TARGET(),\n", "                          ql.Annual, ql.ModifiedFollowing,\n", "                          ql.Actual360(), ql.Euribor6M())\n", "        for rate, tenor in swap_rates\n", "    ]\n", "\n", "    # <PERSON><PERSON><PERSON> the helpers\n", "    rate_helpers = deposit_helpers + swap_helpers\n", "\n", "    # Build the curve\n", "    curve = ql.PiecewiseLogCubicDiscount(evaluation_date, rate_helpers, ql.Actual365Fixed())\n", "    curve.enableExtrapolation()\n", "    return curve\n", "\n", "# Set up some sample historical dates\n", "historical_dates = [ql.Date(1, ql.January, 2020), ql.Date(1, ql.January, 2021), ql.Date(1, ql.January, 2022)]\n", "\n", "# Set up sample market data for deposits and swaps for each historical date\n", "# [deposit rate for 1 month]\n", "deposit_rates_list = [\n", "    [0.05],  # for 2020\n", "    [0.03],  # for 2021\n", "    [0.01]   # for 2022\n", "]\n", "\n", "# [swap rate, tenor (in years)]\n", "swap_rates_list = [\n", "    [(0.75, 5), (1.00, 10)],  # for 2020\n", "    [(0.50, 5), (0.75, 10)],  # for 2021\n", "    [(0.25, 5), (0.50, 10)]   # for 2022\n", "]\n", "\n", "# Dictionary to store the calibrated curves for each historical date\n", "historical_curves = {}\n", "\n", "# Loop through each historical date and build the curve\n", "for i, historical_date in enumerate(historical_dates):\n", "    deposit_rates = deposit_rates_list[i]\n", "    swap_rates = swap_rates_list[i]\n", "    curve = build_curve(historical_date, deposit_rates, swap_rates)\n", "    historical_curves[historical_date] = curve\n", "\n", "# Now you have a set of historical curves stored in 'historical_curves'\n", "# You can access them by date and use them to get discount factors, zero rates, etc.\n", "\n", "# Example: Access a specific historical curve and get the discount factor for a future date\n", "query_date = ql.Date(1, ql.January, 2023)\n", "for hist_date, curve in historical_curves.items():\n", "    discount_factor = curve.discount(query_date)\n", "    print(f\"Discount Factor on {query_date} from curve on {hist_date}: {discount_factor}\")\n", "\n", "ql.Settings.instance().evaluationDate = ql.Date(1,1,2021)\n", "for hist_date, curve in historical_curves.items():\n", "    discount_factor = curve.discount(query_date)\n", "    print(f\"Discount Factor on {query_date} from curve on {hist_date}: {discount_factor}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import math"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["18.68785343556698"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.blackF<PERSON>ula(-1, 131.11, 131.11, 9.325/100 * math.sqrt(3), math.exp(-(0.243-3.639)/100 * 3), 0) * 2"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.09773726502439972"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.blackF<PERSON>ula(-1, 131.12, 131.12, 8.631/100 * math.sqrt(2), math.exp(-(0.245-3.63)/100 * 3), 0) * 2 / 144.53"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["252"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.Settlement).businessDaysBetween(ql.Date(2,5,2025), ql.Date(6, 5, 2026))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["748"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.UnitedStates(ql.UnitedStates.Settlement).businessDaysBetween(ql.Date(2,5,2025), ql.Date(1, 5, 2028))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}