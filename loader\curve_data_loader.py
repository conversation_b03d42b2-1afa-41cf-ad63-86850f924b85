import pandas as pd
import datetime as dt
import os
import warnings
import QuantLib as ql
from functools import lru_cache
from bisect import bisect_right
from .config import load_yaml_config
from xbbg import blp

CurveBenchmarkDataFolderName = "curve_benchmark_data"


class curveBenchmarkLoader:
    def __init__(self, name: str, **params):
        self.curve_name = name.upper().replace(".", "_")
        self.calendar = curveBenchmarkLoader.get_calendar(self.curve_name[:3])
        self.debug_mode = params.get("debug_mode", False)

    def _print(self, *msg):
        if self.debug_mode:
            print(*msg)

    @staticmethod
    @lru_cache
    def __benchmark_file_path(curve_name: str, effective_date: dt.date):
        return os.path.join(
            os.path.dirname(__file__),
            CurveBenchmarkDataFolderName,
            f"{curve_name}_{effective_date.strftime('%Y%m%d')}_marketclose.parquet",
        )

    @staticmethod
    @lru_cache
    def __custom_file_path(curve_name: str, data_source: str):
        return os.path.join(
            os.path.dirname(__file__),
            data_source.lower() + "_curve_benchmark_data",
            f"{curve_name}.csv",
        )

    @staticmethod
    @lru_cache(maxsize=1)
    def get_calendar(ccy: str) -> ql.Calendar:
        calendar = {
            "USD": ql.UnitedStates(ql.UnitedStates.FederalReserve),
            "GBP": ql.UnitedKingdom(),
            "EUR": ql.TARGET(),
            "CAD": ql.Canada(),
            "JPY": ql.Japan(),
        }
        return calendar.get(ccy, None)

    @staticmethod
    @lru_cache
    def __get_all_dates_and_benchmarks(curve_name: str):
        benchmarks_data = load_yaml_config(curve_name, "curve_benchmark_config")
        benchmarks_history = sorted(
            benchmarks_data["curves"], key=lambda x: x["effective_date"]
        )
        benchmarks_history = [
            {
                "effective_date": benchmark["effective_date"],
                "benchmarks": pd.DataFrame(benchmark["instruments"])
                .explode(["maturities", "ticker"])
                .reset_index(drop=True),
            }
            for benchmark in benchmarks_history
        ]
        effective_dates = [
            benchmarks["effective_date"] for benchmarks in benchmarks_history
        ]

        return effective_dates, benchmarks_history

    def get_benchmarks(self, date: dt.date):
        effective_dates, benchmarks_history = (
            curveBenchmarkLoader.__get_all_dates_and_benchmarks(self.curve_name)
        )
        index = bisect_right(effective_dates, date) - 1
        if index >= 0 and effective_dates[index] <= date:
            return benchmarks_history[index]
        warnings.warn(
            f"{self.curve_name}: No benchmark defined before on start date: {date}"
        )
        return None

    @staticmethod
    def __get_benchmarks_for_period(
        curve_name: str, start_date: dt.date, end_date: dt.date
    ):
        effective_dates, benchmarks_history = (
            curveBenchmarkLoader.__get_all_dates_and_benchmarks(curve_name)
        )
        if start_date < effective_dates[0]:
            warnings.warn(
                f"{curve_name}: No benchmark defined before {effective_dates[0]}, overriding start date {start_date} with {effective_dates[0]}"
            )
            start_date = effective_dates[0]
            if end_date < effective_dates[0]:
                raise ValueError(
                    f"{curve_name}: No benchmark defined before {effective_dates[0]}, but you queried a period with end date {end_date}"
                )
        end_date = max(start_date, end_date)

        start_index = bisect_right(effective_dates, start_date) - 1
        right_index = bisect_right(effective_dates, end_date)
        return benchmarks_history[start_index:right_index]

    def get_benchmarks_for_period(self, start_date: dt.date, end_date: dt.date):
        return curveBenchmarkLoader.__get_benchmarks_for_period(
            self.curve_name, start_date, end_date
        )

    @staticmethod
    def _load_close_px_for_period(
        curve_name: str, start_date: dt.date, end_date: dt.date, **params
    ):
        check_missing_tickers = params.get("check_missing_tickers", True)
        end_date = min(end_date, dt.date.today() - dt.timedelta(days=1))
        period_benchmarks = curveBenchmarkLoader.__get_benchmarks_for_period(
            curve_name, start_date, end_date
        ).copy()
        print(f"Loading close price for period [{start_date}, {end_date}]")
        period_bbg_bar_data = []
        for i, period in enumerate(period_benchmarks):
            effective_date = period["effective_date"]
            bbg_tickers = period["benchmarks"]["ticker"].tolist()
            if i < len(period_benchmarks) - 1:
                period_end_date = min(
                    end_date,
                    period_benchmarks[i + 1]["effective_date"] - dt.timedelta(days=1),
                )
            else:
                period_end_date = end_date
            print(f"Loading close price from {effective_date} to {period_end_date}")

            file = curveBenchmarkLoader.__benchmark_file_path(
                curve_name, effective_date
            )
            to_save, bbg_bar_data = False, None
            if os.path.exists(file):
                print("Opening curve benchmark data from bbg:", file)
                bbg_bar_data = pd.read_parquet(file)
                next_day = (
                    curveBenchmarkLoader.get_calendar(curve_name[:3])
                    .advance(ql.Date.from_date(bbg_bar_data.index[-1]), 1, ql.Days)
                    .to_date()
                )
                if i == len(period_benchmarks) - 1 and next_day <= period_end_date:
                    print(
                        f"Some curve benchmark history missing, read data from bbg from {next_day}"
                    )
                    missing_time_data = blp.bdh(
                        tickers=bbg_bar_data.columns,
                        flds=["PX_LAST"],
                        start_date=str(next_day),
                        end_date=str(period_end_date),
                    )
                    if not missing_time_data.empty:
                        missing_time_data.columns = missing_time_data.columns.droplevel(
                            1
                        )
                        bbg_bar_data = pd.concat(
                            [bbg_bar_data, missing_time_data], axis=0
                        ).sort_index()
                        to_save = True
                if check_missing_tickers:
                    missing_tickers = list(set(bbg_tickers) - set(bbg_bar_data.columns))
                    if len(missing_tickers) > 0:
                        missing_tickers.sort()
                        print(
                            f"Some curve benchmark tickers missing, read data from bbg: {missing_tickers}"
                        )
                        missing_tickers_data = blp.bdh(
                            tickers=missing_tickers,
                            flds=["PX_LAST"],
                            start_date=str(effective_date),
                            # The below is necessary
                            end_date=str(max(period_end_date, bbg_bar_data.index[-1])),
                        )
                        missing_tickers_data.columns = (
                            missing_tickers_data.columns.droplevel(1)
                        )
                        if not missing_tickers_data.empty:
                            bbg_bar_data = pd.concat(
                                [bbg_bar_data, missing_tickers_data], axis=1
                            ).sort_index()
                            to_save = True

            else:
                bbg_bar_data = blp.bdh(
                    tickers=bbg_tickers,
                    flds=["PX_LAST"],
                    start_date=str(effective_date),
                    end_date=str(period_end_date),
                )
                if not bbg_bar_data.empty:
                    print(
                        f"Whole curve benchmark history missing, read data from bbg from {effective_date} to {period_end_date}"
                    )
                    bbg_bar_data.columns = bbg_bar_data.columns.droplevel(1)
                    to_save = True

            if to_save:
                # Yesterday's fixing is only available from sometime today (next business day).
                # For example, 2024-12-11 fixing is usually published on 2024-12-12 at 8AM CET for ESTR, 8AM ET for SOFR (and T+0).
                # For euribor, 2024-12-11 fixing is usually published on 2024-12-11 at 11AM CET (and T+2).
                if (dt.date.today() - end_date).days <= 3 and bbg_bar_data.iloc[
                    -1
                ].isna().any():  # if any fixings or tickers are missing
                    print(
                        f"Storing new curve benchmark market data from bbg, to {bbg_bar_data.index[-2]}:",
                        file,
                    )
                    bbg_bar_data.iloc[:-1].to_parquet(file)
                else:
                    print(
                        f"Storing new curve benchmark market data from bbg, to {bbg_bar_data.index[-1]}:",
                        file,
                    )
                    bbg_bar_data.to_parquet(file)

            period_bbg_bar_data.append(bbg_bar_data[bbg_tickers])

        return period_benchmarks, period_bbg_bar_data

    def load_close_px_for_period(
        self, start_date: dt.date, end_date: dt.date, **params
    ):
        return curveBenchmarkLoader._load_close_px_for_period(
            self.curve_name, start_date, end_date, **params
        )

    @staticmethod
    @lru_cache
    def _load_close_bbg_bar_data(curve_name: str, effective_date: dt.date):
        file = curveBenchmarkLoader.__benchmark_file_path(curve_name, effective_date)
        bbg_bar_data = None
        if os.path.exists(file):
            bbg_bar_data = pd.read_parquet(file)
        return bbg_bar_data

    @staticmethod
    @lru_cache
    def _load_custom_data(curve_name: str, data_source: str):
        # TODO: Enable other data sources
        file = curveBenchmarkLoader.__custom_file_path(curve_name, data_source)
        print(f"Opening {data_source} data:", file)
        benchmarks, data = None, None
        if os.path.exists(file):
            data = pd.read_csv(
                file, index_col=0, parse_dates=[0], date_format="%d-%b-%y"
            )
            data.index = data.index.date
            benchmarks = pd.DataFrame(data.columns, columns=["ticker"])
            benchmarks["instrument_type"] = "Swap"
            benchmarks["maturities"] = (
                benchmarks["ticker"]
                .str.extract(r"(\d+)\s*(D|M|Y)")
                .apply(lambda x: f"{x[0]}{x[1]}", axis=1)
            )

        return benchmarks, data

    def load_custom_px(self, date: dt.date, data_source: str):
        benchmarks, data = curveBenchmarkLoader._load_custom_data(
            self.curve_name, data_source
        )
        if data is None:
            raise ValueError(f"No {data_source} data loaded for {self.curve_name}")
        return benchmarks, data.loc[date].dropna()

    def load_close_px(self, date: dt.date, fixing_publish_lag: int = 1):
        """
        Only use this method when calibrating curves.
        The method overrides fixings data from previous dates.
        """
        benchmarks = self.get_benchmarks(date)
        bbg_bar_data = curveBenchmarkLoader._load_close_bbg_bar_data(
            self.curve_name, benchmarks["effective_date"]
        )
        bbg_tickers = benchmarks["benchmarks"]["ticker"]  # don't change this
        if (
            bbg_bar_data is None
            or bbg_bar_data.index[-1] < date
            or not set(bbg_tickers).issubset(
                set(bbg_bar_data.columns)
                # To make sure no missing tickers when benchmark config is changed, can be removed if config won't be changed
            )
        ):
            _, period_bbg_bar_data = self.load_close_px_for_period(date, date)
            bbg_bar_data = period_bbg_bar_data[0]

        # NB: Please make sure NEVER write to the lru-cached bbg_bar_data, otherwise it will be overridden!
        close_date_bbg_bar_data = bbg_bar_data.loc[date]  # .copy()

        # Special handling for index fixings, since overnight fixings are published T+1 (what about ibor fixings?)
        if fixing_publish_lag >= 1:
            index_tickers = bbg_tickers[bbg_tickers.str.endswith("Index")].tolist()
            close_date_bbg_bar_data = close_date_bbg_bar_data.reindex(
                close_date_bbg_bar_data.index.union(index_tickers, sort=False),
                copy=True,
            )
            if index_tickers:
                prev_date = self.calendar.advance(
                    ql.Date.from_date(date), -fixing_publish_lag, ql.Days
                ).to_date()
                self._print(
                    f"NB: Loading index fixing on {prev_date}, lag: {fixing_publish_lag}d"
                )
                if prev_date in bbg_bar_data.index:
                    prev_bbg_bar_data = bbg_bar_data.loc[prev_date, index_tickers]
                else:
                    _, prev_bbg_bar_data = self.load_close_px(prev_date, 0)
                close_date_bbg_bar_data.update(prev_bbg_bar_data[index_tickers])
                # NB: Update and add new values
        return benchmarks["benchmarks"], close_date_bbg_bar_data

    def load_live_px(self, date: dt.date, fixing_publish_lag: int = 1):
        """
        Only use this method when calibrating curves.
        The method overrides fixings data from previous dates.
        """
        benchmarks_meta_data = self.get_benchmarks(date)["benchmarks"]
        bbg_tickers = benchmarks_meta_data["ticker"]  # don't change this
        self._print(f"Read live data from bbg...")
        live_bbg_bar_data = blp.bdp(tickers=bbg_tickers.tolist(), flds=["PX_MID"])
        self._print(f"Live data fetched from bbg.")
        if live_bbg_bar_data.empty:
            raise ValueError("No live data loaded from bbg!!!")
        live_bbg_bar_data = live_bbg_bar_data.T.squeeze()
        live_bbg_bar_data.name = date

        if fixing_publish_lag >= 1:
            index_tickers = bbg_tickers[bbg_tickers.str.endswith("Index")].tolist()
            live_bbg_bar_data = live_bbg_bar_data.reindex(
                live_bbg_bar_data.index.union(index_tickers, sort=False), copy=True
            )
            if index_tickers:
                prev_date = self.calendar.advance(
                    ql.Date.from_date(date), -fixing_publish_lag, ql.Days
                ).to_date()
                self._print(f"NB Live: Loading index fixing on {prev_date}")
                _, prev_bbg_bar_data = self.load_close_px(prev_date, 0)
                live_bbg_bar_data.update(prev_bbg_bar_data[index_tickers])
                # pd.Series can't add missing values, use reindex
        return benchmarks_meta_data, live_bbg_bar_data


if __name__ == "__main__":
    loader = curveBenchmarkLoader("EUR.EURIBOR.3M")
    benchmark_meta_data, bbg_bar_data = loader.load_live_px(dt.date(2024, 12, 12))
