import datetime as dt
import pandas as pd
import QuantLib as ql
from curves.config import available_curves
from loader.curve_calibrator import curveCalibrator
from helpers.date_helpers import strp_tenor
from typing import List


class forwardRatePricer:
    def __init__(self, name: str) -> None:
        name = name.replace("_", ".")
        if name not in available_curves:
            raise ValueError(
                f"Curve name {name} is not valid. Available curves: {list(available_curves.keys())}"
            )
        self.curve_name = name.replace(".", "_")
        self.curve = available_curves[name]("20000101")

    def calculate(
        self,
        curve_data: pd.Series,
        start_tenors: List[str] | None,
        end_tenors: List[str] | None,
    ):
        flat_pillar_size = int(curve_data.get("flat_pillar_size", 0))
        dates = [ql.Date.from_date(date) for date in curve_data["pillar_dates"]]
        curve = ql.LogMixedLinearCubicDiscountCurve(
            dates,
            curve_data["discount_factors"],
            self.curve.index.dayCounter(),
            self.curve.fixed_calendar,
            ql.LogMixedLinearCubic(
                n=flat_pillar_size, behavior=ql.MixedInterpolation.SplitRanges
            ),
        )
        start_date = ql.Date.from_date(curve_data.name)
        forward_rates = []
        for start_tenor, end_tenor in zip(start_tenors, end_tenors):
            if start_tenor:
                start_date = self.curve.fixed_calendar.advance(
                    start_date, ql.Period(start_tenor)
                )
            end_date = self.curve.fixed_calendar.advance(
                start_date, ql.Period(end_tenor)
            )
            forward_rate = curve.forwardRate(
                start_date, end_date, self.curve.fixed_day_counter, ql.Simple
            )
            forward_rates.append(forward_rate.rate())
        return forward_rates

    def price(
        self, start_date: dt.date, end_date: dt.date, tenors: str | List[str]
    ) -> pd.DataFrame:
        curve_calibrator = curveCalibrator(self.curve_name)
        period_calibration_results = curve_calibrator.load(start_date, end_date)
        if not isinstance(tenors, list):
            tenors = [tenors]
        start_tenors, end_tenors, _ = strp_tenor(tenors)
        period_forward_rates = []
        for calibration_results in period_calibration_results:
            forward_rates = calibration_results.apply(
                lambda calibration_data: self.calculate(
                    calibration_data, start_tenors, end_tenors
                ),
                axis=1,
                result_type="expand",
            )
            forward_rates.columns = [self.curve_name + ": " + tenor for tenor in tenors]
            period_forward_rates.append(forward_rates)
        return pd.concat(period_forward_rates)
