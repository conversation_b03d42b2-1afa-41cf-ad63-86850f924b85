import math
import QuantLib as ql
import pandas as pd
import sqlite3
import numpy as np
from xbbg import blp
import datetime
from datetime import datetime as dt
import matplotlib.pyplot as plt
import xlwings as xw

def main():
    wb = xw.Book.caller()
    wb.api.RefreshAll()

def live_curve():

    wb = xw.Book('irs2.xlsm')
    metadata = wb.sheets['metadata']
    ois_list = metadata['ois_list'].value
    activesheet1 = wb.sheets.active
    curve_list = activesheet1['curvelist'].options(ndim=1).value   #### forces single item to be  single list item
    conn = sqlite3.connect('H:\Python\SwapRV\SQL_data\swap_data.db', detect_types=sqlite3.PARSE_DECLTYPES|sqlite3.PARSE_COLNAMES)
    cur = conn.cursor()
    today = np.datetime64('today')
    today_sql = np.datetime_as_string(today, unit='D')
    lbd = np.busday_offset(today, -1, roll='backward')

    ### step1 Delete previous live data
    for curve in curve_list:
        
        tail_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15, 20, 30]
        for tail in tail_list:
            d1 = cur.execute(f'SELECT MAX(date) FROM {curve}_{tail}y')
            d1 = cur.fetchone()
            d1 = np.datetime64(dt.strptime(d1[0], '%Y-%m-%d').date())
            if d1 == today:
                deltext = f'DELETE FROM {curve}_{tail}y WHERE date IN (SELECT date FROM {curve}_{tail}y ORDER BY date DESC LIMIT 1)'
                cur.execute(deltext)    
        conn.commit()


    #### create tuples to build Quantlib curve
    for curve in curve_list:
        if curve in ois_list:
            tenorlist = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 15, 20, 30]
            fwds = np.arange(0, 421, 3).tolist()   ###create list 0-35yrs quarterly
            fwds_create = np.arange(3, 421, 3).tolist()                                 ### create text for SQL
            fwds_create = list(map(lambda x: 'fwd'+str(int(x))+'m REAL', fwds_create))
            fwds_create = (', '.join(fwds_create))
            fwds_insert = np.arange(3, 421, 3).tolist()                                 ### insert text for SQL
            fwds_insert = list(map(lambda x: 'fwd'+str(int(x))+'m', fwds_insert))
            fwds_insert = (', '.join(fwds_insert))
            qlist = np.arange(0,142,1).tolist()                                         ### create list of 142 ?,?,?.... 
            qlist = list(map(lambda x: '?', qlist))
            qlist = (','.join(qlist))
            dfon2 = pd.DataFrame()
            dfw2 = pd.DataFrame()
            dfm2 = pd.DataFrame()
            dfy2 = pd.DataFrame()
            activesheet = wb.sheets[curve]
            ### Get ON ois - NB assume o/n = t/n = s/n
            dlist = activesheet['daily_list'].value
            dlist_cols = activesheet['dlist_cols'].value
            dfon1 = pd.DataFrame(blp.bdp(dlist, 'px_last'))
            dfon1 = pd.concat([dfon1]*3).reset_index(drop=True)
            dfon2['D'] = dlist_cols
            dfon2['D'] = dfon2['D'].astype('int')
            dfon1['D'] = dfon2['D']
            l1 = list(dfon1.itertuples(index=False, name=None))    ##### list format for QuantLib

            ### Get OIS 1-3 weeks
            wlist = activesheet['weekly_list'].value
            wlist_cols = activesheet['wlist_cols'].value
            dfw = pd.DataFrame(blp.bdp(wlist, 'px_mid'))
            dfw = dfw.reset_index(drop=True)
            dfw2['W'] = wlist_cols
            dfw2['W'] = dfw2['W'].astype('int')
            dfw['W'] = dfw2['W']
            l2 = list(dfw.itertuples(index=False, name=None))    ##### list format for QuantLib

            ### Get OIS monthly data up to 21m
            mlist = activesheet['monthly_list'].value
            mlist_cols = activesheet['mlist_cols'].value
            dfm = pd.DataFrame(blp.bdp(mlist, 'px_mid')).reindex(mlist)  #### correct bbg sending data alphbetically
            dfm = dfm.reset_index(drop=True)
            dfm2['M'] = mlist_cols
            dfm2['M'] = dfm2['M'].astype('int')
            dfm['M'] = dfm2['M']
            l3 = list(dfm.itertuples(index=False, name=None))    ##### list format for QuantLib

            ### Get OIS annual data
            ylist = activesheet['yearly_list'].value
            ylist_cols = activesheet['ylist_cols'].value
            dfy = pd.DataFrame(blp.bdp(ylist, 'px_mid')).reindex(ylist)
            dfy = dfy.reset_index(drop=True)
            dfy2['Y'] = ylist_cols
            dfy2['Y'] = dfy2['Y'].astype('int')
            dfy['Y'] = dfy2['Y']
            l4 = list(dfy.itertuples(index=False, name=None))    ##### list format for QuantLib

            ### Bootstrap OIS curve
            today = ql.Date.todaysDate()
            ql.Settings.instance().evaluationDate = today
            #global yts
            yts = ql.RelinkableYieldTermStructureHandle()
            ### OverNight 1-3days
            helpers = [
                ql.DepositRateHelper(ql.QuoteHandle(ql.SimpleQuote(rate/100)),
                                    ql.Period(1,ql.Days), fixingDays,
                                    ql.TARGET(), ql.Following,
                                    False, ql.Actual360())
                for rate, fixingDays in l1
            ]

            if curve == 'estr':
                ois_handle = ql.Estr()
                dcount_handle = ql.Actual360()
            elif curve == 'sonia':
                ois_handle = ql.Sonia()
                dcount_handle = ql.Actual365Fixed()
            elif curve == 'sofr':
                ois_handle = ql.Sofr()
                dcount_handle = ql.Actual360()
            elif curve == 'corra':
                ois_handle = ql.Sonia()
                dcount_handle = ql.Actual365Fixed()
            elif curve == 'tona':
                ois_handle = ql.Sonia()
                dcount_handle = ql.Actual365Fixed()
            if curve == 'saron':
                ois_handle = ql.Estr()
                dcount_handle = ql.Actual360()
            ###  weeklies
            helpers += [
                ql.OISRateHelper(2, ql.Period(tenor, ql.Weeks),
                                ql.QuoteHandle(ql.SimpleQuote(rate/100)), ois_handle)
                for rate, tenor in l2
            ]
            ###  monthlies
            helpers += [
                ql.OISRateHelper(2, ql.Period(tenor, ql.Months),
                                ql.QuoteHandle(ql.SimpleQuote(rate/100)), ois_handle)
                for rate, tenor in l3
            ]
            ### yearlies
            helpers += [
                ql.OISRateHelper(2, ql.Period(tenor, ql.Years),
                                ql.QuoteHandle(ql.SimpleQuote(rate/100)), ois_handle)
                for rate, tenor in l4
            ]
            curve_c = ql.PiecewiseLogCubicDiscount(0, ql.TARGET(),
                                                        helpers, dcount_handle)
            curve_c.enableExtrapolation()
            #discount_curve = ql.YieldTermStructureHandle(estr_curve_c)

            yts.linkTo(curve_c)

            swapEngine = ql.DiscountingSwapEngine(yts)

            fwds = np.arange(0, 421, 3).tolist()   ###create list 0-35yrs quarterly

            for tenor in tenorlist:
                ratelist = [today_sql]
                createtext = f'CREATE TABLE IF NOT EXISTS {curve}_{tenor}Y (date REAL PRIMARY KEY, spot REAL, {fwds_create})'
                inserttext = f'INSERT INTO {curve}_{tenor}Y (date, spot, {fwds_insert}) VALUES ({qlist})'
                cur.execute(createtext)   #### create table if does not aleady exist
                for fwd in fwds:
                    if curve == 'estr':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Estr(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    elif curve == 'sonia':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Sonia(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    elif curve == 'sofr':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Sofr(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    elif curve == 'corra':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Sonia(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    elif curve == 'tona':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Sonia(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    if curve == 'saron':
                        ir_swap = ql.MakeOIS(swapTenor=ql.Period(tenor, ql.Years), fwdStart=ql.Period(fwd, ql.Months),\
                                            overnightIndex = ql.Estr(yts), pricingEngine=swapEngine, fixedRate=2.0)
                    ratelist.append(round(ir_swap.fairRate()*100,4))
                cur.execute(inserttext, ratelist)
                conn.commit()
            activesheet1["J1"].value = f'Done {curve} {dt.now().strftime("%H-%M-%S")}'   #### Show progress 
    
        else:
            active_sheet = wb.sheets[curve]
            tail_list = active_sheet[f'{curve}_tails'].value    
            fwds_insert = active_sheet['fwds_list'].value    
            fwds_insert = list(map(lambda x: 'fwd'+str(int(x[:-1])*12)+'m' if x[-1]=='Y' else 'fwd'+str(int(x[:-1]))+'m' if x[-1]=='M' els
            qlist = np.arange(0,len(fwds_insert)+1,1).tolist()                                         ### create list of ?,?,?.... 
            qlist = list(map(lambda x: '?', qlist))
            fwds_create = list(map(lambda x: x+' REAL', fwds_insert))
            fwds_insert = (','.join(fwds_insert))
            qlist = (','.join(qlist))
            fwds_create = (','.join(fwds_create))                        

            for tail in tail_list:  #### Loop through tails
                
                create_struct = f'CREATE TABLE IF NOT EXISTS {tail} (date REAL PRIMARY KEY, {fwds_create})'
                insert_struct = f'INSERT INTO {tail} (date, {fwds_insert}) VALUES ({qlist})'
                instr_list = active_sheet[tail].value            
                df1 = pd.DataFrame(blp.bdp(instr_list, ['px_last']))
                df1 = df1.T
                df1 = df1[instr_list]
                df1['date'] = today_sql
                df1 = df1.set_index('date').reset_index()
                cur.execute(f'{create_struct}')   #### create table if does not aleady exist
                cur.execute(f'{insert_struct}', df1[0:].values.flatten().tolist())
                conn.commit()
                activesheet1["J1"].value = f'Done {tail}'   #### Show progress
            
    
    
    conn.close()


if __name__ == "__main__":
    xw.Book("irs2.xlsm").set_mock_caller()
    main()