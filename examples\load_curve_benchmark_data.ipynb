{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "from loader.config import load_yaml_config\n", "from loader.curve_data_loader import curveBenchmarkLoader\n", "import pandas as pd\n", "import datetime as dt"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["benchmarks_data = load_yaml_config(\n", "            \"USD_SOFR\", \"curve_benchmark_config\"\n", "        )"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["loader = curveBenchmarkLoader(\"USD_SOFR\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["bh = loader.get_benchmarks_for_period(dt.date(2023,12,31), dt.date(2023,12,31))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'effective_date': datetime.date(2020, 12, 17),\n", "  'benchmarks':        instrument_type maturities           ticker\n", "  0   OvernightIndexSwap         1W  USOSFR1Z Curncy\n", "  1   OvernightIndexSwap         2W  USOSFR2Z Curncy\n", "  2   OvernightIndexSwap         3W  USOSFR3Z Curncy\n", "  3   OvernightIndexSwap         1M   USOSFRA Curncy\n", "  4   OvernightIndexSwap         2M   USOSFRB Curncy\n", "  5   OvernightIndexSwap         3M   USOSFRC Curncy\n", "  6   OvernightIndexSwap        18M  USOSFR1F Curncy\n", "  7   OvernightIndexSwap         2Y   USOSFR2 Curncy\n", "  8   OvernightIndexSwap         3Y   USOSFR3 Curncy\n", "  9   OvernightIndexSwap         4Y   USOSFR4 Curncy\n", "  10  OvernightIndexSwap         5Y   USOSFR5 Curncy\n", "  11  OvernightIndexSwap         6Y   USOSFR6 Curncy\n", "  12  OvernightIndexSwap         7Y   USOSFR7 Curncy\n", "  13  OvernightIndexSwap         8Y   USOSFR8 Curncy\n", "  14  OvernightIndexSwap         9Y   USOSFR9 Curncy\n", "  15  OvernightIndexSwap        10Y  USOSFR10 Curncy\n", "  16  OvernightIndexSwap        11Y  USOSFR11 Curncy\n", "  17  OvernightIndexSwap        12Y  USOSFR12 Curncy\n", "  18  OvernightIndexSwap        15Y  USOSFR15 Curncy\n", "  19  OvernightIndexSwap        20Y  USOSFR20 Curncy\n", "  20  OvernightIndexSwap        25Y  USOSFR25 Curncy\n", "  21  OvernightIndexSwap        30Y  USOSFR30 Curncy\n", "  22  OvernightIndexSwap        40Y  USOSFR40 Curncy\n", "  23  OvernightIndexSwap        50Y  USOSFR50 Curncy\n", "  24     MeetingDateSwap         s1   USSOSR1 Curncy\n", "  25     MeetingDateSwap         s2   USSOSR2 Curncy\n", "  26     MeetingDateSwap         s3   USSOSR3 Curncy\n", "  27     MeetingDateSwap         s4   USSOSR4 Curncy\n", "  28     MeetingDateSwap         s5   USSOSR5 Curncy\n", "  29     MeetingDateSwap         s6   USSOSR6 Curncy\n", "  30     MeetingDateSwap         s7   USSOSR7 Curncy}]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["bh"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "      <th>SOFRRATE Index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>4.8530</td>\n", "      <td>4.7745</td>\n", "      <td>4.7176</td>\n", "      <td>4.6926</td>\n", "      <td>4.6399</td>\n", "      <td>4.5933</td>\n", "      <td>4.0061</td>\n", "      <td>3.9244</td>\n", "      <td>3.8362</td>\n", "      <td>3.7901</td>\n", "      <td>...</td>\n", "      <td>3.5178</td>\n", "      <td>3.3375</td>\n", "      <td>4.6245</td>\n", "      <td>4.4800</td>\n", "      <td>4.290</td>\n", "      <td>4.1060</td>\n", "      <td>3.9670</td>\n", "      <td>3.8470</td>\n", "      <td>3.7800</td>\n", "      <td>4.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-29</th>\n", "      <td>4.8530</td>\n", "      <td>4.7490</td>\n", "      <td>4.7035</td>\n", "      <td>4.6830</td>\n", "      <td>4.6279</td>\n", "      <td>4.5759</td>\n", "      <td>3.9630</td>\n", "      <td>3.8781</td>\n", "      <td>3.7900</td>\n", "      <td>3.7455</td>\n", "      <td>...</td>\n", "      <td>3.4920</td>\n", "      <td>3.3115</td>\n", "      <td>4.6250</td>\n", "      <td>4.4595</td>\n", "      <td>4.262</td>\n", "      <td>4.0730</td>\n", "      <td>3.9290</td>\n", "      <td>3.8000</td>\n", "      <td>3.7220</td>\n", "      <td>4.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-30</th>\n", "      <td>4.8370</td>\n", "      <td>4.7291</td>\n", "      <td>4.6935</td>\n", "      <td>4.6711</td>\n", "      <td>4.6226</td>\n", "      <td>4.5718</td>\n", "      <td>4.0364</td>\n", "      <td>3.9624</td>\n", "      <td>3.8815</td>\n", "      <td>3.8310</td>\n", "      <td>...</td>\n", "      <td>3.4941</td>\n", "      <td>3.3140</td>\n", "      <td>4.6238</td>\n", "      <td>4.4740</td>\n", "      <td>4.299</td>\n", "      <td>4.1260</td>\n", "      <td>3.9900</td>\n", "      <td>3.8810</td>\n", "      <td>3.8165</td>\n", "      <td>4.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-31</th>\n", "      <td>4.7270</td>\n", "      <td>4.6808</td>\n", "      <td>4.6615</td>\n", "      <td>4.6517</td>\n", "      <td>4.6026</td>\n", "      <td>4.5554</td>\n", "      <td>4.0208</td>\n", "      <td>3.9458</td>\n", "      <td>3.8624</td>\n", "      <td>3.8166</td>\n", "      <td>...</td>\n", "      <td>3.4525</td>\n", "      <td>3.2725</td>\n", "      <td>4.6330</td>\n", "      <td>4.4590</td>\n", "      <td>4.290</td>\n", "      <td>4.1210</td>\n", "      <td>3.9915</td>\n", "      <td>3.8800</td>\n", "      <td>3.8120</td>\n", "      <td>4.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-01</th>\n", "      <td>4.7045</td>\n", "      <td>4.6584</td>\n", "      <td>4.6431</td>\n", "      <td>4.6374</td>\n", "      <td>4.5860</td>\n", "      <td>4.5360</td>\n", "      <td>4.0435</td>\n", "      <td>3.9785</td>\n", "      <td>3.9065</td>\n", "      <td>3.8672</td>\n", "      <td>...</td>\n", "      <td>3.5465</td>\n", "      <td>3.3656</td>\n", "      <td>4.6230</td>\n", "      <td>4.4450</td>\n", "      <td>4.287</td>\n", "      <td>4.1340</td>\n", "      <td>4.0155</td>\n", "      <td>3.9080</td>\n", "      <td>3.8400</td>\n", "      <td>4.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-04</th>\n", "      <td>4.6598</td>\n", "      <td>4.6340</td>\n", "      <td>4.6295</td>\n", "      <td>4.6285</td>\n", "      <td>4.5795</td>\n", "      <td>4.5273</td>\n", "      <td>4.0109</td>\n", "      <td>3.9400</td>\n", "      <td>3.8565</td>\n", "      <td>3.8055</td>\n", "      <td>...</td>\n", "      <td>3.4329</td>\n", "      <td>3.2495</td>\n", "      <td>4.6210</td>\n", "      <td>4.4390</td>\n", "      <td>4.277</td>\n", "      <td>4.1180</td>\n", "      <td>3.9880</td>\n", "      <td>3.8705</td>\n", "      <td>3.8000</td>\n", "      <td>4.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-05</th>\n", "      <td>4.6306</td>\n", "      <td>4.6172</td>\n", "      <td>4.6144</td>\n", "      <td>4.6173</td>\n", "      <td>4.5723</td>\n", "      <td>4.5248</td>\n", "      <td>4.0266</td>\n", "      <td>3.9549</td>\n", "      <td>3.8635</td>\n", "      <td>3.8110</td>\n", "      <td>...</td>\n", "      <td>3.3985</td>\n", "      <td>3.2129</td>\n", "      <td>4.6175</td>\n", "      <td>4.4540</td>\n", "      <td>4.280</td>\n", "      <td>4.1135</td>\n", "      <td>3.9880</td>\n", "      <td>3.8895</td>\n", "      <td>3.8290</td>\n", "      <td>4.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-06</th>\n", "      <td>4.5935</td>\n", "      <td>4.5965</td>\n", "      <td>4.5985</td>\n", "      <td>4.6044</td>\n", "      <td>4.5672</td>\n", "      <td>4.5245</td>\n", "      <td>4.1186</td>\n", "      <td>4.0621</td>\n", "      <td>3.9890</td>\n", "      <td>3.9483</td>\n", "      <td>...</td>\n", "      <td>3.5915</td>\n", "      <td>3.4055</td>\n", "      <td>4.6115</td>\n", "      <td>4.4675</td>\n", "      <td>4.338</td>\n", "      <td>4.1875</td>\n", "      <td>4.0800</td>\n", "      <td>3.9905</td>\n", "      <td>3.9300</td>\n", "      <td>4.81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-07</th>\n", "      <td>4.5913</td>\n", "      <td>4.5939</td>\n", "      <td>4.5974</td>\n", "      <td>4.6027</td>\n", "      <td>4.5523</td>\n", "      <td>4.5092</td>\n", "      <td>4.0608</td>\n", "      <td>3.9957</td>\n", "      <td>3.9125</td>\n", "      <td>3.8617</td>\n", "      <td>...</td>\n", "      <td>3.5290</td>\n", "      <td>3.3395</td>\n", "      <td>4.6071</td>\n", "      <td>4.4560</td>\n", "      <td>4.321</td>\n", "      <td>4.1570</td>\n", "      <td>4.0440</td>\n", "      <td>3.9410</td>\n", "      <td>3.8730</td>\n", "      <td>4.82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-11-08</th>\n", "      <td>4.5928</td>\n", "      <td>4.5948</td>\n", "      <td>4.5969</td>\n", "      <td>4.6020</td>\n", "      <td>4.5603</td>\n", "      <td>4.5225</td>\n", "      <td>4.1204</td>\n", "      <td>4.0525</td>\n", "      <td>3.9570</td>\n", "      <td>3.8969</td>\n", "      <td>...</td>\n", "      <td>3.4827</td>\n", "      <td>3.2934</td>\n", "      <td>4.4749</td>\n", "      <td>4.3600</td>\n", "      <td>4.219</td>\n", "      <td>4.1235</td>\n", "      <td>4.0250</td>\n", "      <td>3.9570</td>\n", "      <td>3.8970</td>\n", "      <td>4.60</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10 rows × 32 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2024-10-28           4.8530           4.7745           4.7176          4.6926   \n", "2024-10-29           4.8530           4.7490           4.7035          4.6830   \n", "2024-10-30           4.8370           4.7291           4.6935          4.6711   \n", "2024-10-31           4.7270           4.6808           4.6615          4.6517   \n", "2024-11-01           4.7045           4.6584           4.6431          4.6374   \n", "2024-11-04           4.6598           4.6340           4.6295          4.6285   \n", "2024-11-05           4.6306           4.6172           4.6144          4.6173   \n", "2024-11-06           4.5935           4.5965           4.5985          4.6044   \n", "2024-11-07           4.5913           4.5939           4.5974          4.6027   \n", "2024-11-08           4.5928           4.5948           4.5969          4.6020   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2024-10-28          4.6399          4.5933           4.0061          3.9244   \n", "2024-10-29          4.6279          4.5759           3.9630          3.8781   \n", "2024-10-30          4.6226          4.5718           4.0364          3.9624   \n", "2024-10-31          4.6026          4.5554           4.0208          3.9458   \n", "2024-11-01          4.5860          4.5360           4.0435          3.9785   \n", "2024-11-04          4.5795          4.5273           4.0109          3.9400   \n", "2024-11-05          4.5723          4.5248           4.0266          3.9549   \n", "2024-11-06          4.5672          4.5245           4.1186          4.0621   \n", "2024-11-07          4.5523          4.5092           4.0608          3.9957   \n", "2024-11-08          4.5603          4.5225           4.1204          4.0525   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR40 Curncy  \\\n", "2024-10-28          3.8362          3.7901  ...           3.5178   \n", "2024-10-29          3.7900          3.7455  ...           3.4920   \n", "2024-10-30          3.8815          3.8310  ...           3.4941   \n", "2024-10-31          3.8624          3.8166  ...           3.4525   \n", "2024-11-01          3.9065          3.8672  ...           3.5465   \n", "2024-11-04          3.8565          3.8055  ...           3.4329   \n", "2024-11-05          3.8635          3.8110  ...           3.3985   \n", "2024-11-06          3.9890          3.9483  ...           3.5915   \n", "2024-11-07          3.9125          3.8617  ...           3.5290   \n", "2024-11-08          3.9570          3.8969  ...           3.4827   \n", "\n", "            USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  USSOSR3 Curncy  \\\n", "2024-10-28           3.3375          4.6245          4.4800           4.290   \n", "2024-10-29           3.3115          4.6250          4.4595           4.262   \n", "2024-10-30           3.3140          4.6238          4.4740           4.299   \n", "2024-10-31           3.2725          4.6330          4.4590           4.290   \n", "2024-11-01           3.3656          4.6230          4.4450           4.287   \n", "2024-11-04           3.2495          4.6210          4.4390           4.277   \n", "2024-11-05           3.2129          4.6175          4.4540           4.280   \n", "2024-11-06           3.4055          4.6115          4.4675           4.338   \n", "2024-11-07           3.3395          4.6071          4.4560           4.321   \n", "2024-11-08           3.2934          4.4749          4.3600           4.219   \n", "\n", "            USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  USSOSR7 Curncy  \\\n", "2024-10-28          4.1060          3.9670          3.8470          3.7800   \n", "2024-10-29          4.0730          3.9290          3.8000          3.7220   \n", "2024-10-30          4.1260          3.9900          3.8810          3.8165   \n", "2024-10-31          4.1210          3.9915          3.8800          3.8120   \n", "2024-11-01          4.1340          4.0155          3.9080          3.8400   \n", "2024-11-04          4.1180          3.9880          3.8705          3.8000   \n", "2024-11-05          4.1135          3.9880          3.8895          3.8290   \n", "2024-11-06          4.1875          4.0800          3.9905          3.9300   \n", "2024-11-07          4.1570          4.0440          3.9410          3.8730   \n", "2024-11-08          4.1235          4.0250          3.9570          3.8970   \n", "\n", "            SOFRRATE Index  \n", "2024-10-28            4.82  \n", "2024-10-29            4.82  \n", "2024-10-30            4.81  \n", "2024-10-31            4.90  \n", "2024-11-01            4.86  \n", "2024-11-04            4.82  \n", "2024-11-05            4.82  \n", "2024-11-06            4.81  \n", "2024-11-07            4.82  \n", "2024-11-08            4.60  \n", "\n", "[10 rows x 32 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\")\n", "data.tail(10)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MUTKCALM Index</th>\n", "      <th>JYSO1Z Curncy</th>\n", "      <th>JYSO2Z Curncy</th>\n", "      <th>JYSO3Z Curncy</th>\n", "      <th>JYSOA Curncy</th>\n", "      <th>JYSO<PERSON></th>\n", "      <th>JYSOC Curncy</th>\n", "      <th>JYSO1F <PERSON></th>\n", "      <th>JYSO2 <PERSON>cy</th>\n", "      <th>JYSO3 C<PERSON>cy</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>JY<PERSON><PERSON>cy</th>\n", "      <th>JYSOMPM7 Curncy</th>\n", "      <th>JYSOMPM1 Curncy</th>\n", "      <th>JYSOMPM4 Curncy</th>\n", "      <th>JYSOMPM6 Curncy</th>\n", "      <th>JYSOMPM2 Curncy</th>\n", "      <th>JYSOMPM5 Curncy</th>\n", "      <th>JYSOMPM3 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>NaN</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058100</td>\n", "      <td>0.057500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>NaN</td>\n", "      <td>0.0600</td>\n", "      <td>0.06000</td>\n", "      <td>0.0600</td>\n", "      <td>0.05900</td>\n", "      <td>0.058100</td>\n", "      <td>0.056000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-05</th>\n", "      <td>0.070</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058100</td>\n", "      <td>0.056480</td>\n", "      <td>0.05300</td>\n", "      <td>0.05310</td>\n", "      <td>0.06250</td>\n", "      <td>...</td>\n", "      <td>1.01250</td>\n", "      <td>1.079999</td>\n", "      <td>1.201250</td>\n", "      <td>0.05000</td>\n", "      <td>0.06375</td>\n", "      <td>0.05000</td>\n", "      <td>0.05000</td>\n", "      <td>0.05500</td>\n", "      <td>0.05000</td>\n", "      <td>0.05000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-06</th>\n", "      <td>0.071</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058000</td>\n", "      <td>0.056000</td>\n", "      <td>0.05250</td>\n", "      <td>0.05300</td>\n", "      <td>0.06000</td>\n", "      <td>...</td>\n", "      <td>0.93500</td>\n", "      <td>0.997500</td>\n", "      <td>1.106250</td>\n", "      <td>0.05250</td>\n", "      <td>0.06250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05500</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-07</th>\n", "      <td>0.073</td>\n", "      <td>0.0625</td>\n", "      <td>0.06130</td>\n", "      <td>0.0600</td>\n", "      <td>0.06000</td>\n", "      <td>0.058000</td>\n", "      <td>0.056000</td>\n", "      <td>0.05300</td>\n", "      <td>0.05300</td>\n", "      <td>0.06500</td>\n", "      <td>...</td>\n", "      <td>0.91250</td>\n", "      <td>0.975000</td>\n", "      <td>1.090000</td>\n", "      <td>0.05250</td>\n", "      <td>0.05625</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05500</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>-0.011</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01250</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01100</td>\n", "      <td>-0.000043</td>\n", "      <td>0.010000</td>\n", "      <td>0.14500</td>\n", "      <td>0.21000</td>\n", "      <td>0.30000</td>\n", "      <td>...</td>\n", "      <td>1.38400</td>\n", "      <td>1.410000</td>\n", "      <td>1.396249</td>\n", "      <td>0.17500</td>\n", "      <td>0.01375</td>\n", "      <td>0.09125</td>\n", "      <td>0.14187</td>\n", "      <td>0.03750</td>\n", "      <td>0.11625</td>\n", "      <td>0.06250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>-0.011</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01300</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01200</td>\n", "      <td>0.001250</td>\n", "      <td>0.009000</td>\n", "      <td>0.14900</td>\n", "      <td>0.20501</td>\n", "      <td>0.29750</td>\n", "      <td>...</td>\n", "      <td>1.41124</td>\n", "      <td>1.436250</td>\n", "      <td>1.428750</td>\n", "      <td>0.17500</td>\n", "      <td>0.01312</td>\n", "      <td>0.09500</td>\n", "      <td>0.14750</td>\n", "      <td>0.03875</td>\n", "      <td>0.12250</td>\n", "      <td>0.06500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>-0.013</td>\n", "      <td>-0.0144</td>\n", "      <td>-0.01500</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01331</td>\n", "      <td>-0.009000</td>\n", "      <td>-0.004475</td>\n", "      <td>0.12500</td>\n", "      <td>0.17875</td>\n", "      <td>0.27125</td>\n", "      <td>...</td>\n", "      <td>1.38875</td>\n", "      <td>1.414999</td>\n", "      <td>1.407500</td>\n", "      <td>0.16000</td>\n", "      <td>-0.00375</td>\n", "      <td>0.08000</td>\n", "      <td>0.14000</td>\n", "      <td>0.02000</td>\n", "      <td>0.11500</td>\n", "      <td>0.04125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>-0.016</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01505</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01375</td>\n", "      <td>-0.010025</td>\n", "      <td>-0.006250</td>\n", "      <td>0.13000</td>\n", "      <td>0.18500</td>\n", "      <td>0.27250</td>\n", "      <td>...</td>\n", "      <td>1.40625</td>\n", "      <td>1.438750</td>\n", "      <td>1.428750</td>\n", "      <td>0.15500</td>\n", "      <td>-0.00125</td>\n", "      <td>0.07125</td>\n", "      <td>0.12750</td>\n", "      <td>0.01250</td>\n", "      <td>0.10500</td>\n", "      <td>0.03375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>-0.039</td>\n", "      <td>-0.0210</td>\n", "      <td>-0.01900</td>\n", "      <td>-0.0175</td>\n", "      <td>-0.01600</td>\n", "      <td>-0.015000</td>\n", "      <td>-0.008750</td>\n", "      <td>0.13257</td>\n", "      <td>0.19250</td>\n", "      <td>0.29000</td>\n", "      <td>...</td>\n", "      <td>1.44125</td>\n", "      <td>1.472500</td>\n", "      <td>1.468000</td>\n", "      <td>0.16375</td>\n", "      <td>-0.00828</td>\n", "      <td>0.07250</td>\n", "      <td>0.13797</td>\n", "      <td>0.01000</td>\n", "      <td>0.10734</td>\n", "      <td>0.04000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2347 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            MUTKCALM Index  JYSO1Z Curncy  JYSO2Z Curncy  JYSO3Z Curncy  \\\n", "2015-01-01             NaN         0.0650        0.06250         0.0613   \n", "2015-01-02             NaN         0.0600        0.06000         0.0600   \n", "2015-01-05           0.070         0.0650        0.06250         0.0613   \n", "2015-01-06           0.071         0.0650        0.06250         0.0613   \n", "2015-01-07           0.073         0.0625        0.06130         0.0600   \n", "...                    ...            ...            ...            ...   \n", "2023-12-25          -0.011        -0.0125       -0.01250        -0.0125   \n", "2023-12-26          -0.011        -0.0125       -0.01300        -0.0125   \n", "2023-12-27          -0.013        -0.0144       -0.01500        -0.0150   \n", "2023-12-28          -0.016        -0.0150       -0.01505        -0.0150   \n", "2023-12-29          -0.039        -0.0210       -0.01900        -0.0175   \n", "\n", "            JYSOA Curncy  JYSOB Curncy  JYSOC Curncy  JYSO1F <PERSON><PERSON><PERSON>  \\\n", "2015-01-01       0.05940      0.058100      0.057500            NaN   \n", "2015-01-02       0.05900      0.058100      0.056000            NaN   \n", "2015-01-05       0.05940      0.058100      0.056480        0.05300   \n", "2015-01-06       0.05940      0.058000      0.056000        0.05250   \n", "2015-01-07       0.06000      0.058000      0.056000        0.05300   \n", "...                  ...           ...           ...            ...   \n", "2023-12-25      -0.01100     -0.000043      0.010000        0.14500   \n", "2023-12-26      -0.01200      0.001250      0.009000        0.14900   \n", "2023-12-27      -0.01331     -0.009000     -0.004475        0.12500   \n", "2023-12-28      -0.01375     -0.010025     -0.006250        0.13000   \n", "2023-12-29      -0.01600     -0.015000     -0.008750        0.13257   \n", "\n", "            JYSO2 Curncy  JYSO3 Curncy  ...  JYSO25 Curncy  JYSO30 Curncy  \\\n", "2015-01-01           NaN           NaN  ...            NaN            NaN   \n", "2015-01-02           NaN           NaN  ...            NaN            NaN   \n", "2015-01-05       0.05310       0.06250  ...        1.01250       1.079999   \n", "2015-01-06       0.05300       0.06000  ...        0.93500       0.997500   \n", "2015-01-07       0.05300       0.06500  ...        0.91250       0.975000   \n", "...                  ...           ...  ...            ...            ...   \n", "2023-12-25       0.21000       0.30000  ...        1.38400       1.410000   \n", "2023-12-26       0.20501       0.29750  ...        1.41124       1.436250   \n", "2023-12-27       0.17875       0.27125  ...        1.38875       1.414999   \n", "2023-12-28       0.18500       0.27250  ...        1.40625       1.438750   \n", "2023-12-29       0.19250       0.29000  ...        1.44125       1.472500   \n", "\n", "            JYSO40 Curncy  JYSOMPM7 Curncy  JYSOMPM1 Curncy  JYSOMPM4 Curncy  \\\n", "2015-01-01       1.217500              NaN              NaN              NaN   \n", "2015-01-02       1.217500              NaN              NaN              NaN   \n", "2015-01-05       1.201250          0.05000          0.06375          0.05000   \n", "2015-01-06       1.106250          0.05250          0.06250          0.05250   \n", "2015-01-07       1.090000          0.05250          0.05625          0.05250   \n", "...                   ...              ...              ...              ...   \n", "2023-12-25       1.396249          0.17500          0.01375          0.09125   \n", "2023-12-26       1.428750          0.17500          0.01312          0.09500   \n", "2023-12-27       1.407500          0.16000         -0.00375          0.08000   \n", "2023-12-28       1.428750          0.15500         -0.00125          0.07125   \n", "2023-12-29       1.468000          0.16375         -0.00828          0.07250   \n", "\n", "            JYSOMPM6 Curncy  JYSOMPM2 Curncy  JYSOMPM5 Curncy  JYSOMPM3 Curncy  \n", "2015-01-01              NaN              NaN              NaN              NaN  \n", "2015-01-02              NaN              NaN              NaN              NaN  \n", "2015-01-05          0.05000          0.05500          0.05000          0.05000  \n", "2015-01-06          0.05250          0.05500          0.05250          0.05250  \n", "2015-01-07          0.05250          0.05500          0.05250          0.05250  \n", "...                     ...              ...              ...              ...  \n", "2023-12-25          0.14187          0.03750          0.11625          0.06250  \n", "2023-12-26          0.14750          0.03875          0.12250          0.06500  \n", "2023-12-27          0.14000          0.02000          0.11500          0.04125  \n", "2023-12-28          0.12750          0.01250          0.10500          0.03375  \n", "2023-12-29          0.13797          0.01000          0.10734          0.04000  \n", "\n", "[2347 rows x 31 columns]"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\JPY_TONAR_20150101_marketclose.parquet\")\n", "data"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MUTKCALM Index</th>\n", "      <th>JYSO1Z Curncy</th>\n", "      <th>JYSO2Z Curncy</th>\n", "      <th>JYSO3Z Curncy</th>\n", "      <th>JYSOA Curncy</th>\n", "      <th>JYSO<PERSON></th>\n", "      <th>JYSOC Curncy</th>\n", "      <th>JYSO1F <PERSON></th>\n", "      <th>JYSO2 <PERSON>cy</th>\n", "      <th>JYSO3 C<PERSON>cy</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>JY<PERSON><PERSON>cy</th>\n", "      <th>JYSOMPM1 Curncy</th>\n", "      <th>JYSOMPM2 Curncy</th>\n", "      <th>JYSOMPM3 Curncy</th>\n", "      <th>JYSOMPM4 Curncy</th>\n", "      <th>JYSOMPM5 Curncy</th>\n", "      <th>JYSOMPM6 Curncy</th>\n", "      <th>JYSOMPM7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-05-04</th>\n", "      <td>NaN</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05060</td>\n", "      <td>-0.0560</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06375</td>\n", "      <td>-0.10630</td>\n", "      <td>-0.11440</td>\n", "      <td>-0.12875</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.05200</td>\n", "      <td>-0.06500</td>\n", "      <td>-0.08200</td>\n", "      <td>-0.09200</td>\n", "      <td>-0.105</td>\n", "      <td>-0.112</td>\n", "      <td>-0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-05-05</th>\n", "      <td>NaN</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05060</td>\n", "      <td>-0.0513</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06375</td>\n", "      <td>-0.10630</td>\n", "      <td>-0.11875</td>\n", "      <td>-0.13130</td>\n", "      <td>...</td>\n", "      <td>0.15060</td>\n", "      <td>0.17750</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-05-06</th>\n", "      <td>NaN</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05060</td>\n", "      <td>-0.0513</td>\n", "      <td>-0.05190</td>\n", "      <td>-0.05880</td>\n", "      <td>-0.06500</td>\n", "      <td>NaN</td>\n", "      <td>-0.11875</td>\n", "      <td>-0.13130</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>-0.05200</td>\n", "      <td>-0.06500</td>\n", "      <td>-0.08200</td>\n", "      <td>-0.09200</td>\n", "      <td>-0.105</td>\n", "      <td>-0.112</td>\n", "      <td>-0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-05-19</th>\n", "      <td>-0.061</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.04625</td>\n", "      <td>-0.0500</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06190</td>\n", "      <td>-0.10125</td>\n", "      <td>-0.10750</td>\n", "      <td>-0.12000</td>\n", "      <td>...</td>\n", "      <td>0.18500</td>\n", "      <td>0.21500</td>\n", "      <td>0.221250</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.07500</td>\n", "      <td>-0.08688</td>\n", "      <td>-0.09750</td>\n", "      <td>NaN</td>\n", "      <td>-0.117</td>\n", "      <td>-0.127</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-02</th>\n", "      <td>-0.059</td>\n", "      <td>-0.05900</td>\n", "      <td>-0.06000</td>\n", "      <td>-0.0600</td>\n", "      <td>-0.06000</td>\n", "      <td>-0.06375</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.09625</td>\n", "      <td>-0.10310</td>\n", "      <td>-0.11250</td>\n", "      <td>...</td>\n", "      <td>0.22500</td>\n", "      <td>0.26060</td>\n", "      <td>0.267500</td>\n", "      <td>-0.06125</td>\n", "      <td>-0.07375</td>\n", "      <td>-0.08125</td>\n", "      <td>-0.08875</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-25</th>\n", "      <td>-0.042</td>\n", "      <td>-0.04625</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.0509</td>\n", "      <td>-0.05400</td>\n", "      <td>-0.05560</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.08190</td>\n", "      <td>-0.08875</td>\n", "      <td>-0.10190</td>\n", "      <td>...</td>\n", "      <td>0.20375</td>\n", "      <td>0.23625</td>\n", "      <td>0.241250</td>\n", "      <td>-0.06000</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-26</th>\n", "      <td>-0.038</td>\n", "      <td>-0.04625</td>\n", "      <td>-0.04880</td>\n", "      <td>-0.0500</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.08100</td>\n", "      <td>-0.08750</td>\n", "      <td>-0.10125</td>\n", "      <td>...</td>\n", "      <td>0.20750</td>\n", "      <td>0.24125</td>\n", "      <td>0.247500</td>\n", "      <td>-0.06000</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-29</th>\n", "      <td>-0.041</td>\n", "      <td>-0.04600</td>\n", "      <td>-0.04880</td>\n", "      <td>-0.0500</td>\n", "      <td>-0.05250</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.05900</td>\n", "      <td>-0.08100</td>\n", "      <td>-0.08750</td>\n", "      <td>-0.09875</td>\n", "      <td>...</td>\n", "      <td>0.21125</td>\n", "      <td>0.24375</td>\n", "      <td>0.250000</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-06-30</th>\n", "      <td>-0.068</td>\n", "      <td>-0.04600</td>\n", "      <td>-0.06250</td>\n", "      <td>-0.0530</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.06000</td>\n", "      <td>-0.06250</td>\n", "      <td>-0.08000</td>\n", "      <td>-0.08375</td>\n", "      <td>-0.09250</td>\n", "      <td>...</td>\n", "      <td>0.22750</td>\n", "      <td>0.26625</td>\n", "      <td>0.268800</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-01</th>\n", "      <td>-0.041</td>\n", "      <td>-0.04700</td>\n", "      <td>-0.05130</td>\n", "      <td>-0.0525</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.05900</td>\n", "      <td>-0.08125</td>\n", "      <td>-0.08500</td>\n", "      <td>-0.09500</td>\n", "      <td>...</td>\n", "      <td>0.26130</td>\n", "      <td>0.30000</td>\n", "      <td>0.307500</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-02</th>\n", "      <td>-0.039</td>\n", "      <td>-0.04625</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.0513</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.05900</td>\n", "      <td>-0.08000</td>\n", "      <td>-0.08560</td>\n", "      <td>-0.09600</td>\n", "      <td>...</td>\n", "      <td>0.26125</td>\n", "      <td>0.29875</td>\n", "      <td>0.312500</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06625</td>\n", "      <td>-0.07375</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-03</th>\n", "      <td>-0.034</td>\n", "      <td>-0.04625</td>\n", "      <td>-0.04875</td>\n", "      <td>-0.0513</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.08250</td>\n", "      <td>-0.08875</td>\n", "      <td>-0.10250</td>\n", "      <td>...</td>\n", "      <td>0.24000</td>\n", "      <td>0.27625</td>\n", "      <td>0.291250</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06625</td>\n", "      <td>-0.07375</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-06</th>\n", "      <td>-0.034</td>\n", "      <td>-0.04600</td>\n", "      <td>-0.04500</td>\n", "      <td>-0.0510</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05700</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.08060</td>\n", "      <td>-0.08625</td>\n", "      <td>-0.09700</td>\n", "      <td>...</td>\n", "      <td>0.24625</td>\n", "      <td>0.28250</td>\n", "      <td>0.296250</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06625</td>\n", "      <td>-0.07375</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-07</th>\n", "      <td>-0.027</td>\n", "      <td>-0.04500</td>\n", "      <td>-0.04750</td>\n", "      <td>-0.0509</td>\n", "      <td>-0.05250</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.08250</td>\n", "      <td>-0.08940</td>\n", "      <td>-0.10125</td>\n", "      <td>...</td>\n", "      <td>0.24250</td>\n", "      <td>0.28000</td>\n", "      <td>0.295000</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06750</td>\n", "      <td>-0.07000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-08</th>\n", "      <td>-0.023</td>\n", "      <td>-0.04500</td>\n", "      <td>-0.04630</td>\n", "      <td>-0.0494</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.08375</td>\n", "      <td>-0.09250</td>\n", "      <td>-0.10375</td>\n", "      <td>...</td>\n", "      <td>0.21880</td>\n", "      <td>0.25440</td>\n", "      <td>0.265000</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06500</td>\n", "      <td>-0.07250</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-09</th>\n", "      <td>-0.021</td>\n", "      <td>-0.04500</td>\n", "      <td>-0.04630</td>\n", "      <td>-0.0494</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.08250</td>\n", "      <td>-0.09125</td>\n", "      <td>-0.10375</td>\n", "      <td>...</td>\n", "      <td>0.21310</td>\n", "      <td>0.24690</td>\n", "      <td>0.256249</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06500</td>\n", "      <td>-0.07188</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-10</th>\n", "      <td>-0.017</td>\n", "      <td>-0.04500</td>\n", "      <td>-0.04630</td>\n", "      <td>-0.0488</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.05630</td>\n", "      <td>-0.08375</td>\n", "      <td>-0.09500</td>\n", "      <td>-0.10810</td>\n", "      <td>...</td>\n", "      <td>0.20690</td>\n", "      <td>0.24060</td>\n", "      <td>0.252500</td>\n", "      <td>-0.05625</td>\n", "      <td>-0.06375</td>\n", "      <td>-0.07250</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-13</th>\n", "      <td>-0.019</td>\n", "      <td>-0.03750</td>\n", "      <td>-0.04250</td>\n", "      <td>-0.0470</td>\n", "      <td>-0.04875</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05250</td>\n", "      <td>-0.07310</td>\n", "      <td>-0.08250</td>\n", "      <td>-0.09500</td>\n", "      <td>...</td>\n", "      <td>0.23750</td>\n", "      <td>0.27375</td>\n", "      <td>0.288750</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05750</td>\n", "      <td>-0.06000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-14</th>\n", "      <td>-0.017</td>\n", "      <td>-0.03750</td>\n", "      <td>-0.04250</td>\n", "      <td>-0.0460</td>\n", "      <td>-0.04750</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.07375</td>\n", "      <td>-0.08375</td>\n", "      <td>-0.09500</td>\n", "      <td>...</td>\n", "      <td>0.23375</td>\n", "      <td>0.27000</td>\n", "      <td>0.286250</td>\n", "      <td>-0.04750</td>\n", "      <td>-0.05500</td>\n", "      <td>-0.05875</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-07-15</th>\n", "      <td>-0.014</td>\n", "      <td>-0.03500</td>\n", "      <td>-0.03875</td>\n", "      <td>-0.0450</td>\n", "      <td>-0.04800</td>\n", "      <td>-0.05000</td>\n", "      <td>-0.05125</td>\n", "      <td>-0.07625</td>\n", "      <td>-0.08750</td>\n", "      <td>-0.10125</td>\n", "      <td>...</td>\n", "      <td>0.24000</td>\n", "      <td>0.27690</td>\n", "      <td>0.290000</td>\n", "      <td>-0.04750</td>\n", "      <td>-0.05375</td>\n", "      <td>-0.05875</td>\n", "      <td>-0.06500</td>\n", "      <td>-0.070</td>\n", "      <td>-0.070</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>20 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            MUTKCALM Index  JYSO1Z Curncy  JYSO2Z Curncy  JYSO3Z Curncy  \\\n", "2020-05-04             NaN       -0.05000       -0.05060        -0.0560   \n", "2020-05-05             NaN       -0.05000       -0.05060        -0.0513   \n", "2020-05-06             NaN       -0.05000       -0.05060        -0.0513   \n", "2020-05-19          -0.061       -0.05500       -0.04625        -0.0500   \n", "2020-06-02          -0.059       -0.05900       -0.06000        -0.0600   \n", "2020-06-25          -0.042       -0.04625       -0.05000        -0.0509   \n", "2020-06-26          -0.038       -0.04625       -0.04880        -0.0500   \n", "2020-06-29          -0.041       -0.04600       -0.04880        -0.0500   \n", "2020-06-30          -0.068       -0.04600       -0.06250        -0.0530   \n", "2020-07-01          -0.041       -0.04700       -0.05130        -0.0525   \n", "2020-07-02          -0.039       -0.04625       -0.05000        -0.0513   \n", "2020-07-03          -0.034       -0.04625       -0.04875        -0.0513   \n", "2020-07-06          -0.034       -0.04600       -0.04500        -0.0510   \n", "2020-07-07          -0.027       -0.04500       -0.04750        -0.0509   \n", "2020-07-08          -0.023       -0.04500       -0.04630        -0.0494   \n", "2020-07-09          -0.021       -0.04500       -0.04630        -0.0494   \n", "2020-07-10          -0.017       -0.04500       -0.04630        -0.0488   \n", "2020-07-13          -0.019       -0.03750       -0.04250        -0.0470   \n", "2020-07-14          -0.017       -0.03750       -0.04250        -0.0460   \n", "2020-07-15          -0.014       -0.03500       -0.03875        -0.0450   \n", "\n", "            JYSOA Curncy  JYSOB Curncy  JYSOC Curncy  JYSO1F <PERSON><PERSON><PERSON>  \\\n", "2020-05-04      -0.05125      -0.05625      -0.06375       -0.10630   \n", "2020-05-05      -0.05125      -0.05625      -0.06375       -0.10630   \n", "2020-05-06      -0.05190      -0.05880      -0.06500            NaN   \n", "2020-05-19      -0.05125      -0.05625      -0.06190       -0.10125   \n", "2020-06-02      -0.06000      -0.06375      -0.06750       -0.09625   \n", "2020-06-25      -0.05400      -0.05560      -0.05875       -0.08190   \n", "2020-06-26      -0.05375      -0.05625      -0.05875       -0.08100   \n", "2020-06-29      -0.05250      -0.05625      -0.05900       -0.08100   \n", "2020-06-30      -0.05750      -0.06000      -0.06250       -0.08000   \n", "2020-07-01      -0.05375      -0.05625      -0.05900       -0.08125   \n", "2020-07-02      -0.05375      -0.05625      -0.05900       -0.08000   \n", "2020-07-03      -0.05375      -0.05750      -0.05875       -0.08250   \n", "2020-07-06      -0.05375      -0.05700      -0.05875       -0.08060   \n", "2020-07-07      -0.05250      -0.05500      -0.05750       -0.08250   \n", "2020-07-08      -0.05125      -0.05500      -0.05750       -0.08375   \n", "2020-07-09      -0.05125      -0.05500      -0.05750       -0.08250   \n", "2020-07-10      -0.05000      -0.05500      -0.05630       -0.08375   \n", "2020-07-13      -0.04875      -0.05000      -0.05250       -0.07310   \n", "2020-07-14      -0.04750      -0.05000      -0.05125       -0.07375   \n", "2020-07-15      -0.04800      -0.05000      -0.05125       -0.07625   \n", "\n", "            JYSO2 Curncy  JYSO3 Curncy  ...  JYSO25 Curncy  JYSO30 Curncy  \\\n", "2020-05-04      -0.11440      -0.12875  ...            NaN            NaN   \n", "2020-05-05      -0.11875      -0.13130  ...        0.15060        0.17750   \n", "2020-05-06      -0.11875      -0.13130  ...            NaN            NaN   \n", "2020-05-19      -0.10750      -0.12000  ...        0.18500        0.21500   \n", "2020-06-02      -0.10310      -0.11250  ...        0.22500        0.26060   \n", "2020-06-25      -0.08875      -0.10190  ...        0.20375        0.23625   \n", "2020-06-26      -0.08750      -0.10125  ...        0.20750        0.24125   \n", "2020-06-29      -0.08750      -0.09875  ...        0.21125        0.24375   \n", "2020-06-30      -0.08375      -0.09250  ...        0.22750        0.26625   \n", "2020-07-01      -0.08500      -0.09500  ...        0.26130        0.30000   \n", "2020-07-02      -0.08560      -0.09600  ...        0.26125        0.29875   \n", "2020-07-03      -0.08875      -0.10250  ...        0.24000        0.27625   \n", "2020-07-06      -0.08625      -0.09700  ...        0.24625        0.28250   \n", "2020-07-07      -0.08940      -0.10125  ...        0.24250        0.28000   \n", "2020-07-08      -0.09250      -0.10375  ...        0.21880        0.25440   \n", "2020-07-09      -0.09125      -0.10375  ...        0.21310        0.24690   \n", "2020-07-10      -0.09500      -0.10810  ...        0.20690        0.24060   \n", "2020-07-13      -0.08250      -0.09500  ...        0.23750        0.27375   \n", "2020-07-14      -0.08375      -0.09500  ...        0.23375        0.27000   \n", "2020-07-15      -0.08750      -0.10125  ...        0.24000        0.27690   \n", "\n", "            JYSO40 Curncy  JYSOMPM1 Curncy  JYSOMPM2 Curncy  JYSOMPM3 Curncy  \\\n", "2020-05-04            NaN         -0.05200         -0.06500         -0.08200   \n", "2020-05-05            NaN              NaN              NaN              NaN   \n", "2020-05-06            NaN         -0.05200         -0.06500         -0.08200   \n", "2020-05-19       0.221250         -0.05875         -0.07500         -0.08688   \n", "2020-06-02       0.267500         -0.06125         -0.07375         -0.08125   \n", "2020-06-25       0.241250         -0.06000         -0.06750         -0.07500   \n", "2020-06-26       0.247500         -0.06000         -0.06750         -0.07500   \n", "2020-06-29       0.250000         -0.05875         -0.06750         -0.07500   \n", "2020-06-30       0.268800         -0.05875         -0.06750         -0.07500   \n", "2020-07-01       0.307500         -0.05875         -0.06750         -0.07500   \n", "2020-07-02       0.312500         -0.05875         -0.06625         -0.07375   \n", "2020-07-03       0.291250         -0.05875         -0.06625         -0.07375   \n", "2020-07-06       0.296250         -0.05875         -0.06625         -0.07375   \n", "2020-07-07       0.295000         -0.05625         -0.06750         -0.07000   \n", "2020-07-08       0.265000         -0.05625         -0.06500         -0.07250   \n", "2020-07-09       0.256249         -0.05625         -0.06500         -0.07188   \n", "2020-07-10       0.252500         -0.05625         -0.06375         -0.07250   \n", "2020-07-13       0.288750         -0.05000         -0.05750         -0.06000   \n", "2020-07-14       0.286250         -0.04750         -0.05500         -0.05875   \n", "2020-07-15       0.290000         -0.04750         -0.05375         -0.05875   \n", "\n", "            JYSOMPM4 Curncy  JYSOMPM5 Curncy  JYSOMPM6 Curncy  JYSOMPM7 Curncy  \n", "2020-05-04         -0.09200           -0.105           -0.112           -0.122  \n", "2020-05-05              NaN              NaN              NaN              NaN  \n", "2020-05-06         -0.09200           -0.105           -0.112           -0.122  \n", "2020-05-19         -0.09750              NaN           -0.117           -0.127  \n", "2020-06-02         -0.08875              NaN              NaN              NaN  \n", "2020-06-25              NaN              NaN              NaN              NaN  \n", "2020-06-26              NaN              NaN              NaN              NaN  \n", "2020-06-29              NaN              NaN              NaN              NaN  \n", "2020-06-30              NaN              NaN              NaN              NaN  \n", "2020-07-01              NaN              NaN              NaN              NaN  \n", "2020-07-02              NaN              NaN              NaN              NaN  \n", "2020-07-03              NaN              NaN              NaN              NaN  \n", "2020-07-06              NaN              NaN              NaN              NaN  \n", "2020-07-07              NaN              NaN              NaN              NaN  \n", "2020-07-08              NaN              NaN              NaN              NaN  \n", "2020-07-09              NaN              NaN              NaN              NaN  \n", "2020-07-10              NaN              NaN              NaN              NaN  \n", "2020-07-13              NaN              NaN              NaN              NaN  \n", "2020-07-14              NaN              NaN              NaN              NaN  \n", "2020-07-15         -0.06500           -0.070           -0.070              NaN  \n", "\n", "[20 rows x 31 columns]"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["missing = data[data.isna().any(axis=1)]\n", "missing[missing.index.map(lambda x: 2020 <= x.year <= 2021 and 5<= x.month <=8)].head(20)"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>JYSOMPM7 Curncy</th>\n", "      <th>JYSOMPM1 Curncy</th>\n", "      <th>JYSOMPM4 Curncy</th>\n", "      <th>JYSOMPM6 Curncy</th>\n", "      <th>JYSOMPM2 Curncy</th>\n", "      <th>JYSOMPM5 Curncy</th>\n", "      <th>JYSOMPM3 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-05</th>\n", "      <td>0.05000</td>\n", "      <td>0.06375</td>\n", "      <td>0.05000</td>\n", "      <td>0.05000</td>\n", "      <td>0.05500</td>\n", "      <td>0.05000</td>\n", "      <td>0.05000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-06</th>\n", "      <td>0.05250</td>\n", "      <td>0.06250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05500</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-07</th>\n", "      <td>0.05250</td>\n", "      <td>0.05625</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "      <td>0.05500</td>\n", "      <td>0.05250</td>\n", "      <td>0.05250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>0.17500</td>\n", "      <td>0.01375</td>\n", "      <td>0.09125</td>\n", "      <td>0.14187</td>\n", "      <td>0.03750</td>\n", "      <td>0.11625</td>\n", "      <td>0.06250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>0.17500</td>\n", "      <td>0.01312</td>\n", "      <td>0.09500</td>\n", "      <td>0.14750</td>\n", "      <td>0.03875</td>\n", "      <td>0.12250</td>\n", "      <td>0.06500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>0.16000</td>\n", "      <td>-0.00375</td>\n", "      <td>0.08000</td>\n", "      <td>0.14000</td>\n", "      <td>0.02000</td>\n", "      <td>0.11500</td>\n", "      <td>0.04125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>0.15500</td>\n", "      <td>-0.00125</td>\n", "      <td>0.07125</td>\n", "      <td>0.12750</td>\n", "      <td>0.01250</td>\n", "      <td>0.10500</td>\n", "      <td>0.03375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>0.16375</td>\n", "      <td>-0.00828</td>\n", "      <td>0.07250</td>\n", "      <td>0.13797</td>\n", "      <td>0.01000</td>\n", "      <td>0.10734</td>\n", "      <td>0.04000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2347 rows × 7 columns</p>\n", "</div>"], "text/plain": ["            JYSOMPM7 Curncy  JYSOMPM1 Curncy  JYSOMPM4 Curncy  \\\n", "2015-01-01              NaN              NaN              NaN   \n", "2015-01-02              NaN              NaN              NaN   \n", "2015-01-05          0.05000          0.06375          0.05000   \n", "2015-01-06          0.05250          0.06250          0.05250   \n", "2015-01-07          0.05250          0.05625          0.05250   \n", "...                     ...              ...              ...   \n", "2023-12-25          0.17500          0.01375          0.09125   \n", "2023-12-26          0.17500          0.01312          0.09500   \n", "2023-12-27          0.16000         -0.00375          0.08000   \n", "2023-12-28          0.15500         -0.00125          0.07125   \n", "2023-12-29          0.16375         -0.00828          0.07250   \n", "\n", "            JYSOMPM6 Curncy  JYSOMPM2 Curncy  JYSOMPM5 Curncy  JYSOMPM3 Curncy  \n", "2015-01-01              NaN              NaN              NaN              NaN  \n", "2015-01-02              NaN              NaN              NaN              NaN  \n", "2015-01-05          0.05000          0.05500          0.05000          0.05000  \n", "2015-01-06          0.05250          0.05500          0.05250          0.05250  \n", "2015-01-07          0.05250          0.05500          0.05250          0.05250  \n", "...                     ...              ...              ...              ...  \n", "2023-12-25          0.14187          0.03750          0.11625          0.06250  \n", "2023-12-26          0.14750          0.03875          0.12250          0.06500  \n", "2023-12-27          0.14000          0.02000          0.11500          0.04125  \n", "2023-12-28          0.12750          0.01250          0.10500          0.03375  \n", "2023-12-29          0.13797          0.01000          0.10734          0.04000  \n", "\n", "[2347 rows x 7 columns]"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["data.iloc[:,24:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from xbbg import blp\n", "blp.bdh(tickers=['JYSOMPM1 Curncy','JYSOMPM2 Curncy','JYSOMPM3 Curncy','JYSOMPM4 Curncy','JYSOMPM5 Curncy','JYSOMPM6 Curncy','JYSOMPM7 Curncy'], flds=['PX_LAST'], start_date=dt.date(2020,6,1),end_date='2020-06-30')"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MUTKCALM Index</th>\n", "      <th>JYSO1Z Curncy</th>\n", "      <th>JYSO2Z Curncy</th>\n", "      <th>JYSO3Z Curncy</th>\n", "      <th>JYSOA Curncy</th>\n", "      <th>JYSO<PERSON></th>\n", "      <th>JYSOC Curncy</th>\n", "      <th>JYSO1F <PERSON></th>\n", "      <th>JYSO2 <PERSON>cy</th>\n", "      <th>JYSO3 C<PERSON>cy</th>\n", "      <th>...</th>\n", "      <th>JYSO<PERSON> C<PERSON>cy</th>\n", "      <th>JY<PERSON><PERSON></th>\n", "      <th>JY<PERSON><PERSON><PERSON></th>\n", "      <th>JYSO11 Curncy</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON>Y<PERSON><PERSON> <PERSON><PERSON><PERSON></th>\n", "      <th>JYSO<PERSON> Curncy</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>JY<PERSON><PERSON>cy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>NaN</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058100</td>\n", "      <td>0.057500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.326875</td>\n", "      <td>0.378800</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>NaN</td>\n", "      <td>0.0600</td>\n", "      <td>0.06000</td>\n", "      <td>0.0600</td>\n", "      <td>0.05900</td>\n", "      <td>0.058100</td>\n", "      <td>0.056000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.326875</td>\n", "      <td>0.378800</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-05</th>\n", "      <td>0.070</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058100</td>\n", "      <td>0.056480</td>\n", "      <td>0.05300</td>\n", "      <td>0.05310</td>\n", "      <td>0.06250</td>\n", "      <td>...</td>\n", "      <td>0.220625</td>\n", "      <td>0.26500</td>\n", "      <td>0.312500</td>\n", "      <td>0.365650</td>\n", "      <td>0.42125</td>\n", "      <td>0.618750</td>\n", "      <td>0.880000</td>\n", "      <td>1.01250</td>\n", "      <td>1.079999</td>\n", "      <td>1.201250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-06</th>\n", "      <td>0.071</td>\n", "      <td>0.0650</td>\n", "      <td>0.06250</td>\n", "      <td>0.0613</td>\n", "      <td>0.05940</td>\n", "      <td>0.058000</td>\n", "      <td>0.056000</td>\n", "      <td>0.05250</td>\n", "      <td>0.05300</td>\n", "      <td>0.06000</td>\n", "      <td>...</td>\n", "      <td>0.198750</td>\n", "      <td>0.23875</td>\n", "      <td>0.281875</td>\n", "      <td>0.328750</td>\n", "      <td>0.38500</td>\n", "      <td>0.570000</td>\n", "      <td>0.805000</td>\n", "      <td>0.93500</td>\n", "      <td>0.997500</td>\n", "      <td>1.106250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-07</th>\n", "      <td>0.073</td>\n", "      <td>0.0625</td>\n", "      <td>0.06130</td>\n", "      <td>0.0600</td>\n", "      <td>0.06000</td>\n", "      <td>0.058000</td>\n", "      <td>0.056000</td>\n", "      <td>0.05300</td>\n", "      <td>0.05300</td>\n", "      <td>0.06500</td>\n", "      <td>...</td>\n", "      <td>0.211250</td>\n", "      <td>0.25000</td>\n", "      <td>0.290625</td>\n", "      <td>0.336250</td>\n", "      <td>0.38500</td>\n", "      <td>0.560000</td>\n", "      <td>0.789375</td>\n", "      <td>0.91250</td>\n", "      <td>0.975000</td>\n", "      <td>1.090000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>-0.011</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01250</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01100</td>\n", "      <td>-0.000043</td>\n", "      <td>0.010000</td>\n", "      <td>0.14500</td>\n", "      <td>0.21000</td>\n", "      <td>0.30000</td>\n", "      <td>...</td>\n", "      <td>0.696250</td>\n", "      <td>0.76499</td>\n", "      <td>0.831250</td>\n", "      <td>0.891250</td>\n", "      <td>0.94500</td>\n", "      <td>1.102500</td>\n", "      <td>1.304000</td>\n", "      <td>1.38400</td>\n", "      <td>1.410000</td>\n", "      <td>1.396249</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>-0.011</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01300</td>\n", "      <td>-0.0125</td>\n", "      <td>-0.01200</td>\n", "      <td>0.001250</td>\n", "      <td>0.009000</td>\n", "      <td>0.14900</td>\n", "      <td>0.20501</td>\n", "      <td>0.29750</td>\n", "      <td>...</td>\n", "      <td>0.712500</td>\n", "      <td>0.78250</td>\n", "      <td>0.848750</td>\n", "      <td>0.908750</td>\n", "      <td>0.96250</td>\n", "      <td>1.124990</td>\n", "      <td>1.331260</td>\n", "      <td>1.41124</td>\n", "      <td>1.436250</td>\n", "      <td>1.428750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>-0.013</td>\n", "      <td>-0.0144</td>\n", "      <td>-0.01500</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01331</td>\n", "      <td>-0.009000</td>\n", "      <td>-0.004475</td>\n", "      <td>0.12500</td>\n", "      <td>0.17875</td>\n", "      <td>0.27125</td>\n", "      <td>...</td>\n", "      <td>0.685000</td>\n", "      <td>0.75500</td>\n", "      <td>0.821250</td>\n", "      <td>0.882500</td>\n", "      <td>0.93875</td>\n", "      <td>1.099999</td>\n", "      <td>1.307500</td>\n", "      <td>1.38875</td>\n", "      <td>1.414999</td>\n", "      <td>1.407500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>-0.016</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01505</td>\n", "      <td>-0.0150</td>\n", "      <td>-0.01375</td>\n", "      <td>-0.010025</td>\n", "      <td>-0.006250</td>\n", "      <td>0.13000</td>\n", "      <td>0.18500</td>\n", "      <td>0.27250</td>\n", "      <td>...</td>\n", "      <td>0.685000</td>\n", "      <td>0.75625</td>\n", "      <td>0.821250</td>\n", "      <td>0.878125</td>\n", "      <td>0.94000</td>\n", "      <td>1.105000</td>\n", "      <td>1.318750</td>\n", "      <td>1.40625</td>\n", "      <td>1.438750</td>\n", "      <td>1.428750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>-0.039</td>\n", "      <td>-0.0210</td>\n", "      <td>-0.01900</td>\n", "      <td>-0.0175</td>\n", "      <td>-0.01600</td>\n", "      <td>-0.015000</td>\n", "      <td>-0.008750</td>\n", "      <td>0.13257</td>\n", "      <td>0.19250</td>\n", "      <td>0.29000</td>\n", "      <td>...</td>\n", "      <td>0.712480</td>\n", "      <td>0.78375</td>\n", "      <td>0.851250</td>\n", "      <td>0.919000</td>\n", "      <td>0.97250</td>\n", "      <td>1.137500</td>\n", "      <td>1.352500</td>\n", "      <td>1.44125</td>\n", "      <td>1.472500</td>\n", "      <td>1.468000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2347 rows × 24 columns</p>\n", "</div>"], "text/plain": ["            MUTKCALM Index  JYSO1Z Curncy  JYSO2Z Curncy  JYSO3Z Curncy  \\\n", "2015-01-01             NaN         0.0650        0.06250         0.0613   \n", "2015-01-02             NaN         0.0600        0.06000         0.0600   \n", "2015-01-05           0.070         0.0650        0.06250         0.0613   \n", "2015-01-06           0.071         0.0650        0.06250         0.0613   \n", "2015-01-07           0.073         0.0625        0.06130         0.0600   \n", "...                    ...            ...            ...            ...   \n", "2023-12-25          -0.011        -0.0125       -0.01250        -0.0125   \n", "2023-12-26          -0.011        -0.0125       -0.01300        -0.0125   \n", "2023-12-27          -0.013        -0.0144       -0.01500        -0.0150   \n", "2023-12-28          -0.016        -0.0150       -0.01505        -0.0150   \n", "2023-12-29          -0.039        -0.0210       -0.01900        -0.0175   \n", "\n", "            JYSOA Curncy  JYSOB Curncy  JYSOC Curncy  JYSO1F <PERSON><PERSON><PERSON>  \\\n", "2015-01-01       0.05940      0.058100      0.057500            NaN   \n", "2015-01-02       0.05900      0.058100      0.056000            NaN   \n", "2015-01-05       0.05940      0.058100      0.056480        0.05300   \n", "2015-01-06       0.05940      0.058000      0.056000        0.05250   \n", "2015-01-07       0.06000      0.058000      0.056000        0.05300   \n", "...                  ...           ...           ...            ...   \n", "2023-12-25      -0.01100     -0.000043      0.010000        0.14500   \n", "2023-12-26      -0.01200      0.001250      0.009000        0.14900   \n", "2023-12-27      -0.01331     -0.009000     -0.004475        0.12500   \n", "2023-12-28      -0.01375     -0.010025     -0.006250        0.13000   \n", "2023-12-29      -0.01600     -0.015000     -0.008750        0.13257   \n", "\n", "            JYSO2 Curncy  JYSO3 Curncy  ...  JYSO8 Curncy  JYSO9 Curncy  \\\n", "2015-01-01           NaN           NaN  ...           NaN           NaN   \n", "2015-01-02           NaN           NaN  ...           NaN           NaN   \n", "2015-01-05       0.05310       0.06250  ...      0.220625       0.26500   \n", "2015-01-06       0.05300       0.06000  ...      0.198750       0.23875   \n", "2015-01-07       0.05300       0.06500  ...      0.211250       0.25000   \n", "...                  ...           ...  ...           ...           ...   \n", "2023-12-25       0.21000       0.30000  ...      0.696250       0.76499   \n", "2023-12-26       0.20501       0.29750  ...      0.712500       0.78250   \n", "2023-12-27       0.17875       0.27125  ...      0.685000       0.75500   \n", "2023-12-28       0.18500       0.27250  ...      0.685000       0.75625   \n", "2023-12-29       0.19250       0.29000  ...      0.712480       0.78375   \n", "\n", "            JYSO10 Curncy  JYSO11 Curncy  JYSO12 Curncy  JYSO15 Curncy  \\\n", "2015-01-01       0.326875       0.378800            NaN            NaN   \n", "2015-01-02       0.326875       0.378800            NaN            NaN   \n", "2015-01-05       0.312500       0.365650        0.42125       0.618750   \n", "2015-01-06       0.281875       0.328750        0.38500       0.570000   \n", "2015-01-07       0.290625       0.336250        0.38500       0.560000   \n", "...                   ...            ...            ...            ...   \n", "2023-12-25       0.831250       0.891250        0.94500       1.102500   \n", "2023-12-26       0.848750       0.908750        0.96250       1.124990   \n", "2023-12-27       0.821250       0.882500        0.93875       1.099999   \n", "2023-12-28       0.821250       0.878125        0.94000       1.105000   \n", "2023-12-29       0.851250       0.919000        0.97250       1.137500   \n", "\n", "            JYSO20 Curncy  JYSO25 Curncy  JYSO30 Curncy  JYSO40 Curncy  \n", "2015-01-01            NaN            NaN            NaN       1.217500  \n", "2015-01-02            NaN            NaN            NaN       1.217500  \n", "2015-01-05       0.880000        1.01250       1.079999       1.201250  \n", "2015-01-06       0.805000        0.93500       0.997500       1.106250  \n", "2015-01-07       0.789375        0.91250       0.975000       1.090000  \n", "...                   ...            ...            ...            ...  \n", "2023-12-25       1.304000        1.38400       1.410000       1.396249  \n", "2023-12-26       1.331260        1.41124       1.436250       1.428750  \n", "2023-12-27       1.307500        1.38875       1.414999       1.407500  \n", "2023-12-28       1.318750        1.40625       1.438750       1.428750  \n", "2023-12-29       1.352500        1.44125       1.472500       1.468000  \n", "\n", "[2347 rows x 24 columns]"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["data=data.drop(columns=data.iloc[:,24:].columns)\n", "data"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["data.to_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\JPY_TONAR_20150101_marketclose.parquet\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import QuantLib as ql\n", "from helpers.date_helpers import to_ql_date"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>MUTKCALM Index</th>\n", "      <th>JYSO1Z Curncy</th>\n", "      <th>JYSO2Z Curncy</th>\n", "      <th>JYSO3Z Curncy</th>\n", "      <th>JYSOA Curncy</th>\n", "      <th>JYSO<PERSON></th>\n", "      <th>JYSOC Curncy</th>\n", "      <th>JYSO1F <PERSON></th>\n", "      <th>JYSO2 <PERSON>cy</th>\n", "      <th>JYSO3 C<PERSON>cy</th>\n", "      <th>...</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>JY<PERSON><PERSON>cy</th>\n", "      <th>JYSOMPM7 Curncy</th>\n", "      <th>JYSOMPM1 Curncy</th>\n", "      <th>JYSOMPM4 Curncy</th>\n", "      <th>JYSOMPM6 Curncy</th>\n", "      <th>JYSOMPM2 Curncy</th>\n", "      <th>JYSOMPM5 Curncy</th>\n", "      <th>JYSOMPM3 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2015-01-01</th>\n", "      <td>NaN</td>\n", "      <td>0.06500</td>\n", "      <td>0.062500</td>\n", "      <td>0.0613</td>\n", "      <td>0.0594</td>\n", "      <td>0.0581</td>\n", "      <td>0.05750</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-02</th>\n", "      <td>NaN</td>\n", "      <td>0.06000</td>\n", "      <td>0.060000</td>\n", "      <td>0.0600</td>\n", "      <td>0.0590</td>\n", "      <td>0.0581</td>\n", "      <td>0.05600</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.217500</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-01-12</th>\n", "      <td>NaN</td>\n", "      <td>0.06250</td>\n", "      <td>0.061300</td>\n", "      <td>0.0600</td>\n", "      <td>0.0600</td>\n", "      <td>0.0580</td>\n", "      <td>0.05600</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.096900</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-02-11</th>\n", "      <td>NaN</td>\n", "      <td>0.07300</td>\n", "      <td>0.070000</td>\n", "      <td>0.0690</td>\n", "      <td>0.0680</td>\n", "      <td>0.0650</td>\n", "      <td>0.05900</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>1.146250</td>\n", "      <td>1.221250</td>\n", "      <td>1.335000</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2015-04-29</th>\n", "      <td>NaN</td>\n", "      <td>0.06500</td>\n", "      <td>0.065000</td>\n", "      <td>0.0640</td>\n", "      <td>0.0640</td>\n", "      <td>0.0620</td>\n", "      <td>0.06100</td>\n", "      <td>0.05310</td>\n", "      <td>0.05400</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>1.073750</td>\n", "      <td>1.151249</td>\n", "      <td>1.258800</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-04</th>\n", "      <td>-0.069</td>\n", "      <td>-0.06875</td>\n", "      <td>-0.057605</td>\n", "      <td>-0.0600</td>\n", "      <td>-0.0575</td>\n", "      <td>-0.0550</td>\n", "      <td>-0.05125</td>\n", "      <td>0.06750</td>\n", "      <td>0.13375</td>\n", "      <td>0.24250</td>\n", "      <td>...</td>\n", "      <td>1.386250</td>\n", "      <td>1.413750</td>\n", "      <td>1.411999</td>\n", "      <td>0.06625</td>\n", "      <td>-0.05625</td>\n", "      <td>0.00625</td>\n", "      <td>0.03375</td>\n", "      <td>NaN</td>\n", "      <td>0.02250</td>\n", "      <td>-0.02375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-09-18</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.12800</td>\n", "      <td>0.20400</td>\n", "      <td>0.32375</td>\n", "      <td>...</td>\n", "      <td>1.435000</td>\n", "      <td>1.456260</td>\n", "      <td>1.454000</td>\n", "      <td>NaN</td>\n", "      <td>-0.04250</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.08375</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-10-09</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.12624</td>\n", "      <td>0.20251</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>1.619999</td>\n", "      <td>1.651249</td>\n", "      <td>1.649375</td>\n", "      <td>0.13563</td>\n", "      <td>NaN</td>\n", "      <td>0.05750</td>\n", "      <td>NaN</td>\n", "      <td>-0.01125</td>\n", "      <td>0.08813</td>\n", "      <td>0.02563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11-03</th>\n", "      <td>NaN</td>\n", "      <td>-0.02000</td>\n", "      <td>-0.022000</td>\n", "      <td>-0.0240</td>\n", "      <td>-0.0170</td>\n", "      <td>NaN</td>\n", "      <td>-0.01000</td>\n", "      <td>0.20500</td>\n", "      <td>0.29375</td>\n", "      <td>0.43625</td>\n", "      <td>...</td>\n", "      <td>1.692500</td>\n", "      <td>1.713750</td>\n", "      <td>1.726000</td>\n", "      <td>0.27375</td>\n", "      <td>-0.01250</td>\n", "      <td>0.12750</td>\n", "      <td>0.23000</td>\n", "      <td>0.02750</td>\n", "      <td>0.18000</td>\n", "      <td>0.07625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-11-23</th>\n", "      <td>NaN</td>\n", "      <td>-0.01600</td>\n", "      <td>-0.017500</td>\n", "      <td>-0.0200</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>0.13875</td>\n", "      <td>0.21125</td>\n", "      <td>0.33375</td>\n", "      <td>...</td>\n", "      <td>1.505000</td>\n", "      <td>1.534000</td>\n", "      <td>1.535000</td>\n", "      <td>0.16000</td>\n", "      <td>-0.02000</td>\n", "      <td>0.05750</td>\n", "      <td>0.12812</td>\n", "      <td>0.00500</td>\n", "      <td>0.08750</td>\n", "      <td>0.03000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>902 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            MUTKCALM Index  JYSO1Z Curncy  JYSO2Z Curncy  JYSO3Z Curncy  \\\n", "2015-01-01             NaN        0.06500       0.062500         0.0613   \n", "2015-01-02             NaN        0.06000       0.060000         0.0600   \n", "2015-01-12             NaN        0.06250       0.061300         0.0600   \n", "2015-02-11             NaN        0.07300       0.070000         0.0690   \n", "2015-04-29             NaN        0.06500       0.065000         0.0640   \n", "...                    ...            ...            ...            ...   \n", "2023-09-04          -0.069       -0.06875      -0.057605        -0.0600   \n", "2023-09-18             NaN            NaN            NaN            NaN   \n", "2023-10-09             NaN            NaN            NaN            NaN   \n", "2023-11-03             NaN       -0.02000      -0.022000        -0.0240   \n", "2023-11-23             NaN       -0.01600      -0.017500        -0.0200   \n", "\n", "            JYSOA Curncy  JYSOB Curncy  JYSOC Curncy  JYSO1F <PERSON><PERSON><PERSON>  \\\n", "2015-01-01        0.0594        0.0581       0.05750            NaN   \n", "2015-01-02        0.0590        0.0581       0.05600            NaN   \n", "2015-01-12        0.0600        0.0580       0.05600            NaN   \n", "2015-02-11        0.0680        0.0650       0.05900            NaN   \n", "2015-04-29        0.0640        0.0620       0.06100        0.05310   \n", "...                  ...           ...           ...            ...   \n", "2023-09-04       -0.0575       -0.0550      -0.05125        0.06750   \n", "2023-09-18           NaN           NaN           NaN        0.12800   \n", "2023-10-09           NaN           NaN           NaN        0.12624   \n", "2023-11-03       -0.0170           NaN      -0.01000        0.20500   \n", "2023-11-23           NaN           NaN           NaN        0.13875   \n", "\n", "            JYSO2 Curncy  JYSO3 Curncy  ...  JYSO25 Curncy  JYSO30 Curncy  \\\n", "2015-01-01           NaN           NaN  ...            NaN            NaN   \n", "2015-01-02           NaN           NaN  ...            NaN            NaN   \n", "2015-01-12           NaN           NaN  ...            NaN            NaN   \n", "2015-02-11           NaN           NaN  ...       1.146250       1.221250   \n", "2015-04-29       0.05400           NaN  ...       1.073750       1.151249   \n", "...                  ...           ...  ...            ...            ...   \n", "2023-09-04       0.13375       0.24250  ...       1.386250       1.413750   \n", "2023-09-18       0.20400       0.32375  ...       1.435000       1.456260   \n", "2023-10-09       0.20251           NaN  ...       1.619999       1.651249   \n", "2023-11-03       0.29375       0.43625  ...       1.692500       1.713750   \n", "2023-11-23       0.21125       0.33375  ...       1.505000       1.534000   \n", "\n", "            JYSO40 Curncy  JYSOMPM7 Curncy  JYSOMPM1 Curncy  JYSOMPM4 Curncy  \\\n", "2015-01-01       1.217500              NaN              NaN              NaN   \n", "2015-01-02       1.217500              NaN              NaN              NaN   \n", "2015-01-12       1.096900              NaN              NaN              NaN   \n", "2015-02-11       1.335000              NaN              NaN              NaN   \n", "2015-04-29       1.258800              NaN              NaN              NaN   \n", "...                   ...              ...              ...              ...   \n", "2023-09-04       1.411999          0.06625         -0.05625          0.00625   \n", "2023-09-18       1.454000              NaN         -0.04250              NaN   \n", "2023-10-09       1.649375          0.13563              NaN          0.05750   \n", "2023-11-03       1.726000          0.27375         -0.01250          0.12750   \n", "2023-11-23       1.535000          0.16000         -0.02000          0.05750   \n", "\n", "            JYSOMPM6 Curncy  JYSOMPM2 Curncy  JYSOMPM5 Curncy  JYSOMPM3 Curncy  \n", "2015-01-01              NaN              NaN              NaN              NaN  \n", "2015-01-02              NaN              NaN              NaN              NaN  \n", "2015-01-12              NaN              NaN              NaN              NaN  \n", "2015-02-11              NaN              NaN              NaN              NaN  \n", "2015-04-29              NaN              NaN              NaN              NaN  \n", "...                     ...              ...              ...              ...  \n", "2023-09-04          0.03375              NaN          0.02250         -0.02375  \n", "2023-09-18              NaN              NaN          0.08375              NaN  \n", "2023-10-09              NaN         -0.01125          0.08813          0.02563  \n", "2023-11-03          0.23000          0.02750          0.18000          0.07625  \n", "2023-11-23          0.12812          0.00500          0.08750          0.03000  \n", "\n", "[902 rows x 31 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["data[data.isna().any(axis=1)]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["data.to_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2023-12-20, 2023-12-20]\n", "Loading close price from 2020-09-09\n", "Opening cached curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\CAD_CORRA_20200909_marketclose.parquet\n"]}], "source": ["from curves import corraCurve\n", "cor = corraCurve(\"20231220\")\n", "benchmark_meta_data, bbg_bar_data = cor.fetch_curve_benchmarks()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_type</th>\n", "      <th>maturities</th>\n", "      <th>ticker</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Deposit</td>\n", "      <td>1D</td>\n", "      <td>CAONREPO Index</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>1W</td>\n", "      <td>CDSO1Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>2W</td>\n", "      <td>CDSO2Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>3W</td>\n", "      <td>CDSO3Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>1M</td>\n", "      <td>CDSOA Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>2M</td>\n", "      <td>CDSOB Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>3M</td>\n", "      <td>CDSOC Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>18M</td>\n", "      <td>CDSO1F Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>2Y</td>\n", "      <td>CDSO2 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>3Y</td>\n", "      <td>CDSO3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>4Y</td>\n", "      <td>CDSO4 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>5Y</td>\n", "      <td>CDSO5 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>7Y</td>\n", "      <td>CDSO7 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>10Y</td>\n", "      <td>CDSO10 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>12Y</td>\n", "      <td>CDSO12 <PERSON>cy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>15Y</td>\n", "      <td>CDSO<PERSON> Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>20Y</td>\n", "      <td>CDSO20 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>25Y</td>\n", "      <td>CDSO25 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>30Y</td>\n", "      <td>CDSO30 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>40Y</td>\n", "      <td>CDSO40 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s1</td>\n", "      <td>CDSF1A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s2</td>\n", "      <td>CDSF2A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s3</td>\n", "      <td>CDSF3A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s4</td>\n", "      <td>CDSF4A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s5</td>\n", "      <td>CDSF5A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s6</td>\n", "      <td>CDSF6A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s7</td>\n", "      <td>CDSF7A Curncy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_type maturities          ticker\n", "0              Deposit         1D  CAONREPO Index\n", "1   OvernightIndexSwap         1W   CDSO1Z Curncy\n", "2   OvernightIndexSwap         2W   CDSO2Z Curncy\n", "3   OvernightIndexSwap         3W   CDSO3Z Curncy\n", "4   OvernightIndexSwap         1M    CDSOA Curncy\n", "5   OvernightIndexSwap         2M    CDSOB Curncy\n", "6   OvernightIndexSwap         3M    CDSOC Curncy\n", "7   OvernightIndexSwap        18M   CDSO1F Curncy\n", "8   OvernightIndexSwap         2Y    CDSO2 Curncy\n", "9   OvernightIndexSwap         3Y    CDSO3 Curncy\n", "10  OvernightIndexSwap         4Y    CDSO4 Curncy\n", "11  OvernightIndexSwap         5Y    CDSO5 Curncy\n", "12  OvernightIndexSwap         7Y    CDSO7 Curncy\n", "13  OvernightIndexSwap        10Y   CDSO10 Curncy\n", "14  OvernightIndexSwap        12Y   CDSO12 Curncy\n", "15  OvernightIndexSwap        15Y   CDSO15 Curncy\n", "16  OvernightIndexSwap        20Y   CDSO20 Curncy\n", "17  OvernightIndexSwap        25Y   CDSO25 Curncy\n", "18  OvernightIndexSwap        30Y   CDSO30 Curncy\n", "19  OvernightIndexSwap        40Y   CDSO40 Curncy\n", "20     MeetingDateSwap         s1   CDSF1A Curncy\n", "21     MeetingDateSwap         s2   CDSF2A Curncy\n", "22     MeetingDateSwap         s3   CDSF3A Curncy\n", "23     MeetingDateSwap         s4   CDSF4A Curncy\n", "24     MeetingDateSwap         s5   CDSF5A Curncy\n", "25     MeetingDateSwap         s6   CDSF6A Curncy\n", "26     MeetingDateSwap         s7   CDSF7A Curncy"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmark_meta_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_type</th>\n", "      <th>maturities</th>\n", "      <th>ticker</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Deposit</td>\n", "      <td>1D</td>\n", "      <td>CAONREPO Index</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>1W</td>\n", "      <td>CDSO1Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>2W</td>\n", "      <td>CDSO2Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>3W</td>\n", "      <td>CDSO3Z Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>1M</td>\n", "      <td>CDSOA Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>2M</td>\n", "      <td>CDSOB Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>OvernightIndexSwap</td>\n", "      <td>3M</td>\n", "      <td>CDSOC Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s1</td>\n", "      <td>CDSF1A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s2</td>\n", "      <td>CDSF2A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s3</td>\n", "      <td>CDSF3A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s4</td>\n", "      <td>CDSF4A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s5</td>\n", "      <td>CDSF5A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s6</td>\n", "      <td>CDSF6A Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>MeetingDateSwap</td>\n", "      <td>s7</td>\n", "      <td>CDSF7A Curncy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       instrument_type maturities          ticker\n", "0              Deposit         1D  CAONREPO Index\n", "1   OvernightIndexSwap         1W   CDSO1Z Curncy\n", "2   OvernightIndexSwap         2W   CDSO2Z Curncy\n", "3   OvernightIndexSwap         3W   CDSO3Z Curncy\n", "4   OvernightIndexSwap         1M    CDSOA Curncy\n", "5   OvernightIndexSwap         2M    CDSOB Curncy\n", "6   OvernightIndexSwap         3M    CDSOC Curncy\n", "20     MeetingDateSwap         s1   CDSF1A Curncy\n", "21     MeetingDateSwap         s2   CDSF2A Curncy\n", "22     MeetingDateSwap         s3   CDSF3A Curncy\n", "23     MeetingDateSwap         s4   CDSF4A Curncy\n", "24     MeetingDateSwap         s5   CDSF5A Curncy\n", "25     MeetingDateSwap         s6   CDSF6A Curncy\n", "26     MeetingDateSwap         s7   CDSF7A Curncy"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import QuantLib as ql\n", "annual_benchmark_condition = ~benchmark_meta_data.apply(\n", "            lambda row: row[\"instrument_type\"] == \"OvernightIndexSwap\"\n", "            and ql.Period(row[\"maturities\"]) > ql.Period(\"1Y\"),\n", "            axis=1,\n", "        )\n", "benchmark_meta_data[annual_benchmark_condition]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2023-12-20, 2023-12-20]\n", "Loading close price from 2020-09-09\n", "Opening cached curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\CAD_CORRA_20200909_marketclose.parquet\n"]}, {"ename": "KeyError", "evalue": "'instrument_type'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[13], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m cor\u001b[38;5;241m.\u001b[39mset_up_helpers()\n", "File \u001b[1;32mc:\\Users\\<USER>\\Documents\\investment\\curves\\cad.py:44\u001b[0m, in \u001b[0;36mcorraCurve.set_up_helpers\u001b[1;34m(self, benchmark_meta_data, bbg_bar_data)\u001b[0m\n\u001b[0;32m     31\u001b[0m annual_benchmark_condition \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m~\u001b[39mbenchmark_meta_data\u001b[38;5;241m.\u001b[39mapply(\n\u001b[0;32m     32\u001b[0m     \u001b[38;5;28;01mlambda\u001b[39;00m row: row[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minstrument_type\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOvernightIndexSwap\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m     33\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m ql\u001b[38;5;241m.\u001b[39mPeriod(row[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmaturities\u001b[39m\u001b[38;5;124m\"\u001b[39m]) \u001b[38;5;241m>\u001b[39m ql\u001b[38;5;241m.\u001b[39mPeriod(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m1Y\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[0;32m     34\u001b[0m     axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m,\n\u001b[0;32m     35\u001b[0m )\n\u001b[0;32m     36\u001b[0m swap_params \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     37\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mindex\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mois_index,\n\u001b[0;32m     38\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpaymentConvention\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbusiness_day_convention,\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     42\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfixedCalendar\u001b[39m\u001b[38;5;124m\"\u001b[39m: ql\u001b[38;5;241m.\u001b[39mCanada(),\n\u001b[0;32m     43\u001b[0m }\n\u001b[1;32m---> 44\u001b[0m annual_helpers \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mset_up_helpers(\n\u001b[0;32m     45\u001b[0m     swap_params,\n\u001b[0;32m     46\u001b[0m     benchmark_meta_data[annual_benchmark_condition],\n\u001b[0;32m     47\u001b[0m     bbg_bar_data,\n\u001b[0;32m     48\u001b[0m )\n\u001b[0;32m     49\u001b[0m flat_pillar_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mflat_pillar_size\n\u001b[0;32m     51\u001b[0m \u001b[38;5;66;03m# CORRA OIS: Pay Freq = SemiAnnual for term > 1Y\u001b[39;00m\n", "File \u001b[1;32mc:\\Users\\<USER>\\Documents\\investment\\curves\\curve.py:225\u001b[0m, in \u001b[0;36moisCurve.set_up_helpers\u001b[1;34m(self, benchmark_meta_data, bbg_bar_data, swap_params)\u001b[0m\n\u001b[0;32m    222\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m benchmark_meta_data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mor\u001b[39;00m bbg_bar_data \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m    223\u001b[0m     benchmark_meta_data, bbg_bar_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mfetch_curve_benchmarks()\n\u001b[0;32m    224\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mflat_pillar_size \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(\n\u001b[1;32m--> 225\u001b[0m     benchmark_meta_data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minstrument_type\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m    226\u001b[0m     \u001b[38;5;241m.\u001b[39mvalue_counts()\n\u001b[0;32m    227\u001b[0m     \u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMeetingDateSwap\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m0\u001b[39m)\n\u001b[0;32m    228\u001b[0m )\n\u001b[0;32m    229\u001b[0m \u001b[38;5;28;01massert\u001b[39;00m (\n\u001b[0;32m    230\u001b[0m     bbg_bar_data\u001b[38;5;241m.\u001b[39mname \u001b[38;5;241m==\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdate\n\u001b[0;32m    231\u001b[0m ), \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcurve_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m on \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdate\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is using data on \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mbbg_bar_data\u001b[38;5;241m.\u001b[39mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    232\u001b[0m helpers \u001b[38;5;241m=\u001b[39m []\n", "\u001b[1;31m<PERSON>eyError\u001b[0m: 'instrument_type'"]}], "source": ["cor.set_up_helpers()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["CAONREPO Index    5.0400\n", "CDSO1Z Curncy     5.0247\n", "CDSO2Z Curncy     5.0274\n", "CDSO3Z Curncy     5.0250\n", "CDSOA Curncy      5.0320\n", "CDSOB Curncy      5.0260\n", "CDSOC Curncy      5.0120\n", "CDSO1F Curncy     4.1780\n", "CDSO2 Curncy      3.9040\n", "CDSO3 Curncy      3.5310\n", "CDSO4 Curncy      3.2850\n", "CDSO5 Curncy      3.1395\n", "CDSO7 Curncy      3.0500\n", "CDSO10 Curncy     3.0460\n", "CDSO12 <PERSON><PERSON><PERSON>     3.0782\n", "CDSO15 <PERSON><PERSON>cy     3.1205\n", "CDSO20 Curncy     3.1180\n", "CDSO25 <PERSON><PERSON><PERSON>     3.0120\n", "CDSO30 <PERSON><PERSON><PERSON>     2.8785\n", "CDSO40 C<PERSON>cy     2.7698\n", "CDSF1A Curncy     4.9975\n", "CDSF2A Curncy     4.8920\n", "CDSF3A Curncy     4.7210\n", "CDSF4A Curncy     4.4670\n", "CDSF5A Curncy     4.2290\n", "CDSF6A Curncy     4.0400\n", "CDSF7A Curncy     3.8620\n", "Name: 2023-12-20, dtype: float64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2023-12-01, 2023-12-31]\n", "Loading close price from 2020-12-17\n", "Opening cached curve benchmark data from bbg, FILE:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20201217_marketclose.parquet\n"]}], "source": ["benchmarks_meta_data, bbg_bar_data = loader.load_close_px_for_period(dt.date(2023,12,1),dt.date(2023,12,31))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'effective_date': datetime.date(2020, 12, 17),\n", "  'benchmarks':        instrument_type maturities           ticker\n", "  0   OvernightIndexSwap         1W  USOSFR1Z Curncy\n", "  1   OvernightIndexSwap         2W  USOSFR2Z Curncy\n", "  2   OvernightIndexSwap         3W  USOSFR3Z Curncy\n", "  3   OvernightIndexSwap         1M   USOSFRA Curncy\n", "  4   OvernightIndexSwap         2M   USOSFRB Curncy\n", "  5   OvernightIndexSwap         3M   USOSFRC Curncy\n", "  6   OvernightIndexSwap        18M  USOSFR1F Curncy\n", "  7   OvernightIndexSwap         2Y   USOSFR2 Curncy\n", "  8   OvernightIndexSwap         3Y   USOSFR3 Curncy\n", "  9   OvernightIndexSwap         4Y   USOSFR4 Curncy\n", "  10  OvernightIndexSwap         5Y   USOSFR5 Curncy\n", "  11  OvernightIndexSwap         6Y   USOSFR6 Curncy\n", "  12  OvernightIndexSwap         7Y   USOSFR7 Curncy\n", "  13  OvernightIndexSwap         8Y   USOSFR8 Curncy\n", "  14  OvernightIndexSwap         9Y   USOSFR9 Curncy\n", "  15  OvernightIndexSwap        10Y  USOSFR10 Curncy\n", "  16  OvernightIndexSwap        11Y  USOSFR11 Curncy\n", "  17  OvernightIndexSwap        12Y  USOSFR12 Curncy\n", "  18  OvernightIndexSwap        15Y  USOSFR15 Curncy\n", "  19  OvernightIndexSwap        20Y  USOSFR20 Curncy\n", "  20  OvernightIndexSwap        25Y  USOSFR25 Curncy\n", "  21  OvernightIndexSwap        30Y  USOSFR30 Curncy\n", "  22  OvernightIndexSwap        40Y  USOSFR40 Curncy\n", "  23  OvernightIndexSwap        50Y  USOSFR50 Curncy\n", "  24     MeetingDateSwap         s1   USSOSR1 Curncy\n", "  25     MeetingDateSwap         s2   USSOSR2 Curncy\n", "  26     MeetingDateSwap         s3   USSOSR3 Curncy\n", "  27     MeetingDateSwap         s4   USSOSR4 Curncy\n", "  28     MeetingDateSwap         s5   USSOSR5 Curncy\n", "  29     MeetingDateSwap         s6   USSOSR6 Curncy\n", "  30     MeetingDateSwap         s7   USSOSR7 Curncy}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmarks_meta_data"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['USOSFR1Z Curncy', 'USOSFR2Z Curncy', 'USOSFR3Z Curncy',\n", "       'USOSFRA Curncy', 'USOSFRB Curncy', 'USOSFRC Curncy',\n", "       'USOSFR1F Curncy', 'USOSFR2 Curncy', 'USOSFR3 Curncy',\n", "       'USOSFR4 Curncy', 'USOSFR5 Curncy', 'USOSFR6 Curncy',\n", "       'USOSFR7 Curncy', 'USOSFR8 Curncy', 'USOSFR9 Curncy',\n", "       'USOSFR10 Curncy', 'USOSFR11 Curncy', 'USOSFR12 Curncy',\n", "       'USOSFR15 Curncy', 'USOSFR20 Curncy', 'USOSFR25 Curncy',\n", "       'USOSFR30 Curncy', 'USOSFR40 Curncy', 'USOSFR50 Curncy',\n", "       'USSOSR1 Curncy', 'USSOSR2 Curncy', 'USSOSR3 Curncy',\n", "       'USSOSR4 Curncy', 'USSOSR5 Curncy', 'USSOSR6 Curncy',\n", "       'USSOSR7 <PERSON>'], dtype=object)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmarks_meta_data[0][\"benchmarks\"][\"ticker\"].unique()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['USOSFR1Z Curncy', 'USOSFR2Z Curncy', 'USOSFR3Z Curncy',\n", "       'USOSFRA Curncy', 'USOSFRB Curncy', 'USOSFRC Curncy', 'USOSFR1F Curncy',\n", "       'USOSFR2 Curncy', 'USOSFR3 Curncy', 'USOSFR4 Curncy', 'USOSFR5 Curncy',\n", "       'USOSFR6 Curncy', 'USOSFR7 Curncy', 'USOSFR8 Curncy', 'USOSFR9 Curncy',\n", "       'USOSFR10 Curncy', 'USOSFR11 Curncy', 'USOSFR12 Curncy',\n", "       'USOSFR15 Curncy', 'USOSFR20 Curncy', 'USOSFR25 Curncy',\n", "       'USOSFR30 Curncy', 'USOSFR40 Curncy', 'USOSFR50 Curncy',\n", "       'USSOSR1 Curncy', 'USSOSR2 Curncy', 'USSOSR3 Curncy', 'USSOSR4 Curncy',\n", "       'USSOSR5 Curncy', 'USSOSR6 Curncy', 'USSOSR7 Curncy'],\n", "      dtype='object')"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data[0].columns"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-12-17</th>\n", "      <td>0.0708</td>\n", "      <td>0.0834</td>\n", "      <td>0.0767</td>\n", "      <td>0.0745</td>\n", "      <td>0.0700</td>\n", "      <td>0.0680</td>\n", "      <td>0.0616</td>\n", "      <td>0.0657</td>\n", "      <td>0.1020</td>\n", "      <td>0.1650</td>\n", "      <td>...</td>\n", "      <td>1.1634</td>\n", "      <td>1.1275</td>\n", "      <td>1.0457</td>\n", "      <td>0.0630</td>\n", "      <td>0.0515</td>\n", "      <td>0.0460</td>\n", "      <td>0.0530</td>\n", "      <td>0.0485</td>\n", "      <td>0.0660</td>\n", "      <td>0.0570</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-18</th>\n", "      <td>0.0712</td>\n", "      <td>0.0811</td>\n", "      <td>0.0753</td>\n", "      <td>0.0730</td>\n", "      <td>0.0627</td>\n", "      <td>0.0630</td>\n", "      <td>0.0593</td>\n", "      <td>0.0646</td>\n", "      <td>0.1014</td>\n", "      <td>0.1700</td>\n", "      <td>...</td>\n", "      <td>1.1790</td>\n", "      <td>1.1390</td>\n", "      <td>1.0560</td>\n", "      <td>0.0600</td>\n", "      <td>0.0515</td>\n", "      <td>0.0480</td>\n", "      <td>0.0495</td>\n", "      <td>0.0490</td>\n", "      <td>0.0570</td>\n", "      <td>0.0560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-21</th>\n", "      <td>0.0793</td>\n", "      <td>0.0880</td>\n", "      <td>0.0823</td>\n", "      <td>0.0770</td>\n", "      <td>0.0760</td>\n", "      <td>0.0630</td>\n", "      <td>0.0617</td>\n", "      <td>0.0655</td>\n", "      <td>0.0991</td>\n", "      <td>0.1717</td>\n", "      <td>...</td>\n", "      <td>1.1595</td>\n", "      <td>1.1208</td>\n", "      <td>1.0370</td>\n", "      <td>0.0623</td>\n", "      <td>0.0512</td>\n", "      <td>0.0450</td>\n", "      <td>0.0520</td>\n", "      <td>0.0513</td>\n", "      <td>0.0613</td>\n", "      <td>0.0583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-22</th>\n", "      <td>0.0800</td>\n", "      <td>0.0922</td>\n", "      <td>0.0950</td>\n", "      <td>0.0825</td>\n", "      <td>0.0745</td>\n", "      <td>0.0652</td>\n", "      <td>0.0610</td>\n", "      <td>0.0631</td>\n", "      <td>0.0984</td>\n", "      <td>0.1610</td>\n", "      <td>...</td>\n", "      <td>1.1467</td>\n", "      <td>1.1047</td>\n", "      <td>1.0215</td>\n", "      <td>0.0640</td>\n", "      <td>0.0580</td>\n", "      <td>0.0482</td>\n", "      <td>0.0512</td>\n", "      <td>0.0543</td>\n", "      <td>0.0628</td>\n", "      <td>0.0555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-23</th>\n", "      <td>0.0993</td>\n", "      <td>0.0851</td>\n", "      <td>0.0799</td>\n", "      <td>0.0653</td>\n", "      <td>0.0704</td>\n", "      <td>0.0680</td>\n", "      <td>0.0600</td>\n", "      <td>0.0642</td>\n", "      <td>0.1000</td>\n", "      <td>0.1744</td>\n", "      <td>...</td>\n", "      <td>1.1862</td>\n", "      <td>1.1370</td>\n", "      <td>1.0490</td>\n", "      <td>0.0653</td>\n", "      <td>0.0553</td>\n", "      <td>0.0498</td>\n", "      <td>0.0532</td>\n", "      <td>0.0553</td>\n", "      <td>0.0623</td>\n", "      <td>0.0608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-25</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.1370</td>\n", "      <td>3.9770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>5.3645</td>\n", "      <td>5.3563</td>\n", "      <td>5.3586</td>\n", "      <td>5.3580</td>\n", "      <td>5.3560</td>\n", "      <td>5.3476</td>\n", "      <td>4.4117</td>\n", "      <td>4.1390</td>\n", "      <td>3.8191</td>\n", "      <td>3.6648</td>\n", "      <td>...</td>\n", "      <td>3.3483</td>\n", "      <td>3.1435</td>\n", "      <td>2.9297</td>\n", "      <td>5.3350</td>\n", "      <td>5.1560</td>\n", "      <td>4.9060</td>\n", "      <td>4.6565</td>\n", "      <td>4.4375</td>\n", "      <td>4.1990</td>\n", "      <td>4.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>5.3670</td>\n", "      <td>5.3565</td>\n", "      <td>5.3575</td>\n", "      <td>5.3580</td>\n", "      <td>5.3515</td>\n", "      <td>5.3370</td>\n", "      <td>4.3543</td>\n", "      <td>4.0750</td>\n", "      <td>3.7441</td>\n", "      <td>3.5811</td>\n", "      <td>...</td>\n", "      <td>3.2630</td>\n", "      <td>3.0590</td>\n", "      <td>2.8432</td>\n", "      <td>5.3260</td>\n", "      <td>5.1230</td>\n", "      <td>4.8590</td>\n", "      <td>4.6015</td>\n", "      <td>4.3790</td>\n", "      <td>4.1375</td>\n", "      <td>3.9670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>5.3435</td>\n", "      <td>5.3450</td>\n", "      <td>5.3534</td>\n", "      <td>5.3523</td>\n", "      <td>5.3491</td>\n", "      <td>5.3295</td>\n", "      <td>4.3757</td>\n", "      <td>4.0964</td>\n", "      <td>3.7680</td>\n", "      <td>3.6109</td>\n", "      <td>...</td>\n", "      <td>3.2950</td>\n", "      <td>3.0900</td>\n", "      <td>2.8730</td>\n", "      <td>5.3310</td>\n", "      <td>5.1330</td>\n", "      <td>4.8770</td>\n", "      <td>4.6400</td>\n", "      <td>4.4225</td>\n", "      <td>4.1860</td>\n", "      <td>4.0160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>5.3405</td>\n", "      <td>5.3431</td>\n", "      <td>5.3460</td>\n", "      <td>5.3480</td>\n", "      <td>5.3455</td>\n", "      <td>5.3265</td>\n", "      <td>4.3465</td>\n", "      <td>4.0657</td>\n", "      <td>3.7482</td>\n", "      <td>3.6007</td>\n", "      <td>...</td>\n", "      <td>3.3161</td>\n", "      <td>3.1075</td>\n", "      <td>2.8937</td>\n", "      <td>5.3270</td>\n", "      <td>5.1320</td>\n", "      <td>4.8650</td>\n", "      <td>4.6190</td>\n", "      <td>4.3980</td>\n", "      <td>4.1610</td>\n", "      <td>3.9770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>792 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2020-12-17           0.0708           0.0834           0.0767          0.0745   \n", "2020-12-18           0.0712           0.0811           0.0753          0.0730   \n", "2020-12-21           0.0793           0.0880           0.0823          0.0770   \n", "2020-12-22           0.0800           0.0922           0.0950          0.0825   \n", "2020-12-23           0.0993           0.0851           0.0799          0.0653   \n", "...                     ...              ...              ...             ...   \n", "2023-12-25              NaN              NaN              NaN             NaN   \n", "2023-12-26           5.3645           5.3563           5.3586          5.3580   \n", "2023-12-27           5.3670           5.3565           5.3575          5.3580   \n", "2023-12-28           5.3435           5.3450           5.3534          5.3523   \n", "2023-12-29           5.3405           5.3431           5.3460          5.3480   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2020-12-17          0.0700          0.0680           0.0616          0.0657   \n", "2020-12-18          0.0627          0.0630           0.0593          0.0646   \n", "2020-12-21          0.0760          0.0630           0.0617          0.0655   \n", "2020-12-22          0.0745          0.0652           0.0610          0.0631   \n", "2020-12-23          0.0704          0.0680           0.0600          0.0642   \n", "...                    ...             ...              ...             ...   \n", "2023-12-25             NaN             NaN              NaN             NaN   \n", "2023-12-26          5.3560          5.3476           4.4117          4.1390   \n", "2023-12-27          5.3515          5.3370           4.3543          4.0750   \n", "2023-12-28          5.3491          5.3295           4.3757          4.0964   \n", "2023-12-29          5.3455          5.3265           4.3465          4.0657   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2020-12-17          0.1020          0.1650  ...           1.1634   \n", "2020-12-18          0.1014          0.1700  ...           1.1790   \n", "2020-12-21          0.0991          0.1717  ...           1.1595   \n", "2020-12-22          0.0984          0.1610  ...           1.1467   \n", "2020-12-23          0.1000          0.1744  ...           1.1862   \n", "...                    ...             ...  ...              ...   \n", "2023-12-25             NaN             NaN  ...              NaN   \n", "2023-12-26          3.8191          3.6648  ...           3.3483   \n", "2023-12-27          3.7441          3.5811  ...           3.2630   \n", "2023-12-28          3.7680          3.6109  ...           3.2950   \n", "2023-12-29          3.7482          3.6007  ...           3.3161   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2020-12-17           1.1275           1.0457          0.0630          0.0515   \n", "2020-12-18           1.1390           1.0560          0.0600          0.0515   \n", "2020-12-21           1.1208           1.0370          0.0623          0.0512   \n", "2020-12-22           1.1047           1.0215          0.0640          0.0580   \n", "2020-12-23           1.1370           1.0490          0.0653          0.0553   \n", "...                     ...              ...             ...             ...   \n", "2023-12-25              NaN              NaN             NaN             NaN   \n", "2023-12-26           3.1435           2.9297          5.3350          5.1560   \n", "2023-12-27           3.0590           2.8432          5.3260          5.1230   \n", "2023-12-28           3.0900           2.8730          5.3310          5.1330   \n", "2023-12-29           3.1075           2.8937          5.3270          5.1320   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2020-12-17          0.0460          0.0530          0.0485          0.0660   \n", "2020-12-18          0.0480          0.0495          0.0490          0.0570   \n", "2020-12-21          0.0450          0.0520          0.0513          0.0613   \n", "2020-12-22          0.0482          0.0512          0.0543          0.0628   \n", "2020-12-23          0.0498          0.0532          0.0553          0.0623   \n", "...                    ...             ...             ...             ...   \n", "2023-12-25             NaN             NaN             NaN          4.1370   \n", "2023-12-26          4.9060          4.6565          4.4375          4.1990   \n", "2023-12-27          4.8590          4.6015          4.3790          4.1375   \n", "2023-12-28          4.8770          4.6400          4.4225          4.1860   \n", "2023-12-29          4.8650          4.6190          4.3980          4.1610   \n", "\n", "            USSOSR7 Curncy  \n", "2020-12-17          0.0570  \n", "2020-12-18          0.0560  \n", "2020-12-21          0.0583  \n", "2020-12-22          0.0555  \n", "2020-12-23          0.0608  \n", "...                    ...  \n", "2023-12-25          3.9770  \n", "2023-12-26          4.0500  \n", "2023-12-27          3.9670  \n", "2023-12-28          4.0160  \n", "2023-12-29          3.9770  \n", "\n", "[792 rows x 31 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["data = bbg_bar_data[0][benchmarks_meta_data[0][\"benchmarks\"][\"ticker\"].unique()]\n", "data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2020-12-17</th>\n", "      <td>0.0708</td>\n", "      <td>0.0834</td>\n", "      <td>0.0767</td>\n", "      <td>0.0745</td>\n", "      <td>0.0700</td>\n", "      <td>0.0680</td>\n", "      <td>0.0616</td>\n", "      <td>0.0657</td>\n", "      <td>0.1020</td>\n", "      <td>0.1650</td>\n", "      <td>...</td>\n", "      <td>1.1634</td>\n", "      <td>1.1275</td>\n", "      <td>1.0457</td>\n", "      <td>0.0630</td>\n", "      <td>0.0515</td>\n", "      <td>0.0460</td>\n", "      <td>0.0530</td>\n", "      <td>0.0485</td>\n", "      <td>0.0660</td>\n", "      <td>0.0570</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-18</th>\n", "      <td>0.0712</td>\n", "      <td>0.0811</td>\n", "      <td>0.0753</td>\n", "      <td>0.0730</td>\n", "      <td>0.0627</td>\n", "      <td>0.0630</td>\n", "      <td>0.0593</td>\n", "      <td>0.0646</td>\n", "      <td>0.1014</td>\n", "      <td>0.1700</td>\n", "      <td>...</td>\n", "      <td>1.1790</td>\n", "      <td>1.1390</td>\n", "      <td>1.0560</td>\n", "      <td>0.0600</td>\n", "      <td>0.0515</td>\n", "      <td>0.0480</td>\n", "      <td>0.0495</td>\n", "      <td>0.0490</td>\n", "      <td>0.0570</td>\n", "      <td>0.0560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-21</th>\n", "      <td>0.0793</td>\n", "      <td>0.0880</td>\n", "      <td>0.0823</td>\n", "      <td>0.0770</td>\n", "      <td>0.0760</td>\n", "      <td>0.0630</td>\n", "      <td>0.0617</td>\n", "      <td>0.0655</td>\n", "      <td>0.0991</td>\n", "      <td>0.1717</td>\n", "      <td>...</td>\n", "      <td>1.1595</td>\n", "      <td>1.1208</td>\n", "      <td>1.0370</td>\n", "      <td>0.0623</td>\n", "      <td>0.0512</td>\n", "      <td>0.0450</td>\n", "      <td>0.0520</td>\n", "      <td>0.0513</td>\n", "      <td>0.0613</td>\n", "      <td>0.0583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-22</th>\n", "      <td>0.0800</td>\n", "      <td>0.0922</td>\n", "      <td>0.0950</td>\n", "      <td>0.0825</td>\n", "      <td>0.0745</td>\n", "      <td>0.0652</td>\n", "      <td>0.0610</td>\n", "      <td>0.0631</td>\n", "      <td>0.0984</td>\n", "      <td>0.1610</td>\n", "      <td>...</td>\n", "      <td>1.1467</td>\n", "      <td>1.1047</td>\n", "      <td>1.0215</td>\n", "      <td>0.0640</td>\n", "      <td>0.0580</td>\n", "      <td>0.0482</td>\n", "      <td>0.0512</td>\n", "      <td>0.0543</td>\n", "      <td>0.0628</td>\n", "      <td>0.0555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2020-12-23</th>\n", "      <td>0.0993</td>\n", "      <td>0.0851</td>\n", "      <td>0.0799</td>\n", "      <td>0.0653</td>\n", "      <td>0.0704</td>\n", "      <td>0.0680</td>\n", "      <td>0.0600</td>\n", "      <td>0.0642</td>\n", "      <td>0.1000</td>\n", "      <td>0.1744</td>\n", "      <td>...</td>\n", "      <td>1.1862</td>\n", "      <td>1.1370</td>\n", "      <td>1.0490</td>\n", "      <td>0.0653</td>\n", "      <td>0.0553</td>\n", "      <td>0.0498</td>\n", "      <td>0.0532</td>\n", "      <td>0.0553</td>\n", "      <td>0.0623</td>\n", "      <td>0.0608</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-22</th>\n", "      <td>5.3575</td>\n", "      <td>5.3540</td>\n", "      <td>5.3585</td>\n", "      <td>5.3580</td>\n", "      <td>5.3555</td>\n", "      <td>5.3472</td>\n", "      <td>4.3750</td>\n", "      <td>4.1035</td>\n", "      <td>3.7950</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.3560</td>\n", "      <td>3.1510</td>\n", "      <td>2.9460</td>\n", "      <td>5.3340</td>\n", "      <td>5.1260</td>\n", "      <td>4.8495</td>\n", "      <td>4.5920</td>\n", "      <td>4.3650</td>\n", "      <td>4.1460</td>\n", "      <td>3.9835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>5.3645</td>\n", "      <td>5.3563</td>\n", "      <td>5.3586</td>\n", "      <td>5.3580</td>\n", "      <td>5.3560</td>\n", "      <td>5.3476</td>\n", "      <td>4.4117</td>\n", "      <td>4.1390</td>\n", "      <td>3.8191</td>\n", "      <td>3.6648</td>\n", "      <td>...</td>\n", "      <td>3.3483</td>\n", "      <td>3.1435</td>\n", "      <td>2.9297</td>\n", "      <td>5.3350</td>\n", "      <td>5.1560</td>\n", "      <td>4.9060</td>\n", "      <td>4.6565</td>\n", "      <td>4.4375</td>\n", "      <td>4.1990</td>\n", "      <td>4.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>5.3670</td>\n", "      <td>5.3565</td>\n", "      <td>5.3575</td>\n", "      <td>5.3580</td>\n", "      <td>5.3515</td>\n", "      <td>5.3370</td>\n", "      <td>4.3543</td>\n", "      <td>4.0750</td>\n", "      <td>3.7441</td>\n", "      <td>3.5811</td>\n", "      <td>...</td>\n", "      <td>3.2630</td>\n", "      <td>3.0590</td>\n", "      <td>2.8432</td>\n", "      <td>5.3260</td>\n", "      <td>5.1230</td>\n", "      <td>4.8590</td>\n", "      <td>4.6015</td>\n", "      <td>4.3790</td>\n", "      <td>4.1375</td>\n", "      <td>3.9670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>5.3435</td>\n", "      <td>5.3450</td>\n", "      <td>5.3534</td>\n", "      <td>5.3523</td>\n", "      <td>5.3491</td>\n", "      <td>5.3295</td>\n", "      <td>4.3757</td>\n", "      <td>4.0964</td>\n", "      <td>3.7680</td>\n", "      <td>3.6109</td>\n", "      <td>...</td>\n", "      <td>3.2950</td>\n", "      <td>3.0900</td>\n", "      <td>2.8730</td>\n", "      <td>5.3310</td>\n", "      <td>5.1330</td>\n", "      <td>4.8770</td>\n", "      <td>4.6400</td>\n", "      <td>4.4225</td>\n", "      <td>4.1860</td>\n", "      <td>4.0160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>5.3405</td>\n", "      <td>5.3431</td>\n", "      <td>5.3460</td>\n", "      <td>5.3480</td>\n", "      <td>5.3455</td>\n", "      <td>5.3265</td>\n", "      <td>4.3465</td>\n", "      <td>4.0657</td>\n", "      <td>3.7482</td>\n", "      <td>3.6007</td>\n", "      <td>...</td>\n", "      <td>3.3161</td>\n", "      <td>3.1075</td>\n", "      <td>2.8937</td>\n", "      <td>5.3270</td>\n", "      <td>5.1320</td>\n", "      <td>4.8650</td>\n", "      <td>4.6190</td>\n", "      <td>4.3980</td>\n", "      <td>4.1610</td>\n", "      <td>3.9770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>543 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2020-12-17           0.0708           0.0834           0.0767          0.0745   \n", "2020-12-18           0.0712           0.0811           0.0753          0.0730   \n", "2020-12-21           0.0793           0.0880           0.0823          0.0770   \n", "2020-12-22           0.0800           0.0922           0.0950          0.0825   \n", "2020-12-23           0.0993           0.0851           0.0799          0.0653   \n", "...                     ...              ...              ...             ...   \n", "2023-12-22           5.3575           5.3540           5.3585          5.3580   \n", "2023-12-26           5.3645           5.3563           5.3586          5.3580   \n", "2023-12-27           5.3670           5.3565           5.3575          5.3580   \n", "2023-12-28           5.3435           5.3450           5.3534          5.3523   \n", "2023-12-29           5.3405           5.3431           5.3460          5.3480   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2020-12-17          0.0700          0.0680           0.0616          0.0657   \n", "2020-12-18          0.0627          0.0630           0.0593          0.0646   \n", "2020-12-21          0.0760          0.0630           0.0617          0.0655   \n", "2020-12-22          0.0745          0.0652           0.0610          0.0631   \n", "2020-12-23          0.0704          0.0680           0.0600          0.0642   \n", "...                    ...             ...              ...             ...   \n", "2023-12-22          5.3555          5.3472           4.3750          4.1035   \n", "2023-12-26          5.3560          5.3476           4.4117          4.1390   \n", "2023-12-27          5.3515          5.3370           4.3543          4.0750   \n", "2023-12-28          5.3491          5.3295           4.3757          4.0964   \n", "2023-12-29          5.3455          5.3265           4.3465          4.0657   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2020-12-17          0.1020          0.1650  ...           1.1634   \n", "2020-12-18          0.1014          0.1700  ...           1.1790   \n", "2020-12-21          0.0991          0.1717  ...           1.1595   \n", "2020-12-22          0.0984          0.1610  ...           1.1467   \n", "2020-12-23          0.1000          0.1744  ...           1.1862   \n", "...                    ...             ...  ...              ...   \n", "2023-12-22          3.7950          3.6452  ...           3.3560   \n", "2023-12-26          3.8191          3.6648  ...           3.3483   \n", "2023-12-27          3.7441          3.5811  ...           3.2630   \n", "2023-12-28          3.7680          3.6109  ...           3.2950   \n", "2023-12-29          3.7482          3.6007  ...           3.3161   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2020-12-17           1.1275           1.0457          0.0630          0.0515   \n", "2020-12-18           1.1390           1.0560          0.0600          0.0515   \n", "2020-12-21           1.1208           1.0370          0.0623          0.0512   \n", "2020-12-22           1.1047           1.0215          0.0640          0.0580   \n", "2020-12-23           1.1370           1.0490          0.0653          0.0553   \n", "...                     ...              ...             ...             ...   \n", "2023-12-22           3.1510           2.9460          5.3340          5.1260   \n", "2023-12-26           3.1435           2.9297          5.3350          5.1560   \n", "2023-12-27           3.0590           2.8432          5.3260          5.1230   \n", "2023-12-28           3.0900           2.8730          5.3310          5.1330   \n", "2023-12-29           3.1075           2.8937          5.3270          5.1320   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2020-12-17          0.0460          0.0530          0.0485          0.0660   \n", "2020-12-18          0.0480          0.0495          0.0490          0.0570   \n", "2020-12-21          0.0450          0.0520          0.0513          0.0613   \n", "2020-12-22          0.0482          0.0512          0.0543          0.0628   \n", "2020-12-23          0.0498          0.0532          0.0553          0.0623   \n", "...                    ...             ...             ...             ...   \n", "2023-12-22          4.8495          4.5920          4.3650          4.1460   \n", "2023-12-26          4.9060          4.6565          4.4375          4.1990   \n", "2023-12-27          4.8590          4.6015          4.3790          4.1375   \n", "2023-12-28          4.8770          4.6400          4.4225          4.1860   \n", "2023-12-29          4.8650          4.6190          4.3980          4.1610   \n", "\n", "            USSOSR7 Curncy  \n", "2020-12-17          0.0570  \n", "2020-12-18          0.0560  \n", "2020-12-21          0.0583  \n", "2020-12-22          0.0555  \n", "2020-12-23          0.0608  \n", "...                    ...  \n", "2023-12-22          3.9835  \n", "2023-12-26          4.0500  \n", "2023-12-27          3.9670  \n", "2023-12-28          4.0160  \n", "2023-12-29          3.9770  \n", "\n", "[543 rows x 31 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["data = data.dropna(axis=0, how=\"any\")\n", "data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-04</th>\n", "      <td>5.3324</td>\n", "      <td>5.3412</td>\n", "      <td>5.3457</td>\n", "      <td>5.3610</td>\n", "      <td>5.3732</td>\n", "      <td>5.3768</td>\n", "      <td>4.6921</td>\n", "      <td>4.4365</td>\n", "      <td>4.1323</td>\n", "      <td>3.9834</td>\n", "      <td>...</td>\n", "      <td>3.7392</td>\n", "      <td>3.5390</td>\n", "      <td>3.3326</td>\n", "      <td>5.375</td>\n", "      <td>5.3445</td>\n", "      <td>5.2170</td>\n", "      <td>5.0450</td>\n", "      <td>4.8600</td>\n", "      <td>4.6730</td>\n", "      <td>4.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-05</th>\n", "      <td>5.3264</td>\n", "      <td>5.3491</td>\n", "      <td>5.3545</td>\n", "      <td>5.3640</td>\n", "      <td>5.3741</td>\n", "      <td>5.3766</td>\n", "      <td>4.6540</td>\n", "      <td>4.3900</td>\n", "      <td>4.0730</td>\n", "      <td>3.9187</td>\n", "      <td>...</td>\n", "      <td>3.6326</td>\n", "      <td>3.4346</td>\n", "      <td>3.2316</td>\n", "      <td>5.376</td>\n", "      <td>5.3420</td>\n", "      <td>5.2005</td>\n", "      <td>5.0150</td>\n", "      <td>4.8300</td>\n", "      <td>4.6420</td>\n", "      <td>4.4370</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-06</th>\n", "      <td>5.3234</td>\n", "      <td>5.3381</td>\n", "      <td>5.3475</td>\n", "      <td>5.3520</td>\n", "      <td>5.3634</td>\n", "      <td>5.3680</td>\n", "      <td>4.6751</td>\n", "      <td>4.4064</td>\n", "      <td>4.0683</td>\n", "      <td>3.8979</td>\n", "      <td>...</td>\n", "      <td>3.5523</td>\n", "      <td>3.3535</td>\n", "      <td>3.1522</td>\n", "      <td>5.365</td>\n", "      <td>5.3350</td>\n", "      <td>5.2070</td>\n", "      <td>5.0290</td>\n", "      <td>4.8540</td>\n", "      <td>4.6740</td>\n", "      <td>4.4790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-07</th>\n", "      <td>5.3269</td>\n", "      <td>5.3447</td>\n", "      <td>5.3510</td>\n", "      <td>5.3562</td>\n", "      <td>5.3657</td>\n", "      <td>5.3707</td>\n", "      <td>4.6664</td>\n", "      <td>4.4042</td>\n", "      <td>4.0813</td>\n", "      <td>3.9200</td>\n", "      <td>...</td>\n", "      <td>3.5893</td>\n", "      <td>3.3900</td>\n", "      <td>3.1897</td>\n", "      <td>5.364</td>\n", "      <td>5.3400</td>\n", "      <td>5.2110</td>\n", "      <td>5.0310</td>\n", "      <td>4.8480</td>\n", "      <td>4.6660</td>\n", "      <td>4.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-08</th>\n", "      <td>5.3255</td>\n", "      <td>5.3450</td>\n", "      <td>5.3475</td>\n", "      <td>5.3589</td>\n", "      <td>5.3714</td>\n", "      <td>5.3824</td>\n", "      <td>4.7739</td>\n", "      <td>4.5230</td>\n", "      <td>4.2011</td>\n", "      <td>4.0349</td>\n", "      <td>...</td>\n", "      <td>3.6357</td>\n", "      <td>3.4346</td>\n", "      <td>3.2363</td>\n", "      <td>5.365</td>\n", "      <td>5.3640</td>\n", "      <td>5.2670</td>\n", "      <td>5.1120</td>\n", "      <td>4.9480</td>\n", "      <td>4.7890</td>\n", "      <td>4.6050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-11</th>\n", "      <td>5.3485</td>\n", "      <td>5.3535</td>\n", "      <td>5.3570</td>\n", "      <td>5.3620</td>\n", "      <td>5.3737</td>\n", "      <td>5.3849</td>\n", "      <td>4.7691</td>\n", "      <td>4.5181</td>\n", "      <td>4.2050</td>\n", "      <td>4.0418</td>\n", "      <td>...</td>\n", "      <td>3.6472</td>\n", "      <td>3.4472</td>\n", "      <td>3.2475</td>\n", "      <td>5.371</td>\n", "      <td>5.3685</td>\n", "      <td>5.2680</td>\n", "      <td>5.1140</td>\n", "      <td>4.9550</td>\n", "      <td>4.7820</td>\n", "      <td>4.5995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-12</th>\n", "      <td>5.3493</td>\n", "      <td>5.3520</td>\n", "      <td>5.3540</td>\n", "      <td>5.3605</td>\n", "      <td>5.3704</td>\n", "      <td>5.3830</td>\n", "      <td>4.7815</td>\n", "      <td>4.5327</td>\n", "      <td>4.2027</td>\n", "      <td>4.0280</td>\n", "      <td>...</td>\n", "      <td>3.6198</td>\n", "      <td>3.4197</td>\n", "      <td>3.2149</td>\n", "      <td>5.367</td>\n", "      <td>5.3670</td>\n", "      <td>5.2760</td>\n", "      <td>5.1290</td>\n", "      <td>4.9680</td>\n", "      <td>4.8040</td>\n", "      <td>4.6240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-13</th>\n", "      <td>5.3475</td>\n", "      <td>5.3505</td>\n", "      <td>5.3514</td>\n", "      <td>5.3571</td>\n", "      <td>5.3627</td>\n", "      <td>5.3680</td>\n", "      <td>4.5080</td>\n", "      <td>4.2451</td>\n", "      <td>3.9335</td>\n", "      <td>3.7785</td>\n", "      <td>...</td>\n", "      <td>3.4940</td>\n", "      <td>3.2965</td>\n", "      <td>3.0973</td>\n", "      <td>5.363</td>\n", "      <td>5.3410</td>\n", "      <td>5.1550</td>\n", "      <td>4.9160</td>\n", "      <td>4.6900</td>\n", "      <td>4.4820</td>\n", "      <td>4.2795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-14</th>\n", "      <td>5.3465</td>\n", "      <td>5.3505</td>\n", "      <td>5.3538</td>\n", "      <td>5.3552</td>\n", "      <td>5.3600</td>\n", "      <td>5.3674</td>\n", "      <td>4.4872</td>\n", "      <td>4.2060</td>\n", "      <td>3.8828</td>\n", "      <td>3.7208</td>\n", "      <td>...</td>\n", "      <td>3.3515</td>\n", "      <td>3.1510</td>\n", "      <td>2.9530</td>\n", "      <td>5.338</td>\n", "      <td>5.1670</td>\n", "      <td>4.9435</td>\n", "      <td>4.7065</td>\n", "      <td>4.4970</td>\n", "      <td>4.2610</td>\n", "      <td>4.0785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-15</th>\n", "      <td>5.3454</td>\n", "      <td>5.3530</td>\n", "      <td>5.3556</td>\n", "      <td>5.3570</td>\n", "      <td>5.3640</td>\n", "      <td>5.3716</td>\n", "      <td>4.5353</td>\n", "      <td>4.2535</td>\n", "      <td>3.9109</td>\n", "      <td>3.7346</td>\n", "      <td>...</td>\n", "      <td>3.3256</td>\n", "      <td>3.1247</td>\n", "      <td>2.9240</td>\n", "      <td>5.348</td>\n", "      <td>5.1920</td>\n", "      <td>4.9830</td>\n", "      <td>4.7610</td>\n", "      <td>4.5580</td>\n", "      <td>4.3330</td>\n", "      <td>4.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-18</th>\n", "      <td>5.3427</td>\n", "      <td>5.3510</td>\n", "      <td>5.3527</td>\n", "      <td>5.3573</td>\n", "      <td>5.3634</td>\n", "      <td>5.3728</td>\n", "      <td>4.5350</td>\n", "      <td>4.2556</td>\n", "      <td>3.9235</td>\n", "      <td>3.7511</td>\n", "      <td>...</td>\n", "      <td>3.3550</td>\n", "      <td>3.1535</td>\n", "      <td>2.9500</td>\n", "      <td>5.353</td>\n", "      <td>5.1940</td>\n", "      <td>4.9770</td>\n", "      <td>4.7630</td>\n", "      <td>4.5540</td>\n", "      <td>4.3380</td>\n", "      <td>4.1450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-19</th>\n", "      <td>5.3434</td>\n", "      <td>5.3505</td>\n", "      <td>5.3545</td>\n", "      <td>5.3575</td>\n", "      <td>5.3635</td>\n", "      <td>5.3723</td>\n", "      <td>4.5114</td>\n", "      <td>4.2432</td>\n", "      <td>3.9185</td>\n", "      <td>3.7496</td>\n", "      <td>...</td>\n", "      <td>3.3466</td>\n", "      <td>3.1416</td>\n", "      <td>2.9416</td>\n", "      <td>5.350</td>\n", "      <td>5.1810</td>\n", "      <td>4.9485</td>\n", "      <td>4.7230</td>\n", "      <td>4.5200</td>\n", "      <td>4.3010</td>\n", "      <td>4.1220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-20</th>\n", "      <td>5.3337</td>\n", "      <td>5.3495</td>\n", "      <td>5.3510</td>\n", "      <td>5.3548</td>\n", "      <td>5.3570</td>\n", "      <td>5.3602</td>\n", "      <td>4.4051</td>\n", "      <td>4.1317</td>\n", "      <td>3.8063</td>\n", "      <td>3.6432</td>\n", "      <td>...</td>\n", "      <td>3.2890</td>\n", "      <td>3.0828</td>\n", "      <td>2.8780</td>\n", "      <td>5.337</td>\n", "      <td>5.1350</td>\n", "      <td>4.8710</td>\n", "      <td>4.6100</td>\n", "      <td>4.3880</td>\n", "      <td>4.1450</td>\n", "      <td>3.9800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-21</th>\n", "      <td>5.3581</td>\n", "      <td>5.3505</td>\n", "      <td>5.3560</td>\n", "      <td>5.3568</td>\n", "      <td>5.3568</td>\n", "      <td>5.3513</td>\n", "      <td>4.4046</td>\n", "      <td>4.1310</td>\n", "      <td>3.8115</td>\n", "      <td>3.6540</td>\n", "      <td>...</td>\n", "      <td>3.3274</td>\n", "      <td>3.1235</td>\n", "      <td>2.9122</td>\n", "      <td>5.336</td>\n", "      <td>5.1460</td>\n", "      <td>4.8870</td>\n", "      <td>4.6320</td>\n", "      <td>4.4040</td>\n", "      <td>4.1720</td>\n", "      <td>4.0115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-22</th>\n", "      <td>5.3575</td>\n", "      <td>5.3540</td>\n", "      <td>5.3585</td>\n", "      <td>5.3580</td>\n", "      <td>5.3555</td>\n", "      <td>5.3472</td>\n", "      <td>4.3750</td>\n", "      <td>4.1035</td>\n", "      <td>3.7950</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.3560</td>\n", "      <td>3.1510</td>\n", "      <td>2.9460</td>\n", "      <td>5.334</td>\n", "      <td>5.1260</td>\n", "      <td>4.8495</td>\n", "      <td>4.5920</td>\n", "      <td>4.3650</td>\n", "      <td>4.1460</td>\n", "      <td>3.9835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>5.3645</td>\n", "      <td>5.3563</td>\n", "      <td>5.3586</td>\n", "      <td>5.3580</td>\n", "      <td>5.3560</td>\n", "      <td>5.3476</td>\n", "      <td>4.4117</td>\n", "      <td>4.1390</td>\n", "      <td>3.8191</td>\n", "      <td>3.6648</td>\n", "      <td>...</td>\n", "      <td>3.3483</td>\n", "      <td>3.1435</td>\n", "      <td>2.9297</td>\n", "      <td>5.335</td>\n", "      <td>5.1560</td>\n", "      <td>4.9060</td>\n", "      <td>4.6565</td>\n", "      <td>4.4375</td>\n", "      <td>4.1990</td>\n", "      <td>4.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>5.3670</td>\n", "      <td>5.3565</td>\n", "      <td>5.3575</td>\n", "      <td>5.3580</td>\n", "      <td>5.3515</td>\n", "      <td>5.3370</td>\n", "      <td>4.3543</td>\n", "      <td>4.0750</td>\n", "      <td>3.7441</td>\n", "      <td>3.5811</td>\n", "      <td>...</td>\n", "      <td>3.2630</td>\n", "      <td>3.0590</td>\n", "      <td>2.8432</td>\n", "      <td>5.326</td>\n", "      <td>5.1230</td>\n", "      <td>4.8590</td>\n", "      <td>4.6015</td>\n", "      <td>4.3790</td>\n", "      <td>4.1375</td>\n", "      <td>3.9670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>5.3435</td>\n", "      <td>5.3450</td>\n", "      <td>5.3534</td>\n", "      <td>5.3523</td>\n", "      <td>5.3491</td>\n", "      <td>5.3295</td>\n", "      <td>4.3757</td>\n", "      <td>4.0964</td>\n", "      <td>3.7680</td>\n", "      <td>3.6109</td>\n", "      <td>...</td>\n", "      <td>3.2950</td>\n", "      <td>3.0900</td>\n", "      <td>2.8730</td>\n", "      <td>5.331</td>\n", "      <td>5.1330</td>\n", "      <td>4.8770</td>\n", "      <td>4.6400</td>\n", "      <td>4.4225</td>\n", "      <td>4.1860</td>\n", "      <td>4.0160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>5.3405</td>\n", "      <td>5.3431</td>\n", "      <td>5.3460</td>\n", "      <td>5.3480</td>\n", "      <td>5.3455</td>\n", "      <td>5.3265</td>\n", "      <td>4.3465</td>\n", "      <td>4.0657</td>\n", "      <td>3.7482</td>\n", "      <td>3.6007</td>\n", "      <td>...</td>\n", "      <td>3.3161</td>\n", "      <td>3.1075</td>\n", "      <td>2.8937</td>\n", "      <td>5.327</td>\n", "      <td>5.1320</td>\n", "      <td>4.8650</td>\n", "      <td>4.6190</td>\n", "      <td>4.3980</td>\n", "      <td>4.1610</td>\n", "      <td>3.9770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>19 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2023-12-04           5.3324           5.3412           5.3457          5.3610   \n", "2023-12-05           5.3264           5.3491           5.3545          5.3640   \n", "2023-12-06           5.3234           5.3381           5.3475          5.3520   \n", "2023-12-07           5.3269           5.3447           5.3510          5.3562   \n", "2023-12-08           5.3255           5.3450           5.3475          5.3589   \n", "2023-12-11           5.3485           5.3535           5.3570          5.3620   \n", "2023-12-12           5.3493           5.3520           5.3540          5.3605   \n", "2023-12-13           5.3475           5.3505           5.3514          5.3571   \n", "2023-12-14           5.3465           5.3505           5.3538          5.3552   \n", "2023-12-15           5.3454           5.3530           5.3556          5.3570   \n", "2023-12-18           5.3427           5.3510           5.3527          5.3573   \n", "2023-12-19           5.3434           5.3505           5.3545          5.3575   \n", "2023-12-20           5.3337           5.3495           5.3510          5.3548   \n", "2023-12-21           5.3581           5.3505           5.3560          5.3568   \n", "2023-12-22           5.3575           5.3540           5.3585          5.3580   \n", "2023-12-26           5.3645           5.3563           5.3586          5.3580   \n", "2023-12-27           5.3670           5.3565           5.3575          5.3580   \n", "2023-12-28           5.3435           5.3450           5.3534          5.3523   \n", "2023-12-29           5.3405           5.3431           5.3460          5.3480   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2023-12-04          5.3732          5.3768           4.6921          4.4365   \n", "2023-12-05          5.3741          5.3766           4.6540          4.3900   \n", "2023-12-06          5.3634          5.3680           4.6751          4.4064   \n", "2023-12-07          5.3657          5.3707           4.6664          4.4042   \n", "2023-12-08          5.3714          5.3824           4.7739          4.5230   \n", "2023-12-11          5.3737          5.3849           4.7691          4.5181   \n", "2023-12-12          5.3704          5.3830           4.7815          4.5327   \n", "2023-12-13          5.3627          5.3680           4.5080          4.2451   \n", "2023-12-14          5.3600          5.3674           4.4872          4.2060   \n", "2023-12-15          5.3640          5.3716           4.5353          4.2535   \n", "2023-12-18          5.3634          5.3728           4.5350          4.2556   \n", "2023-12-19          5.3635          5.3723           4.5114          4.2432   \n", "2023-12-20          5.3570          5.3602           4.4051          4.1317   \n", "2023-12-21          5.3568          5.3513           4.4046          4.1310   \n", "2023-12-22          5.3555          5.3472           4.3750          4.1035   \n", "2023-12-26          5.3560          5.3476           4.4117          4.1390   \n", "2023-12-27          5.3515          5.3370           4.3543          4.0750   \n", "2023-12-28          5.3491          5.3295           4.3757          4.0964   \n", "2023-12-29          5.3455          5.3265           4.3465          4.0657   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2023-12-04          4.1323          3.9834  ...           3.7392   \n", "2023-12-05          4.0730          3.9187  ...           3.6326   \n", "2023-12-06          4.0683          3.8979  ...           3.5523   \n", "2023-12-07          4.0813          3.9200  ...           3.5893   \n", "2023-12-08          4.2011          4.0349  ...           3.6357   \n", "2023-12-11          4.2050          4.0418  ...           3.6472   \n", "2023-12-12          4.2027          4.0280  ...           3.6198   \n", "2023-12-13          3.9335          3.7785  ...           3.4940   \n", "2023-12-14          3.8828          3.7208  ...           3.3515   \n", "2023-12-15          3.9109          3.7346  ...           3.3256   \n", "2023-12-18          3.9235          3.7511  ...           3.3550   \n", "2023-12-19          3.9185          3.7496  ...           3.3466   \n", "2023-12-20          3.8063          3.6432  ...           3.2890   \n", "2023-12-21          3.8115          3.6540  ...           3.3274   \n", "2023-12-22          3.7950          3.6452  ...           3.3560   \n", "2023-12-26          3.8191          3.6648  ...           3.3483   \n", "2023-12-27          3.7441          3.5811  ...           3.2630   \n", "2023-12-28          3.7680          3.6109  ...           3.2950   \n", "2023-12-29          3.7482          3.6007  ...           3.3161   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2023-12-04           3.5390           3.3326           5.375          5.3445   \n", "2023-12-05           3.4346           3.2316           5.376          5.3420   \n", "2023-12-06           3.3535           3.1522           5.365          5.3350   \n", "2023-12-07           3.3900           3.1897           5.364          5.3400   \n", "2023-12-08           3.4346           3.2363           5.365          5.3640   \n", "2023-12-11           3.4472           3.2475           5.371          5.3685   \n", "2023-12-12           3.4197           3.2149           5.367          5.3670   \n", "2023-12-13           3.2965           3.0973           5.363          5.3410   \n", "2023-12-14           3.1510           2.9530           5.338          5.1670   \n", "2023-12-15           3.1247           2.9240           5.348          5.1920   \n", "2023-12-18           3.1535           2.9500           5.353          5.1940   \n", "2023-12-19           3.1416           2.9416           5.350          5.1810   \n", "2023-12-20           3.0828           2.8780           5.337          5.1350   \n", "2023-12-21           3.1235           2.9122           5.336          5.1460   \n", "2023-12-22           3.1510           2.9460           5.334          5.1260   \n", "2023-12-26           3.1435           2.9297           5.335          5.1560   \n", "2023-12-27           3.0590           2.8432           5.326          5.1230   \n", "2023-12-28           3.0900           2.8730           5.331          5.1330   \n", "2023-12-29           3.1075           2.8937           5.327          5.1320   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2023-12-04          5.2170          5.0450          4.8600          4.6730   \n", "2023-12-05          5.2005          5.0150          4.8300          4.6420   \n", "2023-12-06          5.2070          5.0290          4.8540          4.6740   \n", "2023-12-07          5.2110          5.0310          4.8480          4.6660   \n", "2023-12-08          5.2670          5.1120          4.9480          4.7890   \n", "2023-12-11          5.2680          5.1140          4.9550          4.7820   \n", "2023-12-12          5.2760          5.1290          4.9680          4.8040   \n", "2023-12-13          5.1550          4.9160          4.6900          4.4820   \n", "2023-12-14          4.9435          4.7065          4.4970          4.2610   \n", "2023-12-15          4.9830          4.7610          4.5580          4.3330   \n", "2023-12-18          4.9770          4.7630          4.5540          4.3380   \n", "2023-12-19          4.9485          4.7230          4.5200          4.3010   \n", "2023-12-20          4.8710          4.6100          4.3880          4.1450   \n", "2023-12-21          4.8870          4.6320          4.4040          4.1720   \n", "2023-12-22          4.8495          4.5920          4.3650          4.1460   \n", "2023-12-26          4.9060          4.6565          4.4375          4.1990   \n", "2023-12-27          4.8590          4.6015          4.3790          4.1375   \n", "2023-12-28          4.8770          4.6400          4.4225          4.1860   \n", "2023-12-29          4.8650          4.6190          4.3980          4.1610   \n", "\n", "            USSOSR7 Curncy  \n", "2023-12-04          4.4750  \n", "2023-12-05          4.4370  \n", "2023-12-06          4.4790  \n", "2023-12-07          4.4750  \n", "2023-12-08          4.6050  \n", "2023-12-11          4.5995  \n", "2023-12-12          4.6240  \n", "2023-12-13          4.2795  \n", "2023-12-14          4.0785  \n", "2023-12-15          4.1500  \n", "2023-12-18          4.1450  \n", "2023-12-19          4.1220  \n", "2023-12-20          3.9800  \n", "2023-12-21          4.0115  \n", "2023-12-22          3.9835  \n", "2023-12-26          4.0500  \n", "2023-12-27          3.9670  \n", "2023-12-28          4.0160  \n", "2023-12-29          3.9770  \n", "\n", "[19 rows x 31 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data = data[(data.index > dt.date(2023,12,1))]\n", "data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-04</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-05</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-06</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-07</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-08</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-11</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-12</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-13</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-14</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-15</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-18</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-19</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-20</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-21</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-22</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: []\n", "Index: [2023-12-04, 2023-12-05, 2023-12-06, 2023-12-07, 2023-12-08, 2023-12-11, 2023-12-12, 2023-12-13, 2023-12-14, 2023-12-15, 2023-12-18, 2023-12-19, 2023-12-20, 2023-12-21, 2023-12-22, 2023-12-26, 2023-12-27, 2023-12-28, 2023-12-29]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["calib = pd.DataFrame(index=data.index)\n", "calib"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["def simple_func(row):\n", "    return [row.name.year, row.name.day] + [float(row.name.year), float(row.name.month)]"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-04</th>\n", "      <td>5.3324</td>\n", "      <td>5.3412</td>\n", "      <td>5.3457</td>\n", "      <td>5.3610</td>\n", "      <td>5.3732</td>\n", "      <td>5.3768</td>\n", "      <td>4.6921</td>\n", "      <td>4.4365</td>\n", "      <td>4.1323</td>\n", "      <td>3.9834</td>\n", "      <td>...</td>\n", "      <td>3.7392</td>\n", "      <td>3.5390</td>\n", "      <td>3.3326</td>\n", "      <td>5.375</td>\n", "      <td>5.3445</td>\n", "      <td>5.2170</td>\n", "      <td>5.0450</td>\n", "      <td>4.8600</td>\n", "      <td>4.6730</td>\n", "      <td>4.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-05</th>\n", "      <td>5.3264</td>\n", "      <td>5.3491</td>\n", "      <td>5.3545</td>\n", "      <td>5.3640</td>\n", "      <td>5.3741</td>\n", "      <td>5.3766</td>\n", "      <td>4.6540</td>\n", "      <td>4.3900</td>\n", "      <td>4.0730</td>\n", "      <td>3.9187</td>\n", "      <td>...</td>\n", "      <td>3.6326</td>\n", "      <td>3.4346</td>\n", "      <td>3.2316</td>\n", "      <td>5.376</td>\n", "      <td>5.3420</td>\n", "      <td>5.2005</td>\n", "      <td>5.0150</td>\n", "      <td>4.8300</td>\n", "      <td>4.6420</td>\n", "      <td>4.4370</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-06</th>\n", "      <td>5.3234</td>\n", "      <td>5.3381</td>\n", "      <td>5.3475</td>\n", "      <td>5.3520</td>\n", "      <td>5.3634</td>\n", "      <td>5.3680</td>\n", "      <td>4.6751</td>\n", "      <td>4.4064</td>\n", "      <td>4.0683</td>\n", "      <td>3.8979</td>\n", "      <td>...</td>\n", "      <td>3.5523</td>\n", "      <td>3.3535</td>\n", "      <td>3.1522</td>\n", "      <td>5.365</td>\n", "      <td>5.3350</td>\n", "      <td>5.2070</td>\n", "      <td>5.0290</td>\n", "      <td>4.8540</td>\n", "      <td>4.6740</td>\n", "      <td>4.4790</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-07</th>\n", "      <td>5.3269</td>\n", "      <td>5.3447</td>\n", "      <td>5.3510</td>\n", "      <td>5.3562</td>\n", "      <td>5.3657</td>\n", "      <td>5.3707</td>\n", "      <td>4.6664</td>\n", "      <td>4.4042</td>\n", "      <td>4.0813</td>\n", "      <td>3.9200</td>\n", "      <td>...</td>\n", "      <td>3.5893</td>\n", "      <td>3.3900</td>\n", "      <td>3.1897</td>\n", "      <td>5.364</td>\n", "      <td>5.3400</td>\n", "      <td>5.2110</td>\n", "      <td>5.0310</td>\n", "      <td>4.8480</td>\n", "      <td>4.6660</td>\n", "      <td>4.4750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-08</th>\n", "      <td>5.3255</td>\n", "      <td>5.3450</td>\n", "      <td>5.3475</td>\n", "      <td>5.3589</td>\n", "      <td>5.3714</td>\n", "      <td>5.3824</td>\n", "      <td>4.7739</td>\n", "      <td>4.5230</td>\n", "      <td>4.2011</td>\n", "      <td>4.0349</td>\n", "      <td>...</td>\n", "      <td>3.6357</td>\n", "      <td>3.4346</td>\n", "      <td>3.2363</td>\n", "      <td>5.365</td>\n", "      <td>5.3640</td>\n", "      <td>5.2670</td>\n", "      <td>5.1120</td>\n", "      <td>4.9480</td>\n", "      <td>4.7890</td>\n", "      <td>4.6050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-11</th>\n", "      <td>5.3485</td>\n", "      <td>5.3535</td>\n", "      <td>5.3570</td>\n", "      <td>5.3620</td>\n", "      <td>5.3737</td>\n", "      <td>5.3849</td>\n", "      <td>4.7691</td>\n", "      <td>4.5181</td>\n", "      <td>4.2050</td>\n", "      <td>4.0418</td>\n", "      <td>...</td>\n", "      <td>3.6472</td>\n", "      <td>3.4472</td>\n", "      <td>3.2475</td>\n", "      <td>5.371</td>\n", "      <td>5.3685</td>\n", "      <td>5.2680</td>\n", "      <td>5.1140</td>\n", "      <td>4.9550</td>\n", "      <td>4.7820</td>\n", "      <td>4.5995</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-12</th>\n", "      <td>5.3493</td>\n", "      <td>5.3520</td>\n", "      <td>5.3540</td>\n", "      <td>5.3605</td>\n", "      <td>5.3704</td>\n", "      <td>5.3830</td>\n", "      <td>4.7815</td>\n", "      <td>4.5327</td>\n", "      <td>4.2027</td>\n", "      <td>4.0280</td>\n", "      <td>...</td>\n", "      <td>3.6198</td>\n", "      <td>3.4197</td>\n", "      <td>3.2149</td>\n", "      <td>5.367</td>\n", "      <td>5.3670</td>\n", "      <td>5.2760</td>\n", "      <td>5.1290</td>\n", "      <td>4.9680</td>\n", "      <td>4.8040</td>\n", "      <td>4.6240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-13</th>\n", "      <td>5.3475</td>\n", "      <td>5.3505</td>\n", "      <td>5.3514</td>\n", "      <td>5.3571</td>\n", "      <td>5.3627</td>\n", "      <td>5.3680</td>\n", "      <td>4.5080</td>\n", "      <td>4.2451</td>\n", "      <td>3.9335</td>\n", "      <td>3.7785</td>\n", "      <td>...</td>\n", "      <td>3.4940</td>\n", "      <td>3.2965</td>\n", "      <td>3.0973</td>\n", "      <td>5.363</td>\n", "      <td>5.3410</td>\n", "      <td>5.1550</td>\n", "      <td>4.9160</td>\n", "      <td>4.6900</td>\n", "      <td>4.4820</td>\n", "      <td>4.2795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-14</th>\n", "      <td>5.3465</td>\n", "      <td>5.3505</td>\n", "      <td>5.3538</td>\n", "      <td>5.3552</td>\n", "      <td>5.3600</td>\n", "      <td>5.3674</td>\n", "      <td>4.4872</td>\n", "      <td>4.2060</td>\n", "      <td>3.8828</td>\n", "      <td>3.7208</td>\n", "      <td>...</td>\n", "      <td>3.3515</td>\n", "      <td>3.1510</td>\n", "      <td>2.9530</td>\n", "      <td>5.338</td>\n", "      <td>5.1670</td>\n", "      <td>4.9435</td>\n", "      <td>4.7065</td>\n", "      <td>4.4970</td>\n", "      <td>4.2610</td>\n", "      <td>4.0785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-15</th>\n", "      <td>5.3454</td>\n", "      <td>5.3530</td>\n", "      <td>5.3556</td>\n", "      <td>5.3570</td>\n", "      <td>5.3640</td>\n", "      <td>5.3716</td>\n", "      <td>4.5353</td>\n", "      <td>4.2535</td>\n", "      <td>3.9109</td>\n", "      <td>3.7346</td>\n", "      <td>...</td>\n", "      <td>3.3256</td>\n", "      <td>3.1247</td>\n", "      <td>2.9240</td>\n", "      <td>5.348</td>\n", "      <td>5.1920</td>\n", "      <td>4.9830</td>\n", "      <td>4.7610</td>\n", "      <td>4.5580</td>\n", "      <td>4.3330</td>\n", "      <td>4.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-18</th>\n", "      <td>5.3427</td>\n", "      <td>5.3510</td>\n", "      <td>5.3527</td>\n", "      <td>5.3573</td>\n", "      <td>5.3634</td>\n", "      <td>5.3728</td>\n", "      <td>4.5350</td>\n", "      <td>4.2556</td>\n", "      <td>3.9235</td>\n", "      <td>3.7511</td>\n", "      <td>...</td>\n", "      <td>3.3550</td>\n", "      <td>3.1535</td>\n", "      <td>2.9500</td>\n", "      <td>5.353</td>\n", "      <td>5.1940</td>\n", "      <td>4.9770</td>\n", "      <td>4.7630</td>\n", "      <td>4.5540</td>\n", "      <td>4.3380</td>\n", "      <td>4.1450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-19</th>\n", "      <td>5.3434</td>\n", "      <td>5.3505</td>\n", "      <td>5.3545</td>\n", "      <td>5.3575</td>\n", "      <td>5.3635</td>\n", "      <td>5.3723</td>\n", "      <td>4.5114</td>\n", "      <td>4.2432</td>\n", "      <td>3.9185</td>\n", "      <td>3.7496</td>\n", "      <td>...</td>\n", "      <td>3.3466</td>\n", "      <td>3.1416</td>\n", "      <td>2.9416</td>\n", "      <td>5.350</td>\n", "      <td>5.1810</td>\n", "      <td>4.9485</td>\n", "      <td>4.7230</td>\n", "      <td>4.5200</td>\n", "      <td>4.3010</td>\n", "      <td>4.1220</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-20</th>\n", "      <td>5.3337</td>\n", "      <td>5.3495</td>\n", "      <td>5.3510</td>\n", "      <td>5.3548</td>\n", "      <td>5.3570</td>\n", "      <td>5.3602</td>\n", "      <td>4.4051</td>\n", "      <td>4.1317</td>\n", "      <td>3.8063</td>\n", "      <td>3.6432</td>\n", "      <td>...</td>\n", "      <td>3.2890</td>\n", "      <td>3.0828</td>\n", "      <td>2.8780</td>\n", "      <td>5.337</td>\n", "      <td>5.1350</td>\n", "      <td>4.8710</td>\n", "      <td>4.6100</td>\n", "      <td>4.3880</td>\n", "      <td>4.1450</td>\n", "      <td>3.9800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-21</th>\n", "      <td>5.3581</td>\n", "      <td>5.3505</td>\n", "      <td>5.3560</td>\n", "      <td>5.3568</td>\n", "      <td>5.3568</td>\n", "      <td>5.3513</td>\n", "      <td>4.4046</td>\n", "      <td>4.1310</td>\n", "      <td>3.8115</td>\n", "      <td>3.6540</td>\n", "      <td>...</td>\n", "      <td>3.3274</td>\n", "      <td>3.1235</td>\n", "      <td>2.9122</td>\n", "      <td>5.336</td>\n", "      <td>5.1460</td>\n", "      <td>4.8870</td>\n", "      <td>4.6320</td>\n", "      <td>4.4040</td>\n", "      <td>4.1720</td>\n", "      <td>4.0115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-22</th>\n", "      <td>5.3575</td>\n", "      <td>5.3540</td>\n", "      <td>5.3585</td>\n", "      <td>5.3580</td>\n", "      <td>5.3555</td>\n", "      <td>5.3472</td>\n", "      <td>4.3750</td>\n", "      <td>4.1035</td>\n", "      <td>3.7950</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.3560</td>\n", "      <td>3.1510</td>\n", "      <td>2.9460</td>\n", "      <td>5.334</td>\n", "      <td>5.1260</td>\n", "      <td>4.8495</td>\n", "      <td>4.5920</td>\n", "      <td>4.3650</td>\n", "      <td>4.1460</td>\n", "      <td>3.9835</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>5.3645</td>\n", "      <td>5.3563</td>\n", "      <td>5.3586</td>\n", "      <td>5.3580</td>\n", "      <td>5.3560</td>\n", "      <td>5.3476</td>\n", "      <td>4.4117</td>\n", "      <td>4.1390</td>\n", "      <td>3.8191</td>\n", "      <td>3.6648</td>\n", "      <td>...</td>\n", "      <td>3.3483</td>\n", "      <td>3.1435</td>\n", "      <td>2.9297</td>\n", "      <td>5.335</td>\n", "      <td>5.1560</td>\n", "      <td>4.9060</td>\n", "      <td>4.6565</td>\n", "      <td>4.4375</td>\n", "      <td>4.1990</td>\n", "      <td>4.0500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>5.3670</td>\n", "      <td>5.3565</td>\n", "      <td>5.3575</td>\n", "      <td>5.3580</td>\n", "      <td>5.3515</td>\n", "      <td>5.3370</td>\n", "      <td>4.3543</td>\n", "      <td>4.0750</td>\n", "      <td>3.7441</td>\n", "      <td>3.5811</td>\n", "      <td>...</td>\n", "      <td>3.2630</td>\n", "      <td>3.0590</td>\n", "      <td>2.8432</td>\n", "      <td>5.326</td>\n", "      <td>5.1230</td>\n", "      <td>4.8590</td>\n", "      <td>4.6015</td>\n", "      <td>4.3790</td>\n", "      <td>4.1375</td>\n", "      <td>3.9670</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>5.3435</td>\n", "      <td>5.3450</td>\n", "      <td>5.3534</td>\n", "      <td>5.3523</td>\n", "      <td>5.3491</td>\n", "      <td>5.3295</td>\n", "      <td>4.3757</td>\n", "      <td>4.0964</td>\n", "      <td>3.7680</td>\n", "      <td>3.6109</td>\n", "      <td>...</td>\n", "      <td>3.2950</td>\n", "      <td>3.0900</td>\n", "      <td>2.8730</td>\n", "      <td>5.331</td>\n", "      <td>5.1330</td>\n", "      <td>4.8770</td>\n", "      <td>4.6400</td>\n", "      <td>4.4225</td>\n", "      <td>4.1860</td>\n", "      <td>4.0160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>5.3405</td>\n", "      <td>5.3431</td>\n", "      <td>5.3460</td>\n", "      <td>5.3480</td>\n", "      <td>5.3455</td>\n", "      <td>5.3265</td>\n", "      <td>4.3465</td>\n", "      <td>4.0657</td>\n", "      <td>3.7482</td>\n", "      <td>3.6007</td>\n", "      <td>...</td>\n", "      <td>3.3161</td>\n", "      <td>3.1075</td>\n", "      <td>2.8937</td>\n", "      <td>5.327</td>\n", "      <td>5.1320</td>\n", "      <td>4.8650</td>\n", "      <td>4.6190</td>\n", "      <td>4.3980</td>\n", "      <td>4.1610</td>\n", "      <td>3.9770</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>19 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2023-12-04           5.3324           5.3412           5.3457          5.3610   \n", "2023-12-05           5.3264           5.3491           5.3545          5.3640   \n", "2023-12-06           5.3234           5.3381           5.3475          5.3520   \n", "2023-12-07           5.3269           5.3447           5.3510          5.3562   \n", "2023-12-08           5.3255           5.3450           5.3475          5.3589   \n", "2023-12-11           5.3485           5.3535           5.3570          5.3620   \n", "2023-12-12           5.3493           5.3520           5.3540          5.3605   \n", "2023-12-13           5.3475           5.3505           5.3514          5.3571   \n", "2023-12-14           5.3465           5.3505           5.3538          5.3552   \n", "2023-12-15           5.3454           5.3530           5.3556          5.3570   \n", "2023-12-18           5.3427           5.3510           5.3527          5.3573   \n", "2023-12-19           5.3434           5.3505           5.3545          5.3575   \n", "2023-12-20           5.3337           5.3495           5.3510          5.3548   \n", "2023-12-21           5.3581           5.3505           5.3560          5.3568   \n", "2023-12-22           5.3575           5.3540           5.3585          5.3580   \n", "2023-12-26           5.3645           5.3563           5.3586          5.3580   \n", "2023-12-27           5.3670           5.3565           5.3575          5.3580   \n", "2023-12-28           5.3435           5.3450           5.3534          5.3523   \n", "2023-12-29           5.3405           5.3431           5.3460          5.3480   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2023-12-04          5.3732          5.3768           4.6921          4.4365   \n", "2023-12-05          5.3741          5.3766           4.6540          4.3900   \n", "2023-12-06          5.3634          5.3680           4.6751          4.4064   \n", "2023-12-07          5.3657          5.3707           4.6664          4.4042   \n", "2023-12-08          5.3714          5.3824           4.7739          4.5230   \n", "2023-12-11          5.3737          5.3849           4.7691          4.5181   \n", "2023-12-12          5.3704          5.3830           4.7815          4.5327   \n", "2023-12-13          5.3627          5.3680           4.5080          4.2451   \n", "2023-12-14          5.3600          5.3674           4.4872          4.2060   \n", "2023-12-15          5.3640          5.3716           4.5353          4.2535   \n", "2023-12-18          5.3634          5.3728           4.5350          4.2556   \n", "2023-12-19          5.3635          5.3723           4.5114          4.2432   \n", "2023-12-20          5.3570          5.3602           4.4051          4.1317   \n", "2023-12-21          5.3568          5.3513           4.4046          4.1310   \n", "2023-12-22          5.3555          5.3472           4.3750          4.1035   \n", "2023-12-26          5.3560          5.3476           4.4117          4.1390   \n", "2023-12-27          5.3515          5.3370           4.3543          4.0750   \n", "2023-12-28          5.3491          5.3295           4.3757          4.0964   \n", "2023-12-29          5.3455          5.3265           4.3465          4.0657   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2023-12-04          4.1323          3.9834  ...           3.7392   \n", "2023-12-05          4.0730          3.9187  ...           3.6326   \n", "2023-12-06          4.0683          3.8979  ...           3.5523   \n", "2023-12-07          4.0813          3.9200  ...           3.5893   \n", "2023-12-08          4.2011          4.0349  ...           3.6357   \n", "2023-12-11          4.2050          4.0418  ...           3.6472   \n", "2023-12-12          4.2027          4.0280  ...           3.6198   \n", "2023-12-13          3.9335          3.7785  ...           3.4940   \n", "2023-12-14          3.8828          3.7208  ...           3.3515   \n", "2023-12-15          3.9109          3.7346  ...           3.3256   \n", "2023-12-18          3.9235          3.7511  ...           3.3550   \n", "2023-12-19          3.9185          3.7496  ...           3.3466   \n", "2023-12-20          3.8063          3.6432  ...           3.2890   \n", "2023-12-21          3.8115          3.6540  ...           3.3274   \n", "2023-12-22          3.7950          3.6452  ...           3.3560   \n", "2023-12-26          3.8191          3.6648  ...           3.3483   \n", "2023-12-27          3.7441          3.5811  ...           3.2630   \n", "2023-12-28          3.7680          3.6109  ...           3.2950   \n", "2023-12-29          3.7482          3.6007  ...           3.3161   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2023-12-04           3.5390           3.3326           5.375          5.3445   \n", "2023-12-05           3.4346           3.2316           5.376          5.3420   \n", "2023-12-06           3.3535           3.1522           5.365          5.3350   \n", "2023-12-07           3.3900           3.1897           5.364          5.3400   \n", "2023-12-08           3.4346           3.2363           5.365          5.3640   \n", "2023-12-11           3.4472           3.2475           5.371          5.3685   \n", "2023-12-12           3.4197           3.2149           5.367          5.3670   \n", "2023-12-13           3.2965           3.0973           5.363          5.3410   \n", "2023-12-14           3.1510           2.9530           5.338          5.1670   \n", "2023-12-15           3.1247           2.9240           5.348          5.1920   \n", "2023-12-18           3.1535           2.9500           5.353          5.1940   \n", "2023-12-19           3.1416           2.9416           5.350          5.1810   \n", "2023-12-20           3.0828           2.8780           5.337          5.1350   \n", "2023-12-21           3.1235           2.9122           5.336          5.1460   \n", "2023-12-22           3.1510           2.9460           5.334          5.1260   \n", "2023-12-26           3.1435           2.9297           5.335          5.1560   \n", "2023-12-27           3.0590           2.8432           5.326          5.1230   \n", "2023-12-28           3.0900           2.8730           5.331          5.1330   \n", "2023-12-29           3.1075           2.8937           5.327          5.1320   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2023-12-04          5.2170          5.0450          4.8600          4.6730   \n", "2023-12-05          5.2005          5.0150          4.8300          4.6420   \n", "2023-12-06          5.2070          5.0290          4.8540          4.6740   \n", "2023-12-07          5.2110          5.0310          4.8480          4.6660   \n", "2023-12-08          5.2670          5.1120          4.9480          4.7890   \n", "2023-12-11          5.2680          5.1140          4.9550          4.7820   \n", "2023-12-12          5.2760          5.1290          4.9680          4.8040   \n", "2023-12-13          5.1550          4.9160          4.6900          4.4820   \n", "2023-12-14          4.9435          4.7065          4.4970          4.2610   \n", "2023-12-15          4.9830          4.7610          4.5580          4.3330   \n", "2023-12-18          4.9770          4.7630          4.5540          4.3380   \n", "2023-12-19          4.9485          4.7230          4.5200          4.3010   \n", "2023-12-20          4.8710          4.6100          4.3880          4.1450   \n", "2023-12-21          4.8870          4.6320          4.4040          4.1720   \n", "2023-12-22          4.8495          4.5920          4.3650          4.1460   \n", "2023-12-26          4.9060          4.6565          4.4375          4.1990   \n", "2023-12-27          4.8590          4.6015          4.3790          4.1375   \n", "2023-12-28          4.8770          4.6400          4.4225          4.1860   \n", "2023-12-29          4.8650          4.6190          4.3980          4.1610   \n", "\n", "            USSOSR7 Curncy  \n", "2023-12-04          4.4750  \n", "2023-12-05          4.4370  \n", "2023-12-06          4.4790  \n", "2023-12-07          4.4750  \n", "2023-12-08          4.6050  \n", "2023-12-11          4.5995  \n", "2023-12-12          4.6240  \n", "2023-12-13          4.2795  \n", "2023-12-14          4.0785  \n", "2023-12-15          4.1500  \n", "2023-12-18          4.1450  \n", "2023-12-19          4.1220  \n", "2023-12-20          3.9800  \n", "2023-12-21          4.0115  \n", "2023-12-22          3.9835  \n", "2023-12-26          4.0500  \n", "2023-12-27          3.9670  \n", "2023-12-28          4.0160  \n", "2023-12-29          3.9770  \n", "\n", "[19 rows x 31 columns]"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-12-04</th>\n", "      <td>2023.0</td>\n", "      <td>4.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-05</th>\n", "      <td>2023.0</td>\n", "      <td>5.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-06</th>\n", "      <td>2023.0</td>\n", "      <td>6.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-07</th>\n", "      <td>2023.0</td>\n", "      <td>7.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-08</th>\n", "      <td>2023.0</td>\n", "      <td>8.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-11</th>\n", "      <td>2023.0</td>\n", "      <td>11.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-12</th>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-13</th>\n", "      <td>2023.0</td>\n", "      <td>13.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-14</th>\n", "      <td>2023.0</td>\n", "      <td>14.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-15</th>\n", "      <td>2023.0</td>\n", "      <td>15.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-18</th>\n", "      <td>2023.0</td>\n", "      <td>18.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-19</th>\n", "      <td>2023.0</td>\n", "      <td>19.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-20</th>\n", "      <td>2023.0</td>\n", "      <td>20.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-21</th>\n", "      <td>2023.0</td>\n", "      <td>21.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-22</th>\n", "      <td>2023.0</td>\n", "      <td>22.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-26</th>\n", "      <td>2023.0</td>\n", "      <td>26.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-27</th>\n", "      <td>2023.0</td>\n", "      <td>27.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-28</th>\n", "      <td>2023.0</td>\n", "      <td>28.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-12-29</th>\n", "      <td>2023.0</td>\n", "      <td>29.0</td>\n", "      <td>2023.0</td>\n", "      <td>12.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 0     1       2     3\n", "2023-12-04  2023.0   4.0  2023.0  12.0\n", "2023-12-05  2023.0   5.0  2023.0  12.0\n", "2023-12-06  2023.0   6.0  2023.0  12.0\n", "2023-12-07  2023.0   7.0  2023.0  12.0\n", "2023-12-08  2023.0   8.0  2023.0  12.0\n", "2023-12-11  2023.0  11.0  2023.0  12.0\n", "2023-12-12  2023.0  12.0  2023.0  12.0\n", "2023-12-13  2023.0  13.0  2023.0  12.0\n", "2023-12-14  2023.0  14.0  2023.0  12.0\n", "2023-12-15  2023.0  15.0  2023.0  12.0\n", "2023-12-18  2023.0  18.0  2023.0  12.0\n", "2023-12-19  2023.0  19.0  2023.0  12.0\n", "2023-12-20  2023.0  20.0  2023.0  12.0\n", "2023-12-21  2023.0  21.0  2023.0  12.0\n", "2023-12-22  2023.0  22.0  2023.0  12.0\n", "2023-12-26  2023.0  26.0  2023.0  12.0\n", "2023-12-27  2023.0  27.0  2023.0  12.0\n", "2023-12-28  2023.0  28.0  2023.0  12.0\n", "2023-12-29  2023.0  29.0  2023.0  12.0"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["data.apply(simple_func,axis=1,result_type=\"expand\",)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["from loader.curve_calibrator import curveCalibrator"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-10-16, 2024-10-17]\n", "Loading close price from 2024-01-01\n", "Opening cached curve benchmark data from bbg, FILE:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\n", "Start calibration of USD_SOFR...\n", "OIS term: 3W , Maturity: 2024-11-08, Quote: 4.8510, Pillar date: 2024-11-07\n", "OIS term: 18M, Maturity: 2026-04-20, Quote: 3.8625, Pillar date: 2026-04-20\n", "OIS term: 2Y , Maturity: 2026-10-19, Quote: 3.7405, Pillar date: 2026-10-19\n", "OIS term: 3Y , Maturity: 2027-10-18, Quote: 3.6096, Pillar date: 2027-10-18\n", "OIS term: 4Y , Maturity: 2028-10-18, Quote: 3.5479, Pillar date: 2028-10-18\n", "OIS term: 5Y , Maturity: 2029-10-18, Quote: 3.5208, Pillar date: 2029-10-18\n", "OIS term: 6Y , Maturity: 2030-10-18, Quote: 3.5143, Pillar date: 2030-10-18\n", "OIS term: 7Y , Maturity: 2031-10-20, Quote: 3.5165, Pillar date: 2031-10-20\n", "OIS term: 8Y , Maturity: 2032-10-18, Quote: 3.5247, Pillar date: 2032-10-18\n", "OIS term: 9Y , Maturity: 2033-10-18, Quote: 3.5363, Pillar date: 2033-10-18\n", "OIS term: 10Y, Maturity: 2034-10-18, Quote: 3.5496, Pillar date: 2034-10-18\n", "OIS term: 11Y, Maturity: 2035-10-18, Quote: 3.5650, Pillar date: 2035-10-18\n", "OIS term: 12Y, Maturity: 2036-10-20, Quote: 3.5809, Pillar date: 2036-10-20\n", "OIS term: 15Y, Maturity: 2039-10-18, Quote: 3.6183, Pillar date: 2039-10-18\n", "OIS term: 20Y, Maturity: 2044-10-18, Quote: 3.6252, Pillar date: 2044-10-18\n", "OIS term: 25Y, Maturity: 2049-10-18, Quote: 3.5653, Pillar date: 2049-10-18\n", "OIS term: 30Y, Maturity: 2054-10-19, Quote: 3.4894, Pillar date: 2054-10-19\n", "OIS term: 40Y, Maturity: 2064-10-20, Quote: 3.3055, Pillar date: 2064-10-20\n", "OIS term: 50Y, Maturity: 2074-10-18, Quote: 3.1255, Pillar date: 2074-10-18\n", "Meeting dated swap term s1, 2024-11-07 - 2024-12-18, Maturity: 2024-12-18, Quote: 4.6340, Pillar date: 2024-12-18\n", "Meeting dated swap term s2, 2024-12-18 - 2025-01-29, Maturity: 2025-01-29, Quote: 4.4410, Pillar date: 2025-01-29\n", "Meeting dated swap term s3, 2025-01-29 - 2025-03-19, Maturity: 2025-03-19, Quote: 4.2210, Pillar date: 2025-03-19\n", "Meeting dated swap term s4, 2025-03-19 - 2025-05-07, Maturity: 2025-05-07, Quote: 3.9890, Pillar date: 2025-05-07\n", "Meeting dated swap term s5, 2025-05-07 - 2025-06-18, Maturity: 2025-06-18, Quote: 3.8075, Pillar date: 2025-06-18\n", "Meeting dated swap term s6, 2025-06-18 - 2025-07-30, Maturity: 2025-07-30, Quote: 3.6510, Pillar date: 2025-07-30\n", "Meeting dated swap term s7, 2025-07-30 - 2025-09-17, Maturity: 2025-09-17, Quote: 3.5720, Pillar date: 2025-09-17\n", "Finish calibration of USD_SOFR.\n", "Start calibration of USD_SOFR...\n", "OIS term: 3W , Maturity: 2024-11-12, Quote: 4.8177, Pillar date: 2024-11-07\n", "OIS term: 18M, Maturity: 2026-04-21, Quote: 3.8941, Pillar date: 2026-04-21\n", "OIS term: 2Y , Maturity: 2026-10-21, Quote: 3.7751, <PERSON>llar date: 2026-10-21\n", "OIS term: 3Y , Maturity: 2027-10-21, Quote: 3.6501, Pillar date: 2027-10-21\n", "OIS term: 4Y , Maturity: 2028-10-23, Quote: 3.5948, Pillar date: 2028-10-23\n", "OIS term: 5Y , Maturity: 2029-10-22, Quote: 3.5740, Pillar date: 2029-10-22\n", "OIS term: 6Y , Maturity: 2030-10-21, Quote: 3.5730, Pillar date: 2030-10-21\n", "OIS term: 7Y , Maturity: 2031-10-21, Quote: 3.5795, Pillar date: 2031-10-21\n", "OIS term: 8Y , Maturity: 2032-10-21, Quote: 3.5912, Pillar date: 2032-10-21\n", "OIS term: 9Y , Maturity: 2033-10-21, Quote: 3.6052, Pillar date: 2033-10-21\n", "OIS term: 10Y, Maturity: 2034-10-23, Quote: 3.6221, Pillar date: 2034-10-23\n", "OIS term: 11Y, Maturity: 2035-10-22, Quote: 3.6396, Pillar date: 2035-10-22\n", "OIS term: 12Y, Maturity: 2036-10-21, Quote: 3.6565, <PERSON>llar date: 2036-10-21\n", "OIS term: 15Y, Maturity: 2039-10-21, Quote: 3.6968, Pillar date: 2039-10-21\n", "OIS term: 20Y, Maturity: 2044-10-21, Quote: 3.7068, Pillar date: 2044-10-21\n", "OIS term: 25Y, Maturity: 2049-10-21, Quote: 3.6491, Pillar date: 2049-10-21\n", "OIS term: 30Y, Maturity: 2054-10-21, Quote: 3.5749, Pillar date: 2054-10-21\n", "OIS term: 40Y, Maturity: 2064-10-21, Quote: 3.3915, Pillar date: 2064-10-21\n", "OIS term: 50Y, Maturity: 2074-10-22, Quote: 3.2092, Pillar date: 2074-10-22\n", "Meeting dated swap term s1, 2024-11-07 - 2024-12-18, Maturity: 2024-12-18, Quote: 4.6470, Pillar date: 2024-12-18\n", "Meeting dated swap term s2, 2024-12-18 - 2025-01-29, Maturity: 2025-01-29, Quote: 4.4850, Pillar date: 2025-01-29\n", "Meeting dated swap term s3, 2025-01-29 - 2025-03-19, Maturity: 2025-03-19, Quote: 4.2690, Pillar date: 2025-03-19\n", "Meeting dated swap term s4, 2025-03-19 - 2025-05-07, Maturity: 2025-05-07, Quote: 4.0410, Pillar date: 2025-05-07\n", "Meeting dated swap term s5, 2025-05-07 - 2025-06-18, Maturity: 2025-06-18, Quote: 3.8580, Pillar date: 2025-06-18\n", "Meeting dated swap term s6, 2025-06-18 - 2025-07-30, Maturity: 2025-07-30, Quote: 3.6980, Pillar date: 2025-07-30\n", "Meeting dated swap term s7, 2025-07-30 - 2025-09-17, Maturity: 2025-09-17, Quote: 3.5930, Pillar date: 2025-09-17\n", "Finish calibration of USD_SOFR.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>pillar_dates</th>\n", "      <th>discount_factors</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>[2024-10-16, 2024-11-07, 2024-12-18, 2025-01-2...</td>\n", "      <td>[1.0, 0.9970372904510786, 0.9918029402330839, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>[2024-10-17, 2024-11-07, 2024-12-18, 2025-01-2...</td>\n", "      <td>[1.0, 0.9971676478726544, 0.9919180044933691, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 pillar_dates  \\\n", "2024-10-16  [2024-10-16, 2024-11-07, 2024-12-18, 2025-01-2...   \n", "2024-10-17  [2024-10-17, 2024-11-07, 2024-12-18, 2025-01-2...   \n", "\n", "                                             discount_factors  \n", "2024-10-16  [1.0, 0.9970372904510786, 0.9918029402330839, ...  \n", "2024-10-17  [1.0, 0.9971676478726544, 0.9919180044933691, ...  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["curvecalib = curveCalibrator(\"USD.SOFR\")\n", "curvecalib.recalibrate(dt.date(2024,10,16),dt.date(2024,10,17))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([2024-10-16], dtype='object')"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>USOSFR9 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>...</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "      <th>PX_LAST</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>3.5483</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>3.5363</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>3.6052</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>3.6003</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["           USOSFR1Z Curncy USOSFR1F Curncy USOSFR2 Curncy USOSFR3 Curncy  \\\n", "                   PX_LAST         PX_LAST        PX_LAST        PX_LAST   \n", "2024-10-15          4.8420          3.8710         3.7486         3.6178   \n", "2024-10-16          4.8486          3.8625         3.7405         3.6096   \n", "2024-10-17          4.8505          3.8941         3.7751         3.6501   \n", "2024-10-18          4.8490          3.8707         3.7522         3.6296   \n", "2024-10-21          4.8491          3.8998         3.7861         3.6692   \n", "\n", "           USOSFR4 Curncy USOSFR5 Curncy USOSFR6 Curncy USOSFR7 Curncy  \\\n", "                  PX_LAST        PX_LAST        PX_LAST        PX_LAST   \n", "2024-10-15         3.5564         3.5300         3.5241         3.5275   \n", "2024-10-16         3.5479         3.5208         3.5143         3.5165   \n", "2024-10-17         3.5948         3.5740         3.5730         3.5795   \n", "2024-10-18         3.5781         3.5610         3.5626         3.5712   \n", "2024-10-21         3.6203         3.6044         3.6063         3.6157   \n", "\n", "           USOSFR8 Curncy USOSFR9 Curncy  ... USOSFR25 Curncy USOSFR30 Curncy  \\\n", "                  PX_LAST        PX_LAST  ...         PX_LAST         PX_LAST   \n", "2024-10-15         3.5365         3.5483  ...          3.5807          3.5047   \n", "2024-10-16         3.5247         3.5363  ...          3.5653          3.4894   \n", "2024-10-17         3.5912         3.6052  ...          3.6491          3.5749   \n", "2024-10-18         3.5842         3.6003  ...          3.6525          3.5790   \n", "2024-10-21         3.6292         3.6452  ...          3.6980          3.6245   \n", "\n", "           USOSFR40 Curncy USOSFR50 Curncy USSOSR1 Curncy USSOSR2 Curncy  \\\n", "                   PX_LAST         PX_LAST        PX_LAST        PX_LAST   \n", "2024-10-15          3.3215          3.1395          4.628         4.4375   \n", "2024-10-16          3.3055          3.1255          4.634         4.4410   \n", "2024-10-17          3.3915          3.2092          4.647         4.4850   \n", "2024-10-18          3.3956          3.2143          4.635         4.4640   \n", "2024-10-21          3.4411          3.2605          4.644         4.4730   \n", "\n", "           USSOSR3 Curncy USSOSR4 Curncy USSOSR5 Curncy USSOSR6 Curncy  \n", "                  PX_LAST        PX_LAST        PX_LAST        PX_LAST  \n", "2024-10-15          4.221          3.989         3.8125          3.680  \n", "2024-10-16          4.221          3.989         3.8075          3.651  \n", "2024-10-17          4.269          4.041         3.8580          3.698  \n", "2024-10-18          4.258          4.016         3.8330          3.676  \n", "2024-10-21          4.269          4.042         3.8680          3.714  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["from xbbg import blp\n", "bbg_bar_data = blp.bdh(\n", "            tickers=list(benchmarks_meta_data['ticker']), flds=[\"PX_LAST\"], start_date=\"20241015\", end_date=\"20241021\")\n", "bbg_bar_data"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>USOSFR9 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>3.5483</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>3.5363</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>3.6052</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>3.6003</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  USOSFR3 Curncy  \\\n", "2024-10-15           4.8420           3.8710          3.7486          3.6178   \n", "2024-10-16           4.8486           3.8625          3.7405          3.6096   \n", "2024-10-17           4.8505           3.8941          3.7751          3.6501   \n", "2024-10-18           4.8490           3.8707          3.7522          3.6296   \n", "2024-10-21           4.8491           3.8998          3.7861          3.6692   \n", "\n", "            USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  USOSFR7 Curncy  \\\n", "2024-10-15          3.5564          3.5300          3.5241          3.5275   \n", "2024-10-16          3.5479          3.5208          3.5143          3.5165   \n", "2024-10-17          3.5948          3.5740          3.5730          3.5795   \n", "2024-10-18          3.5781          3.5610          3.5626          3.5712   \n", "2024-10-21          3.6203          3.6044          3.6063          3.6157   \n", "\n", "            USOSFR8 Curncy  USOSFR9 Curncy  ...  USOSFR25 Curncy  \\\n", "2024-10-15          3.5365          3.5483  ...           3.5807   \n", "2024-10-16          3.5247          3.5363  ...           3.5653   \n", "2024-10-17          3.5912          3.6052  ...           3.6491   \n", "2024-10-18          3.5842          3.6003  ...           3.6525   \n", "2024-10-21          3.6292          3.6452  ...           3.6980   \n", "\n", "            USOSFR30 Curncy  USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  \\\n", "2024-10-15           3.5047           3.3215           3.1395           4.628   \n", "2024-10-16           3.4894           3.3055           3.1255           4.634   \n", "2024-10-17           3.5749           3.3915           3.2092           4.647   \n", "2024-10-18           3.5790           3.3956           3.2143           4.635   \n", "2024-10-21           3.6245           3.4411           3.2605           4.644   \n", "\n", "            USSOSR2 Curncy  USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  \\\n", "2024-10-15          4.4375           4.221           3.989          3.8125   \n", "2024-10-16          4.4410           4.221           3.989          3.8075   \n", "2024-10-17          4.4850           4.269           4.041          3.8580   \n", "2024-10-18          4.4640           4.258           4.016          3.8330   \n", "2024-10-21          4.4730           4.269           4.042          3.8680   \n", "\n", "            USSOSR6 Curncy  \n", "2024-10-15           3.680  \n", "2024-10-16           3.651  \n", "2024-10-17           3.698  \n", "2024-10-18           3.676  \n", "2024-10-21           3.714  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.columns = bbg_bar_data.columns.droplevel(1)\n", "bbg_bar_data"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([2024-10-15, 2024-10-16, 2024-10-17, 2024-10-18, 2024-10-21], dtype='object')"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['USOSFR1Z Curncy', 'USOSFR1F Curncy', 'USOSFR2 Curncy',\n", "       'USOSFR3 Curncy', 'USOSFR4 Curncy', 'USOSFR5 Curncy',\n", "       'USOSFR6 Curncy', 'USOSFR7 Curncy', 'USOSFR8 Curncy',\n", "       'USOSFR9 Curncy', 'USOSFR10 Curncy', 'USOSFR11 Curncy',\n", "       'USOSFR12 Curncy', 'USOSFR15 Curncy', 'USOSFR20 Curncy',\n", "       'USOSFR25 Curncy', 'USOSFR30 Curncy', 'USOSFR40 Curncy',\n", "       'USOSFR50 Curncy', 'USSOSR1 Curncy', 'USSOSR2 Curncy',\n", "       'USSOSR3 Curncy', 'USSOSR4 Curncy', 'USSOSR5 Curncy',\n", "       'USSOSR6 <PERSON>'], dtype=object)"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmarks_meta_data[\"ticker\"].unique()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>USOSFR9 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>3.5483</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>3.5363</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>3.6052</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>3.6003</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  USOSFR3 Curncy  \\\n", "2024-10-15           4.8420           3.8710          3.7486          3.6178   \n", "2024-10-16           4.8486           3.8625          3.7405          3.6096   \n", "2024-10-17           4.8505           3.8941          3.7751          3.6501   \n", "2024-10-18           4.8490           3.8707          3.7522          3.6296   \n", "2024-10-21           4.8491           3.8998          3.7861          3.6692   \n", "\n", "            USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  USOSFR7 Curncy  \\\n", "2024-10-15          3.5564          3.5300          3.5241          3.5275   \n", "2024-10-16          3.5479          3.5208          3.5143          3.5165   \n", "2024-10-17          3.5948          3.5740          3.5730          3.5795   \n", "2024-10-18          3.5781          3.5610          3.5626          3.5712   \n", "2024-10-21          3.6203          3.6044          3.6063          3.6157   \n", "\n", "            USOSFR8 Curncy  USOSFR9 Curncy  ...  USOSFR25 Curncy  \\\n", "2024-10-15          3.5365          3.5483  ...           3.5807   \n", "2024-10-16          3.5247          3.5363  ...           3.5653   \n", "2024-10-17          3.5912          3.6052  ...           3.6491   \n", "2024-10-18          3.5842          3.6003  ...           3.6525   \n", "2024-10-21          3.6292          3.6452  ...           3.6980   \n", "\n", "            USOSFR30 Curncy  USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  \\\n", "2024-10-15           3.5047           3.3215           3.1395           4.628   \n", "2024-10-16           3.4894           3.3055           3.1255           4.634   \n", "2024-10-17           3.5749           3.3915           3.2092           4.647   \n", "2024-10-18           3.5790           3.3956           3.2143           4.635   \n", "2024-10-21           3.6245           3.4411           3.2605           4.644   \n", "\n", "            USSOSR2 Curncy  USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  \\\n", "2024-10-15          4.4375           4.221           3.989          3.8125   \n", "2024-10-16          4.4410           4.221           3.989          3.8075   \n", "2024-10-17          4.4850           4.269           4.041          3.8580   \n", "2024-10-18          4.4640           4.258           4.016          3.8330   \n", "2024-10-21          4.4730           4.269           4.042          3.8680   \n", "\n", "            USSOSR6 Curncy  \n", "2024-10-15           3.680  \n", "2024-10-16           3.651  \n", "2024-10-17           3.698  \n", "2024-10-18           3.676  \n", "2024-10-21           3.714  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data[benchmarks_meta_data[\"ticker\"].unique()]"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.date(2024, 10, 21)"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index.max()"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["bbg_bar_data.to_parquet(\"temp_data.parquet\")"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>USOSFR9 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>3.5483</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>3.5363</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>3.6052</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>3.6003</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>3.6452</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 25 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  USOSFR3 Curncy  \\\n", "2024-10-15           4.8420           3.8710          3.7486          3.6178   \n", "2024-10-16           4.8486           3.8625          3.7405          3.6096   \n", "2024-10-17           4.8505           3.8941          3.7751          3.6501   \n", "2024-10-18           4.8490           3.8707          3.7522          3.6296   \n", "2024-10-21           4.8491           3.8998          3.7861          3.6692   \n", "\n", "            USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  USOSFR7 Curncy  \\\n", "2024-10-15          3.5564          3.5300          3.5241          3.5275   \n", "2024-10-16          3.5479          3.5208          3.5143          3.5165   \n", "2024-10-17          3.5948          3.5740          3.5730          3.5795   \n", "2024-10-18          3.5781          3.5610          3.5626          3.5712   \n", "2024-10-21          3.6203          3.6044          3.6063          3.6157   \n", "\n", "            USOSFR8 Curncy  USOSFR9 Curncy  ...  USOSFR25 Curncy  \\\n", "2024-10-15          3.5365          3.5483  ...           3.5807   \n", "2024-10-16          3.5247          3.5363  ...           3.5653   \n", "2024-10-17          3.5912          3.6052  ...           3.6491   \n", "2024-10-18          3.5842          3.6003  ...           3.6525   \n", "2024-10-21          3.6292          3.6452  ...           3.6980   \n", "\n", "            USOSFR30 Curncy  USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  \\\n", "2024-10-15           3.5047           3.3215           3.1395           4.628   \n", "2024-10-16           3.4894           3.3055           3.1255           4.634   \n", "2024-10-17           3.5749           3.3915           3.2092           4.647   \n", "2024-10-18           3.5790           3.3956           3.2143           4.635   \n", "2024-10-21           3.6245           3.4411           3.2605           4.644   \n", "\n", "            USSOSR2 Curncy  USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  \\\n", "2024-10-15          4.4375           4.221           3.989          3.8125   \n", "2024-10-16          4.4410           4.221           3.989          3.8075   \n", "2024-10-17          4.4850           4.269           4.041          3.8580   \n", "2024-10-18          4.4640           4.258           4.016          3.8330   \n", "2024-10-21          4.4730           4.269           4.042          3.8680   \n", "\n", "            USSOSR6 Curncy  \n", "2024-10-15           3.680  \n", "2024-10-16           3.651  \n", "2024-10-17           3.698  \n", "2024-10-18           3.676  \n", "2024-10-21           3.714  \n", "\n", "[5 rows x 25 columns]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data_read = pd.read_parquet(\"temp_data.parquet\")\n", "bbg_bar_data_read"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([2024-10-15, 2024-10-16, 2024-10-17, 2024-10-18, 2024-10-21], dtype='object')"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data_read.index"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>USOSFR9 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-14</th>\n", "      <td>4.839</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.655</td>\n", "      <td>4.4560</td>\n", "      <td>4.239</td>\n", "      <td>4.001</td>\n", "      <td>3.8270</td>\n", "      <td>3.70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.842</td>\n", "      <td>3.871</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.53</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>3.5483</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.68</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 25 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  USOSFR3 Curncy  \\\n", "2024-10-14            4.839              NaN             NaN             NaN   \n", "2024-10-15            4.842            3.871          3.7486          3.6178   \n", "\n", "            USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  USOSFR7 Curncy  \\\n", "2024-10-14             NaN             NaN             NaN             NaN   \n", "2024-10-15          3.5564            3.53          3.5241          3.5275   \n", "\n", "            USOSFR8 Curncy  USOSFR9 Curncy  ...  USOSFR25 Curncy  \\\n", "2024-10-14             NaN             NaN  ...              NaN   \n", "2024-10-15          3.5365          3.5483  ...           3.5807   \n", "\n", "            USOSFR30 Curncy  USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  \\\n", "2024-10-14              NaN              NaN              NaN           4.655   \n", "2024-10-15           3.5047           3.3215           3.1395           4.628   \n", "\n", "            USSOSR2 Curncy  USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  \\\n", "2024-10-14          4.4560           4.239           4.001          3.8270   \n", "2024-10-15          4.4375           4.221           3.989          3.8125   \n", "\n", "            USSOSR6 Curncy  \n", "2024-10-14            3.70  \n", "2024-10-15            3.68  \n", "\n", "[2 rows x 25 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data_2 = blp.bdh(\n", "            tickers=list(benchmarks_meta_data['ticker']), flds=[\"PX_LAST\"], start_date=\"20241013\", end_date=\"20241015\")\n", "bbg_bar_data_2.columns = bbg_bar_data_2.columns.droplevel(1)\n", "bbg_bar_data_2"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-15</td>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-16</td>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-17</td>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-18</td>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-21</td>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["         date  USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "0  2024-10-15           4.8420           3.8710          3.7486   \n", "1  2024-10-16           4.8486           3.8625          3.7405   \n", "2  2024-10-17           4.8505           3.8941          3.7751   \n", "3  2024-10-18           4.8490           3.8707          3.7522   \n", "4  2024-10-21           4.8491           3.8998          3.7861   \n", "\n", "   USOSFR3 Curncy  USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  \\\n", "0          3.6178          3.5564          3.5300          3.5241   \n", "1          3.6096          3.5479          3.5208          3.5143   \n", "2          3.6501          3.5948          3.5740          3.5730   \n", "3          3.6296          3.5781          3.5610          3.5626   \n", "4          3.6692          3.6203          3.6044          3.6063   \n", "\n", "   USOSFR7 Curncy  USOSFR8 Curncy  ...  USOSFR25 Curncy  USOSFR30 Curncy  \\\n", "0          3.5275          3.5365  ...           3.5807           3.5047   \n", "1          3.5165          3.5247  ...           3.5653           3.4894   \n", "2          3.5795          3.5912  ...           3.6491           3.5749   \n", "3          3.5712          3.5842  ...           3.6525           3.5790   \n", "4          3.6157          3.6292  ...           3.6980           3.6245   \n", "\n", "   USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "0           3.3215           3.1395           4.628          4.4375   \n", "1           3.3055           3.1255           4.634          4.4410   \n", "2           3.3915           3.2092           4.647          4.4850   \n", "3           3.3956           3.2143           4.635          4.4640   \n", "4           3.4411           3.2605           4.644          4.4730   \n", "\n", "   USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \n", "0           4.221           3.989          3.8125           3.680  \n", "1           4.221           3.989          3.8075           3.651  \n", "2           4.269           4.041          3.8580           3.698  \n", "3           4.258           4.016          3.8330           3.676  \n", "4           4.269           4.042          3.8680           3.714  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.reset_index(names=['date'])"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["bbg_bar_data=bbg_bar_data.reset_index(names=['date'])\n", "bbg_bar_data_2=bbg_bar_data_2.reset_index(names=['date'])"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-14</td>\n", "      <td>4.8390</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.655</td>\n", "      <td>4.4560</td>\n", "      <td>4.239</td>\n", "      <td>4.001</td>\n", "      <td>3.8270</td>\n", "      <td>3.700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-15</td>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-16</td>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-17</td>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-18</td>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-10-21</td>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6 rows × 26 columns</p>\n", "</div>"], "text/plain": ["         date  USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "0  2024-10-14           4.8390              NaN             NaN   \n", "1  2024-10-15           4.8420           3.8710          3.7486   \n", "2  2024-10-16           4.8486           3.8625          3.7405   \n", "3  2024-10-17           4.8505           3.8941          3.7751   \n", "4  2024-10-18           4.8490           3.8707          3.7522   \n", "5  2024-10-21           4.8491           3.8998          3.7861   \n", "\n", "   USOSFR3 Curncy  USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  \\\n", "0             NaN             NaN             NaN             NaN   \n", "1          3.6178          3.5564          3.5300          3.5241   \n", "2          3.6096          3.5479          3.5208          3.5143   \n", "3          3.6501          3.5948          3.5740          3.5730   \n", "4          3.6296          3.5781          3.5610          3.5626   \n", "5          3.6692          3.6203          3.6044          3.6063   \n", "\n", "   USOSFR7 Curncy  USOSFR8 Curncy  ...  USOSFR25 Curncy  USOSFR30 Curncy  \\\n", "0             NaN             NaN  ...              NaN              NaN   \n", "1          3.5275          3.5365  ...           3.5807           3.5047   \n", "2          3.5165          3.5247  ...           3.5653           3.4894   \n", "3          3.5795          3.5912  ...           3.6491           3.5749   \n", "4          3.5712          3.5842  ...           3.6525           3.5790   \n", "5          3.6157          3.6292  ...           3.6980           3.6245   \n", "\n", "   USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "0              NaN              NaN           4.655          4.4560   \n", "1           3.3215           3.1395           4.628          4.4375   \n", "2           3.3055           3.1255           4.634          4.4410   \n", "3           3.3915           3.2092           4.647          4.4850   \n", "4           3.3956           3.2143           4.635          4.4640   \n", "5           3.4411           3.2605           4.644          4.4730   \n", "\n", "   USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \n", "0           4.239           4.001          3.8270           3.700  \n", "1           4.221           3.989          3.8125           3.680  \n", "2           4.221           3.989          3.8075           3.651  \n", "3           4.269           4.041          3.8580           3.698  \n", "4           4.258           4.016          3.8330           3.676  \n", "5           4.269           4.042          3.8680           3.714  \n", "\n", "[6 rows x 26 columns]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.merge(bbg_bar_data, bbg_bar_data_2, how = 'outer', left_on=list(bbg_bar_data.columns), right_on=list(bbg_bar_data_2.columns))"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-15</td>\n", "      <td>100.0000</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-16</td>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-17</td>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-18</td>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-21</td>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 26 columns</p>\n", "</div>"], "text/plain": ["         date  USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "0  2024-10-15         100.0000           3.8710          3.7486   \n", "1  2024-10-16           4.8486           3.8625          3.7405   \n", "2  2024-10-17           4.8505           3.8941          3.7751   \n", "3  2024-10-18           4.8490           3.8707          3.7522   \n", "4  2024-10-21           4.8491           3.8998          3.7861   \n", "\n", "   USOSFR3 Curncy  USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  \\\n", "0          3.6178          3.5564          3.5300          3.5241   \n", "1          3.6096          3.5479          3.5208          3.5143   \n", "2          3.6501          3.5948          3.5740          3.5730   \n", "3          3.6296          3.5781          3.5610          3.5626   \n", "4          3.6692          3.6203          3.6044          3.6063   \n", "\n", "   USOSFR7 Curncy  USOSFR8 Curncy  ...  USOSFR25 Curncy  USOSFR30 Curncy  \\\n", "0          3.5275          3.5365  ...           3.5807           3.5047   \n", "1          3.5165          3.5247  ...           3.5653           3.4894   \n", "2          3.5795          3.5912  ...           3.6491           3.5749   \n", "3          3.5712          3.5842  ...           3.6525           3.5790   \n", "4          3.6157          3.6292  ...           3.6980           3.6245   \n", "\n", "   USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "0           3.3215           3.1395           4.628          4.4375   \n", "1           3.3055           3.1255           4.634          4.4410   \n", "2           3.3915           3.2092           4.647          4.4850   \n", "3           3.3956           3.2143           4.635          4.4640   \n", "4           3.4411           3.2605           4.644          4.4730   \n", "\n", "   USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \n", "0           4.221           3.989          3.8125           3.680  \n", "1           4.221           3.989          3.8075           3.651  \n", "2           4.269           4.041          3.8580           3.698  \n", "3           4.258           4.016          3.8330           3.676  \n", "4           4.269           4.042          3.8680           3.714  \n", "\n", "[5 rows x 26 columns]"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.iloc[0,1] = 100\n", "bbg_bar_data"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>USOSFR5 Curncy</th>\n", "      <th>USOSFR6 Curncy</th>\n", "      <th>USOSFR7 Curncy</th>\n", "      <th>USOSFR8 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR25 Curncy</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-10-14</td>\n", "      <td>4.8390</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.655</td>\n", "      <td>4.4560</td>\n", "      <td>4.239</td>\n", "      <td>4.001</td>\n", "      <td>3.8270</td>\n", "      <td>3.700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-10-15</td>\n", "      <td>4.8420</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-10-15</td>\n", "      <td>100.0000</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>3.5300</td>\n", "      <td>3.5241</td>\n", "      <td>3.5275</td>\n", "      <td>3.5365</td>\n", "      <td>...</td>\n", "      <td>3.5807</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.628</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8125</td>\n", "      <td>3.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-10-16</td>\n", "      <td>4.8486</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>3.5208</td>\n", "      <td>3.5143</td>\n", "      <td>3.5165</td>\n", "      <td>3.5247</td>\n", "      <td>...</td>\n", "      <td>3.5653</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.634</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.989</td>\n", "      <td>3.8075</td>\n", "      <td>3.651</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-10-17</td>\n", "      <td>4.8505</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>3.5740</td>\n", "      <td>3.5730</td>\n", "      <td>3.5795</td>\n", "      <td>3.5912</td>\n", "      <td>...</td>\n", "      <td>3.6491</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.647</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.041</td>\n", "      <td>3.8580</td>\n", "      <td>3.698</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2024-10-18</td>\n", "      <td>4.8490</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>3.5610</td>\n", "      <td>3.5626</td>\n", "      <td>3.5712</td>\n", "      <td>3.5842</td>\n", "      <td>...</td>\n", "      <td>3.6525</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.635</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.016</td>\n", "      <td>3.8330</td>\n", "      <td>3.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2024-10-21</td>\n", "      <td>4.8491</td>\n", "      <td>3.8998</td>\n", "      <td>3.7861</td>\n", "      <td>3.6692</td>\n", "      <td>3.6203</td>\n", "      <td>3.6044</td>\n", "      <td>3.6063</td>\n", "      <td>3.6157</td>\n", "      <td>3.6292</td>\n", "      <td>...</td>\n", "      <td>3.6980</td>\n", "      <td>3.6245</td>\n", "      <td>3.4411</td>\n", "      <td>3.2605</td>\n", "      <td>4.644</td>\n", "      <td>4.4730</td>\n", "      <td>4.269</td>\n", "      <td>4.042</td>\n", "      <td>3.8680</td>\n", "      <td>3.714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7 rows × 26 columns</p>\n", "</div>"], "text/plain": ["         date  USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "0  2024-10-14           4.8390              NaN             NaN   \n", "1  2024-10-15           4.8420           3.8710          3.7486   \n", "2  2024-10-15         100.0000           3.8710          3.7486   \n", "3  2024-10-16           4.8486           3.8625          3.7405   \n", "4  2024-10-17           4.8505           3.8941          3.7751   \n", "5  2024-10-18           4.8490           3.8707          3.7522   \n", "6  2024-10-21           4.8491           3.8998          3.7861   \n", "\n", "   USOSFR3 Curncy  USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  \\\n", "0             NaN             NaN             NaN             NaN   \n", "1          3.6178          3.5564          3.5300          3.5241   \n", "2          3.6178          3.5564          3.5300          3.5241   \n", "3          3.6096          3.5479          3.5208          3.5143   \n", "4          3.6501          3.5948          3.5740          3.5730   \n", "5          3.6296          3.5781          3.5610          3.5626   \n", "6          3.6692          3.6203          3.6044          3.6063   \n", "\n", "   USOSFR7 Curncy  USOSFR8 Curncy  ...  USOSFR25 Curncy  USOSFR30 Curncy  \\\n", "0             NaN             NaN  ...              NaN              NaN   \n", "1          3.5275          3.5365  ...           3.5807           3.5047   \n", "2          3.5275          3.5365  ...           3.5807           3.5047   \n", "3          3.5165          3.5247  ...           3.5653           3.4894   \n", "4          3.5795          3.5912  ...           3.6491           3.5749   \n", "5          3.5712          3.5842  ...           3.6525           3.5790   \n", "6          3.6157          3.6292  ...           3.6980           3.6245   \n", "\n", "   USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "0              NaN              NaN           4.655          4.4560   \n", "1           3.3215           3.1395           4.628          4.4375   \n", "2           3.3215           3.1395           4.628          4.4375   \n", "3           3.3055           3.1255           4.634          4.4410   \n", "4           3.3915           3.2092           4.647          4.4850   \n", "5           3.3956           3.2143           4.635          4.4640   \n", "6           3.4411           3.2605           4.644          4.4730   \n", "\n", "   USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \n", "0           4.239           4.001          3.8270           3.700  \n", "1           4.221           3.989          3.8125           3.680  \n", "2           4.221           3.989          3.8125           3.680  \n", "3           4.221           3.989          3.8075           3.651  \n", "4           4.269           4.041          3.8580           3.698  \n", "5           4.258           4.016          3.8330           3.676  \n", "6           4.269           4.042          3.8680           3.714  \n", "\n", "[7 rows x 26 columns]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.merge(bbg_bar_data, bbg_bar_data_2, how = 'outer', left_on=list(bbg_bar_data.columns), right_on=list(bbg_bar_data_2.columns))"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price...\n", "Loading close price from 2024-01-01\n", "Opening cached curve benchmark market data from bbg:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\n"]}], "source": ["benchmarks_meta_data, bbg_bar_data = loader.load_close_px_for_period(dt.date(2024,1,3),dt.date(2024,10,13))"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'effective_date': datetime.date(2024, 1, 1),\n", "  'benchmarks':        instrument_type maturities           ticker\n", "  0   OvernightIndexSwap         1W  USOSFR1Z Curncy\n", "  1   OvernightIndexSwap        18M  USOSFR1F Curncy\n", "  2   OvernightIndexSwap         2Y   USOSFR2 Curncy\n", "  3   OvernightIndexSwap         3Y   USOSFR3 Curncy\n", "  4   OvernightIndexSwap         4Y   USOSFR4 Curncy\n", "  5   OvernightIndexSwap         5Y   USOSFR5 Curncy\n", "  6   OvernightIndexSwap         6Y   USOSFR6 Curncy\n", "  7   OvernightIndexSwap         7Y   USOSFR7 Curncy\n", "  8   OvernightIndexSwap         8Y   USOSFR8 Curncy\n", "  9   OvernightIndexSwap         9Y   USOSFR9 Curncy\n", "  10  OvernightIndexSwap        10Y  USOSFR10 Curncy\n", "  11  OvernightIndexSwap        11Y  USOSFR11 Curncy\n", "  12  OvernightIndexSwap        12Y  USOSFR12 Curncy\n", "  13  OvernightIndexSwap        15Y  USOSFR15 Curncy\n", "  14  OvernightIndexSwap        20Y  USOSFR20 Curncy\n", "  15  OvernightIndexSwap        25Y  USOSFR25 Curncy\n", "  16  OvernightIndexSwap        30Y  USOSFR30 Curncy\n", "  17  OvernightIndexSwap        40Y  USOSFR40 Curncy\n", "  18  OvernightIndexSwap        50Y  USOSFR50 Curncy\n", "  19     MeetingDateSwap         s1   USSOSR1 Curncy\n", "  20     MeetingDateSwap         s2   USSOSR2 Curncy\n", "  21     MeetingDateSwap         s3   USSOSR3 Curncy\n", "  22     MeetingDateSwap         s4   USSOSR4 Curncy\n", "  23     MeetingDateSwap         s5   USSOSR5 Curncy\n", "  24     MeetingDateSwap         s6   USSOSR6 Curncy}]"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmarks_meta_data"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["[            USOSFR1Z Curncy  USOSFR1F Curncy  USOSFR2 Curncy  USOSFR3 Curncy  \\\n", " 2024-01-01              NaN              NaN             NaN             NaN   \n", " 2024-01-02           5.3385           4.4065          4.1410          3.8292   \n", " 2024-01-03           5.3380           4.4334          4.1609          3.8383   \n", " 2024-01-04           5.3312           4.4841          4.2227          3.9160   \n", " 2024-01-05           5.3266           4.4685          4.2161          3.9220   \n", " ...                     ...              ...             ...             ...   \n", " 2024-10-14           4.8390              NaN             NaN             NaN   \n", " 2024-10-15           4.8420           3.8710          3.7486          3.6178   \n", " 2024-10-16           4.8486           3.8625          3.7405          3.6096   \n", " 2024-10-17           4.8505           3.8941          3.7751          3.6501   \n", " 2024-10-18           4.8490           3.8707          3.7522          3.6296   \n", " \n", "             USOSFR4 Curncy  USOSFR5 Curncy  USOSFR6 Curncy  USOSFR7 Curncy  \\\n", " 2024-01-01             NaN             NaN             NaN             NaN   \n", " 2024-01-02          3.6725          3.5925          3.5537          3.5315   \n", " 2024-01-03          3.6806          3.5982          3.5590          3.5371   \n", " 2024-01-04          3.7638          3.6848          3.6469          3.6256   \n", " 2024-01-05          3.7795          3.7066          3.6730          3.6562   \n", " ...                    ...             ...             ...             ...   \n", " 2024-10-14             NaN             NaN             NaN             NaN   \n", " 2024-10-15          3.5564          3.5300          3.5241          3.5275   \n", " 2024-10-16          3.5479          3.5208          3.5143          3.5165   \n", " 2024-10-17          3.5948          3.5740          3.5730          3.5795   \n", " 2024-10-18          3.5781          3.5610          3.5626          3.5712   \n", " \n", "             USOSFR8 Curncy  USOSFR9 Curncy  ...  USOSFR25 Curncy  \\\n", " 2024-01-01             NaN             NaN  ...              NaN   \n", " 2024-01-02          3.5198          3.5163  ...           3.4362   \n", " 2024-01-03          3.5257          3.5227  ...           3.4619   \n", " 2024-01-04          3.6143          3.6110  ...           3.5455   \n", " 2024-01-05          3.6486          3.6482  ...           3.5873   \n", " ...                    ...             ...  ...              ...   \n", " 2024-10-14             NaN             NaN  ...              NaN   \n", " 2024-10-15          3.5365          3.5483  ...           3.5807   \n", " 2024-10-16          3.5247          3.5363  ...           3.5653   \n", " 2024-10-17          3.5912          3.6052  ...           3.6491   \n", " 2024-10-18          3.5842          3.6003  ...           3.6525   \n", " \n", "             USOSFR30 Curncy  USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  \\\n", " 2024-01-01              NaN              NaN              NaN             NaN   \n", " 2024-01-02           3.3494           3.1410           2.9283           5.336   \n", " 2024-01-03           3.3785           3.1700           2.9573           5.339   \n", " 2024-01-04           3.4606           3.2539           3.0346           5.341   \n", " 2024-01-05           3.5020           3.2960           3.0812           5.339   \n", " ...                     ...              ...              ...             ...   \n", " 2024-10-14              NaN              NaN              NaN           4.655   \n", " 2024-10-15           3.5047           3.3215           3.1395           4.628   \n", " 2024-10-16           3.4894           3.3055           3.1255           4.634   \n", " 2024-10-17           3.5749           3.3915           3.2092           4.647   \n", " 2024-10-18           3.5790           3.3956           3.2143           4.635   \n", " \n", "             USSOSR2 Curncy  USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  \\\n", " 2024-01-01             NaN             NaN             NaN             NaN   \n", " 2024-01-02          5.1630           4.915           4.678          4.4750   \n", " 2024-01-03          5.1860           4.954           4.728          4.5160   \n", " 2024-01-04          5.1960           4.989           4.764          4.5785   \n", " 2024-01-05          5.1820           4.963           4.751          4.5375   \n", " ...                    ...             ...             ...             ...   \n", " 2024-10-14          4.4560           4.239           4.001          3.8270   \n", " 2024-10-15          4.4375           4.221           3.989          3.8125   \n", " 2024-10-16          4.4410           4.221           3.989          3.8075   \n", " 2024-10-17          4.4850           4.269           4.041          3.8580   \n", " 2024-10-18          4.4640           4.258           4.016          3.8330   \n", " \n", "             USSOSR6 Curncy  \n", " 2024-01-01           4.152  \n", " 2024-01-02           4.232  \n", " 2024-01-03           4.281  \n", " 2024-01-04           4.347  \n", " 2024-01-05           4.321  \n", " ...                    ...  \n", " 2024-10-14           3.700  \n", " 2024-10-15           3.680  \n", " 2024-10-16           3.651  \n", " 2024-10-17           3.698  \n", " 2024-10-18           3.676  \n", " \n", " [210 rows x 25 columns]]"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-10-24, 2024-10-28]\n", "Loading close price from 2024-01-01\n", "Opening cached curve benchmark data from bbg, FILE:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\n", "Some cached curve benchmark history missing, read data from bbg from 2024-10-28\n", "Storing new curve benchmark market data from bbg:  c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\USD_SOFR_20240101_marketclose.parquet\n"]}], "source": ["loader = curveBenchmarkLoader(\"USD_SOFR\")\n", "benchmarks_meta_data, bbg_bar_data = loader.load_close_px_for_period(dt.date(2024,10,24), dt.date(2024,10,28))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'effective_date': datetime.date(2024, 1, 1),\n", " 'benchmarks':        instrument_type maturities           ticker\n", " 0   OvernightIndexSwap         1W  USOSFR1Z Curncy\n", " 1   OvernightIndexSwap         2W  USOSFR2Z Curncy\n", " 2   OvernightIndexSwap         3W  USOSFR3Z Curncy\n", " 3   OvernightIndexSwap         1M   USOSFRA Curncy\n", " 4   OvernightIndexSwap         2M   USOSFRB Curncy\n", " 5   OvernightIndexSwap         3M   USOSFRC Curncy\n", " 6   OvernightIndexSwap        18M  USOSFR1F Curncy\n", " 7   OvernightIndexSwap         2Y   USOSFR2 Curncy\n", " 8   OvernightIndexSwap         3Y   USOSFR3 Curncy\n", " 9   OvernightIndexSwap         4Y   USOSFR4 Curncy\n", " 10  OvernightIndexSwap         5Y   USOSFR5 Curncy\n", " 11  OvernightIndexSwap         6Y   USOSFR6 Curncy\n", " 12  OvernightIndexSwap         7Y   USOSFR7 Curncy\n", " 13  OvernightIndexSwap         8Y   USOSFR8 Curncy\n", " 14  OvernightIndexSwap         9Y   USOSFR9 Curncy\n", " 15  OvernightIndexSwap        10Y  USOSFR10 Curncy\n", " 16  OvernightIndexSwap        11Y  USOSFR11 Curncy\n", " 17  OvernightIndexSwap        12Y  USOSFR12 Curncy\n", " 18  OvernightIndexSwap        15Y  USOSFR15 Curncy\n", " 19  OvernightIndexSwap        20Y  USOSFR20 Curncy\n", " 20  OvernightIndexSwap        25Y  USOSFR25 Curncy\n", " 21  OvernightIndexSwap        30Y  USOSFR30 Curncy\n", " 22  OvernightIndexSwap        40Y  USOSFR40 Curncy\n", " 23  OvernightIndexSwap        50Y  USOSFR50 Curncy\n", " 24     MeetingDateSwap         s1   USSOSR1 Curncy\n", " 25     MeetingDateSwap         s2   USSOSR2 Curncy\n", " 26     MeetingDateSwap         s3   USSOSR3 Curncy\n", " 27     MeetingDateSwap         s4   USSOSR4 Curncy\n", " 28     MeetingDateSwap         s5   USSOSR5 Curncy\n", " 29     MeetingDateSwap         s6   USSOSR6 Curncy\n", " 30     MeetingDateSwap         s7   USSOSR7 Curncy}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmarks_meta_data[0]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-01</th>\n", "      <td>4.8493</td>\n", "      <td>4.8539</td>\n", "      <td>4.8554</td>\n", "      <td>4.8576</td>\n", "      <td>4.7273</td>\n", "      <td>4.6156</td>\n", "      <td>3.5433</td>\n", "      <td>3.3985</td>\n", "      <td>3.2639</td>\n", "      <td>3.2125</td>\n", "      <td>...</td>\n", "      <td>3.2555</td>\n", "      <td>3.0735</td>\n", "      <td>2.8951</td>\n", "      <td>4.5276</td>\n", "      <td>4.1960</td>\n", "      <td>3.861</td>\n", "      <td>3.5768</td>\n", "      <td>3.3730</td>\n", "      <td>3.2250</td>\n", "      <td>3.1125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-02</th>\n", "      <td>4.8507</td>\n", "      <td>4.8487</td>\n", "      <td>4.8510</td>\n", "      <td>4.8530</td>\n", "      <td>4.7234</td>\n", "      <td>4.6049</td>\n", "      <td>3.5753</td>\n", "      <td>3.4388</td>\n", "      <td>3.3080</td>\n", "      <td>3.2574</td>\n", "      <td>...</td>\n", "      <td>3.3116</td>\n", "      <td>3.1308</td>\n", "      <td>2.9521</td>\n", "      <td>4.5380</td>\n", "      <td>4.2120</td>\n", "      <td>3.881</td>\n", "      <td>3.6076</td>\n", "      <td>3.4130</td>\n", "      <td>3.2740</td>\n", "      <td>3.1590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-03</th>\n", "      <td>4.8355</td>\n", "      <td>4.8370</td>\n", "      <td>4.8395</td>\n", "      <td>4.8443</td>\n", "      <td>4.6994</td>\n", "      <td>4.5936</td>\n", "      <td>3.6331</td>\n", "      <td>3.5001</td>\n", "      <td>3.3726</td>\n", "      <td>3.3240</td>\n", "      <td>...</td>\n", "      <td>3.3565</td>\n", "      <td>3.1759</td>\n", "      <td>2.9970</td>\n", "      <td>4.5400</td>\n", "      <td>4.2365</td>\n", "      <td>3.930</td>\n", "      <td>3.6790</td>\n", "      <td>3.4970</td>\n", "      <td>3.3660</td>\n", "      <td>3.2540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-04</th>\n", "      <td>4.8355</td>\n", "      <td>4.8375</td>\n", "      <td>4.8400</td>\n", "      <td>4.8463</td>\n", "      <td>4.7376</td>\n", "      <td>4.6488</td>\n", "      <td>3.8514</td>\n", "      <td>3.7265</td>\n", "      <td>3.5905</td>\n", "      <td>3.5205</td>\n", "      <td>...</td>\n", "      <td>3.4261</td>\n", "      <td>3.2437</td>\n", "      <td>3.0645</td>\n", "      <td>4.6190</td>\n", "      <td>4.3490</td>\n", "      <td>4.109</td>\n", "      <td>3.9099</td>\n", "      <td>3.7630</td>\n", "      <td>3.6410</td>\n", "      <td>3.5340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-07</th>\n", "      <td>4.8400</td>\n", "      <td>4.8430</td>\n", "      <td>4.8445</td>\n", "      <td>4.8263</td>\n", "      <td>4.7583</td>\n", "      <td>4.6788</td>\n", "      <td>3.9200</td>\n", "      <td>3.7989</td>\n", "      <td>3.6583</td>\n", "      <td>3.5859</td>\n", "      <td>...</td>\n", "      <td>3.4871</td>\n", "      <td>3.3035</td>\n", "      <td>3.1262</td>\n", "      <td>4.6580</td>\n", "      <td>4.4085</td>\n", "      <td>4.189</td>\n", "      <td>3.9835</td>\n", "      <td>3.8330</td>\n", "      <td>3.7150</td>\n", "      <td>3.6110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-08</th>\n", "      <td>4.8420</td>\n", "      <td>4.8464</td>\n", "      <td>4.8480</td>\n", "      <td>4.8270</td>\n", "      <td>4.7538</td>\n", "      <td>4.6703</td>\n", "      <td>3.8905</td>\n", "      <td>3.7670</td>\n", "      <td>3.6319</td>\n", "      <td>3.5616</td>\n", "      <td>...</td>\n", "      <td>3.4744</td>\n", "      <td>3.2890</td>\n", "      <td>3.1099</td>\n", "      <td>4.6540</td>\n", "      <td>4.4080</td>\n", "      <td>4.172</td>\n", "      <td>3.9560</td>\n", "      <td>3.8010</td>\n", "      <td>3.6730</td>\n", "      <td>3.5710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-09</th>\n", "      <td>4.8400</td>\n", "      <td>4.8432</td>\n", "      <td>4.8455</td>\n", "      <td>4.8252</td>\n", "      <td>4.7559</td>\n", "      <td>4.6755</td>\n", "      <td>3.9556</td>\n", "      <td>3.8376</td>\n", "      <td>3.7038</td>\n", "      <td>3.6325</td>\n", "      <td>...</td>\n", "      <td>3.5234</td>\n", "      <td>3.3389</td>\n", "      <td>3.1580</td>\n", "      <td>4.6635</td>\n", "      <td>4.4375</td>\n", "      <td>4.230</td>\n", "      <td>4.0300</td>\n", "      <td>3.8865</td>\n", "      <td>3.7610</td>\n", "      <td>3.6640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-10</th>\n", "      <td>4.8405</td>\n", "      <td>4.8430</td>\n", "      <td>4.8460</td>\n", "      <td>4.8033</td>\n", "      <td>4.7362</td>\n", "      <td>4.6555</td>\n", "      <td>3.8879</td>\n", "      <td>3.7705</td>\n", "      <td>3.6438</td>\n", "      <td>3.5845</td>\n", "      <td>...</td>\n", "      <td>3.5400</td>\n", "      <td>3.3560</td>\n", "      <td>3.1758</td>\n", "      <td>4.6550</td>\n", "      <td>4.4455</td>\n", "      <td>4.205</td>\n", "      <td>3.9800</td>\n", "      <td>3.8055</td>\n", "      <td>3.6780</td>\n", "      <td>3.5730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11</th>\n", "      <td>4.8390</td>\n", "      <td>4.8412</td>\n", "      <td>4.8435</td>\n", "      <td>4.7849</td>\n", "      <td>4.7268</td>\n", "      <td>4.6473</td>\n", "      <td>3.8781</td>\n", "      <td>3.7620</td>\n", "      <td>3.6417</td>\n", "      <td>3.5908</td>\n", "      <td>...</td>\n", "      <td>3.5903</td>\n", "      <td>3.4070</td>\n", "      <td>3.2252</td>\n", "      <td>4.6470</td>\n", "      <td>4.4460</td>\n", "      <td>4.215</td>\n", "      <td>3.9789</td>\n", "      <td>3.8015</td>\n", "      <td>3.6670</td>\n", "      <td>3.5620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-14</th>\n", "      <td>4.8390</td>\n", "      <td>4.8412</td>\n", "      <td>4.8435</td>\n", "      <td>4.7878</td>\n", "      <td>NaN</td>\n", "      <td>4.6553</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.6550</td>\n", "      <td>4.4560</td>\n", "      <td>4.239</td>\n", "      <td>4.0010</td>\n", "      <td>3.8270</td>\n", "      <td>3.7000</td>\n", "      <td>3.5850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>4.8455</td>\n", "      <td>4.8500</td>\n", "      <td>4.7793</td>\n", "      <td>4.7116</td>\n", "      <td>4.6325</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>...</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.6280</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.9890</td>\n", "      <td>3.8125</td>\n", "      <td>3.6800</td>\n", "      <td>3.5640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>4.8510</td>\n", "      <td>4.8510</td>\n", "      <td>4.7824</td>\n", "      <td>4.7163</td>\n", "      <td>4.6300</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>...</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.6340</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.9890</td>\n", "      <td>3.8075</td>\n", "      <td>3.6510</td>\n", "      <td>3.5720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>4.8532</td>\n", "      <td>4.8177</td>\n", "      <td>4.7672</td>\n", "      <td>4.7016</td>\n", "      <td>4.6401</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>...</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.6470</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.0410</td>\n", "      <td>3.8580</td>\n", "      <td>3.6980</td>\n", "      <td>3.5930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>4.8512</td>\n", "      <td>4.8135</td>\n", "      <td>4.7537</td>\n", "      <td>4.6895</td>\n", "      <td>4.6245</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>...</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.6350</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.0160</td>\n", "      <td>3.8330</td>\n", "      <td>3.6760</td>\n", "      <td>3.5755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8480</td>\n", "      <td>4.8595</td>\n", "      <td>4.8065</td>\n", "      <td>4.7516</td>\n", "      <td>4.7039</td>\n", "      <td>4.6460</td>\n", "      <td>3.9523</td>\n", "      <td>3.8422</td>\n", "      <td>3.7292</td>\n", "      <td>3.6838</td>\n", "      <td>...</td>\n", "      <td>3.6821</td>\n", "      <td>3.4990</td>\n", "      <td>3.3187</td>\n", "      <td>4.6590</td>\n", "      <td>4.5000</td>\n", "      <td>4.307</td>\n", "      <td>4.0990</td>\n", "      <td>3.9240</td>\n", "      <td>3.7765</td>\n", "      <td>3.6830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-22</th>\n", "      <td>4.8490</td>\n", "      <td>4.8535</td>\n", "      <td>4.7915</td>\n", "      <td>4.7404</td>\n", "      <td>4.6869</td>\n", "      <td>4.6296</td>\n", "      <td>3.9515</td>\n", "      <td>3.8471</td>\n", "      <td>3.7431</td>\n", "      <td>3.6998</td>\n", "      <td>...</td>\n", "      <td>3.6777</td>\n", "      <td>3.4945</td>\n", "      <td>3.3138</td>\n", "      <td>4.6429</td>\n", "      <td>4.4875</td>\n", "      <td>4.288</td>\n", "      <td>4.0845</td>\n", "      <td>3.9200</td>\n", "      <td>3.7795</td>\n", "      <td>3.6880</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-23</th>\n", "      <td>4.8558</td>\n", "      <td>4.8590</td>\n", "      <td>4.7852</td>\n", "      <td>4.7414</td>\n", "      <td>4.6819</td>\n", "      <td>4.6288</td>\n", "      <td>3.9858</td>\n", "      <td>3.8910</td>\n", "      <td>3.7945</td>\n", "      <td>3.7509</td>\n", "      <td>...</td>\n", "      <td>3.6985</td>\n", "      <td>3.5152</td>\n", "      <td>3.3330</td>\n", "      <td>4.6445</td>\n", "      <td>4.4940</td>\n", "      <td>4.307</td>\n", "      <td>4.1100</td>\n", "      <td>3.9480</td>\n", "      <td>3.8180</td>\n", "      <td>3.7385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-24</th>\n", "      <td>4.8560</td>\n", "      <td>4.7971</td>\n", "      <td>4.7485</td>\n", "      <td>4.7106</td>\n", "      <td>4.6554</td>\n", "      <td>4.6079</td>\n", "      <td>3.9625</td>\n", "      <td>3.8686</td>\n", "      <td>3.7712</td>\n", "      <td>3.7241</td>\n", "      <td>...</td>\n", "      <td>3.6546</td>\n", "      <td>3.4707</td>\n", "      <td>3.2895</td>\n", "      <td>4.6380</td>\n", "      <td>4.4810</td>\n", "      <td>4.285</td>\n", "      <td>4.0900</td>\n", "      <td>3.9325</td>\n", "      <td>3.7990</td>\n", "      <td>3.7200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>18 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2024-10-01           4.8493           4.8539           4.8554          4.8576   \n", "2024-10-02           4.8507           4.8487           4.8510          4.8530   \n", "2024-10-03           4.8355           4.8370           4.8395          4.8443   \n", "2024-10-04           4.8355           4.8375           4.8400          4.8463   \n", "2024-10-07           4.8400           4.8430           4.8445          4.8263   \n", "2024-10-08           4.8420           4.8464           4.8480          4.8270   \n", "2024-10-09           4.8400           4.8432           4.8455          4.8252   \n", "2024-10-10           4.8405           4.8430           4.8460          4.8033   \n", "2024-10-11           4.8390           4.8412           4.8435          4.7849   \n", "2024-10-14           4.8390           4.8412           4.8435          4.7878   \n", "2024-10-15           4.8420           4.8455           4.8500          4.7793   \n", "2024-10-16           4.8486           4.8510           4.8510          4.7824   \n", "2024-10-17           4.8505           4.8532           4.8177          4.7672   \n", "2024-10-18           4.8490           4.8512           4.8135          4.7537   \n", "2024-10-21           4.8480           4.8595           4.8065          4.7516   \n", "2024-10-22           4.8490           4.8535           4.7915          4.7404   \n", "2024-10-23           4.8558           4.8590           4.7852          4.7414   \n", "2024-10-24           4.8560           4.7971           4.7485          4.7106   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2024-10-01          4.7273          4.6156           3.5433          3.3985   \n", "2024-10-02          4.7234          4.6049           3.5753          3.4388   \n", "2024-10-03          4.6994          4.5936           3.6331          3.5001   \n", "2024-10-04          4.7376          4.6488           3.8514          3.7265   \n", "2024-10-07          4.7583          4.6788           3.9200          3.7989   \n", "2024-10-08          4.7538          4.6703           3.8905          3.7670   \n", "2024-10-09          4.7559          4.6755           3.9556          3.8376   \n", "2024-10-10          4.7362          4.6555           3.8879          3.7705   \n", "2024-10-11          4.7268          4.6473           3.8781          3.7620   \n", "2024-10-14             NaN          4.6553              NaN             NaN   \n", "2024-10-15          4.7116          4.6325           3.8710          3.7486   \n", "2024-10-16          4.7163          4.6300           3.8625          3.7405   \n", "2024-10-17          4.7016          4.6401           3.8941          3.7751   \n", "2024-10-18          4.6895          4.6245           3.8707          3.7522   \n", "2024-10-21          4.7039          4.6460           3.9523          3.8422   \n", "2024-10-22          4.6869          4.6296           3.9515          3.8471   \n", "2024-10-23          4.6819          4.6288           3.9858          3.8910   \n", "2024-10-24          4.6554          4.6079           3.9625          3.8686   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2024-10-01          3.2639          3.2125  ...           3.2555   \n", "2024-10-02          3.3080          3.2574  ...           3.3116   \n", "2024-10-03          3.3726          3.3240  ...           3.3565   \n", "2024-10-04          3.5905          3.5205  ...           3.4261   \n", "2024-10-07          3.6583          3.5859  ...           3.4871   \n", "2024-10-08          3.6319          3.5616  ...           3.4744   \n", "2024-10-09          3.7038          3.6325  ...           3.5234   \n", "2024-10-10          3.6438          3.5845  ...           3.5400   \n", "2024-10-11          3.6417          3.5908  ...           3.5903   \n", "2024-10-14             NaN             NaN  ...              NaN   \n", "2024-10-15          3.6178          3.5564  ...           3.5047   \n", "2024-10-16          3.6096          3.5479  ...           3.4894   \n", "2024-10-17          3.6501          3.5948  ...           3.5749   \n", "2024-10-18          3.6296          3.5781  ...           3.5790   \n", "2024-10-21          3.7292          3.6838  ...           3.6821   \n", "2024-10-22          3.7431          3.6998  ...           3.6777   \n", "2024-10-23          3.7945          3.7509  ...           3.6985   \n", "2024-10-24          3.7712          3.7241  ...           3.6546   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2024-10-01           3.0735           2.8951          4.5276          4.1960   \n", "2024-10-02           3.1308           2.9521          4.5380          4.2120   \n", "2024-10-03           3.1759           2.9970          4.5400          4.2365   \n", "2024-10-04           3.2437           3.0645          4.6190          4.3490   \n", "2024-10-07           3.3035           3.1262          4.6580          4.4085   \n", "2024-10-08           3.2890           3.1099          4.6540          4.4080   \n", "2024-10-09           3.3389           3.1580          4.6635          4.4375   \n", "2024-10-10           3.3560           3.1758          4.6550          4.4455   \n", "2024-10-11           3.4070           3.2252          4.6470          4.4460   \n", "2024-10-14              NaN              NaN          4.6550          4.4560   \n", "2024-10-15           3.3215           3.1395          4.6280          4.4375   \n", "2024-10-16           3.3055           3.1255          4.6340          4.4410   \n", "2024-10-17           3.3915           3.2092          4.6470          4.4850   \n", "2024-10-18           3.3956           3.2143          4.6350          4.4640   \n", "2024-10-21           3.4990           3.3187          4.6590          4.5000   \n", "2024-10-22           3.4945           3.3138          4.6429          4.4875   \n", "2024-10-23           3.5152           3.3330          4.6445          4.4940   \n", "2024-10-24           3.4707           3.2895          4.6380          4.4810   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2024-10-01           3.861          3.5768          3.3730          3.2250   \n", "2024-10-02           3.881          3.6076          3.4130          3.2740   \n", "2024-10-03           3.930          3.6790          3.4970          3.3660   \n", "2024-10-04           4.109          3.9099          3.7630          3.6410   \n", "2024-10-07           4.189          3.9835          3.8330          3.7150   \n", "2024-10-08           4.172          3.9560          3.8010          3.6730   \n", "2024-10-09           4.230          4.0300          3.8865          3.7610   \n", "2024-10-10           4.205          3.9800          3.8055          3.6780   \n", "2024-10-11           4.215          3.9789          3.8015          3.6670   \n", "2024-10-14           4.239          4.0010          3.8270          3.7000   \n", "2024-10-15           4.221          3.9890          3.8125          3.6800   \n", "2024-10-16           4.221          3.9890          3.8075          3.6510   \n", "2024-10-17           4.269          4.0410          3.8580          3.6980   \n", "2024-10-18           4.258          4.0160          3.8330          3.6760   \n", "2024-10-21           4.307          4.0990          3.9240          3.7765   \n", "2024-10-22           4.288          4.0845          3.9200          3.7795   \n", "2024-10-23           4.307          4.1100          3.9480          3.8180   \n", "2024-10-24           4.285          4.0900          3.9325          3.7990   \n", "\n", "            USSOSR7 Curncy  \n", "2024-10-01          3.1125  \n", "2024-10-02          3.1590  \n", "2024-10-03          3.2540  \n", "2024-10-04          3.5340  \n", "2024-10-07          3.6110  \n", "2024-10-08          3.5710  \n", "2024-10-09          3.6640  \n", "2024-10-10          3.5730  \n", "2024-10-11          3.5620  \n", "2024-10-14          3.5850  \n", "2024-10-15          3.5640  \n", "2024-10-16          3.5720  \n", "2024-10-17          3.5930  \n", "2024-10-18          3.5755  \n", "2024-10-21          3.6830  \n", "2024-10-22          3.6880  \n", "2024-10-23          3.7385  \n", "2024-10-24          3.7200  \n", "\n", "[18 rows x 31 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data[0].loc[dt.date(2024,10,1):dt.date(2024,10,29)]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>USOSFR1Z Curncy</th>\n", "      <th>USOSFR2Z Curncy</th>\n", "      <th>USOSFR3Z Curncy</th>\n", "      <th>USOSFRA Curncy</th>\n", "      <th>USOSFRB Curncy</th>\n", "      <th>USOSFRC Curncy</th>\n", "      <th>USOSFR1F <PERSON>cy</th>\n", "      <th>USOSFR2 Curncy</th>\n", "      <th>USOSFR3 Curncy</th>\n", "      <th>USOSFR4 Curncy</th>\n", "      <th>...</th>\n", "      <th>USOSFR30 Curncy</th>\n", "      <th>USOSFR40 Curncy</th>\n", "      <th>USOSFR50 Curncy</th>\n", "      <th>USSOSR1 Curncy</th>\n", "      <th>USSOSR2 Curncy</th>\n", "      <th>USSOSR3 Curncy</th>\n", "      <th>USSOSR4 Curncy</th>\n", "      <th>USSOSR5 Curncy</th>\n", "      <th>USSOSR6 Curncy</th>\n", "      <th>USSOSR7 Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-10-01</th>\n", "      <td>4.8493</td>\n", "      <td>4.8539</td>\n", "      <td>4.8554</td>\n", "      <td>4.8576</td>\n", "      <td>4.7273</td>\n", "      <td>4.6156</td>\n", "      <td>3.5433</td>\n", "      <td>3.3985</td>\n", "      <td>3.2639</td>\n", "      <td>3.2125</td>\n", "      <td>...</td>\n", "      <td>3.2555</td>\n", "      <td>3.0735</td>\n", "      <td>2.8951</td>\n", "      <td>4.5276</td>\n", "      <td>4.1960</td>\n", "      <td>3.861</td>\n", "      <td>3.5768</td>\n", "      <td>3.3730</td>\n", "      <td>3.2250</td>\n", "      <td>3.1125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-02</th>\n", "      <td>4.8507</td>\n", "      <td>4.8487</td>\n", "      <td>4.8510</td>\n", "      <td>4.8530</td>\n", "      <td>4.7234</td>\n", "      <td>4.6049</td>\n", "      <td>3.5753</td>\n", "      <td>3.4388</td>\n", "      <td>3.3080</td>\n", "      <td>3.2574</td>\n", "      <td>...</td>\n", "      <td>3.3116</td>\n", "      <td>3.1308</td>\n", "      <td>2.9521</td>\n", "      <td>4.5380</td>\n", "      <td>4.2120</td>\n", "      <td>3.881</td>\n", "      <td>3.6076</td>\n", "      <td>3.4130</td>\n", "      <td>3.2740</td>\n", "      <td>3.1590</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-03</th>\n", "      <td>4.8355</td>\n", "      <td>4.8370</td>\n", "      <td>4.8395</td>\n", "      <td>4.8443</td>\n", "      <td>4.6994</td>\n", "      <td>4.5936</td>\n", "      <td>3.6331</td>\n", "      <td>3.5001</td>\n", "      <td>3.3726</td>\n", "      <td>3.3240</td>\n", "      <td>...</td>\n", "      <td>3.3565</td>\n", "      <td>3.1759</td>\n", "      <td>2.9970</td>\n", "      <td>4.5400</td>\n", "      <td>4.2365</td>\n", "      <td>3.930</td>\n", "      <td>3.6790</td>\n", "      <td>3.4970</td>\n", "      <td>3.3660</td>\n", "      <td>3.2540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-04</th>\n", "      <td>4.8355</td>\n", "      <td>4.8375</td>\n", "      <td>4.8400</td>\n", "      <td>4.8463</td>\n", "      <td>4.7376</td>\n", "      <td>4.6488</td>\n", "      <td>3.8514</td>\n", "      <td>3.7265</td>\n", "      <td>3.5905</td>\n", "      <td>3.5205</td>\n", "      <td>...</td>\n", "      <td>3.4261</td>\n", "      <td>3.2437</td>\n", "      <td>3.0645</td>\n", "      <td>4.6190</td>\n", "      <td>4.3490</td>\n", "      <td>4.109</td>\n", "      <td>3.9099</td>\n", "      <td>3.7630</td>\n", "      <td>3.6410</td>\n", "      <td>3.5340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-07</th>\n", "      <td>4.8400</td>\n", "      <td>4.8430</td>\n", "      <td>4.8445</td>\n", "      <td>4.8263</td>\n", "      <td>4.7583</td>\n", "      <td>4.6788</td>\n", "      <td>3.9200</td>\n", "      <td>3.7989</td>\n", "      <td>3.6583</td>\n", "      <td>3.5859</td>\n", "      <td>...</td>\n", "      <td>3.4871</td>\n", "      <td>3.3035</td>\n", "      <td>3.1262</td>\n", "      <td>4.6580</td>\n", "      <td>4.4085</td>\n", "      <td>4.189</td>\n", "      <td>3.9835</td>\n", "      <td>3.8330</td>\n", "      <td>3.7150</td>\n", "      <td>3.6110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-08</th>\n", "      <td>4.8420</td>\n", "      <td>4.8464</td>\n", "      <td>4.8480</td>\n", "      <td>4.8270</td>\n", "      <td>4.7538</td>\n", "      <td>4.6703</td>\n", "      <td>3.8905</td>\n", "      <td>3.7670</td>\n", "      <td>3.6319</td>\n", "      <td>3.5616</td>\n", "      <td>...</td>\n", "      <td>3.4744</td>\n", "      <td>3.2890</td>\n", "      <td>3.1099</td>\n", "      <td>4.6540</td>\n", "      <td>4.4080</td>\n", "      <td>4.172</td>\n", "      <td>3.9560</td>\n", "      <td>3.8010</td>\n", "      <td>3.6730</td>\n", "      <td>3.5710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-09</th>\n", "      <td>4.8400</td>\n", "      <td>4.8432</td>\n", "      <td>4.8455</td>\n", "      <td>4.8252</td>\n", "      <td>4.7559</td>\n", "      <td>4.6755</td>\n", "      <td>3.9556</td>\n", "      <td>3.8376</td>\n", "      <td>3.7038</td>\n", "      <td>3.6325</td>\n", "      <td>...</td>\n", "      <td>3.5234</td>\n", "      <td>3.3389</td>\n", "      <td>3.1580</td>\n", "      <td>4.6635</td>\n", "      <td>4.4375</td>\n", "      <td>4.230</td>\n", "      <td>4.0300</td>\n", "      <td>3.8865</td>\n", "      <td>3.7610</td>\n", "      <td>3.6640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-10</th>\n", "      <td>4.8405</td>\n", "      <td>4.8430</td>\n", "      <td>4.8460</td>\n", "      <td>4.8033</td>\n", "      <td>4.7362</td>\n", "      <td>4.6555</td>\n", "      <td>3.8879</td>\n", "      <td>3.7705</td>\n", "      <td>3.6438</td>\n", "      <td>3.5845</td>\n", "      <td>...</td>\n", "      <td>3.5400</td>\n", "      <td>3.3560</td>\n", "      <td>3.1758</td>\n", "      <td>4.6550</td>\n", "      <td>4.4455</td>\n", "      <td>4.205</td>\n", "      <td>3.9800</td>\n", "      <td>3.8055</td>\n", "      <td>3.6780</td>\n", "      <td>3.5730</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-11</th>\n", "      <td>4.8390</td>\n", "      <td>4.8412</td>\n", "      <td>4.8435</td>\n", "      <td>4.7849</td>\n", "      <td>4.7268</td>\n", "      <td>4.6473</td>\n", "      <td>3.8781</td>\n", "      <td>3.7620</td>\n", "      <td>3.6417</td>\n", "      <td>3.5908</td>\n", "      <td>...</td>\n", "      <td>3.5903</td>\n", "      <td>3.4070</td>\n", "      <td>3.2252</td>\n", "      <td>4.6470</td>\n", "      <td>4.4460</td>\n", "      <td>4.215</td>\n", "      <td>3.9789</td>\n", "      <td>3.8015</td>\n", "      <td>3.6670</td>\n", "      <td>3.5620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-14</th>\n", "      <td>4.8390</td>\n", "      <td>4.8412</td>\n", "      <td>4.8435</td>\n", "      <td>4.7878</td>\n", "      <td>NaN</td>\n", "      <td>4.6553</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>4.6550</td>\n", "      <td>4.4560</td>\n", "      <td>4.239</td>\n", "      <td>4.0010</td>\n", "      <td>3.8270</td>\n", "      <td>3.7000</td>\n", "      <td>3.5850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-15</th>\n", "      <td>4.8420</td>\n", "      <td>4.8455</td>\n", "      <td>4.8500</td>\n", "      <td>4.7793</td>\n", "      <td>4.7116</td>\n", "      <td>4.6325</td>\n", "      <td>3.8710</td>\n", "      <td>3.7486</td>\n", "      <td>3.6178</td>\n", "      <td>3.5564</td>\n", "      <td>...</td>\n", "      <td>3.5047</td>\n", "      <td>3.3215</td>\n", "      <td>3.1395</td>\n", "      <td>4.6280</td>\n", "      <td>4.4375</td>\n", "      <td>4.221</td>\n", "      <td>3.9890</td>\n", "      <td>3.8125</td>\n", "      <td>3.6800</td>\n", "      <td>3.5640</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-16</th>\n", "      <td>4.8486</td>\n", "      <td>4.8510</td>\n", "      <td>4.8510</td>\n", "      <td>4.7824</td>\n", "      <td>4.7163</td>\n", "      <td>4.6300</td>\n", "      <td>3.8625</td>\n", "      <td>3.7405</td>\n", "      <td>3.6096</td>\n", "      <td>3.5479</td>\n", "      <td>...</td>\n", "      <td>3.4894</td>\n", "      <td>3.3055</td>\n", "      <td>3.1255</td>\n", "      <td>4.6340</td>\n", "      <td>4.4410</td>\n", "      <td>4.221</td>\n", "      <td>3.9890</td>\n", "      <td>3.8075</td>\n", "      <td>3.6510</td>\n", "      <td>3.5720</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-17</th>\n", "      <td>4.8505</td>\n", "      <td>4.8532</td>\n", "      <td>4.8177</td>\n", "      <td>4.7672</td>\n", "      <td>4.7016</td>\n", "      <td>4.6401</td>\n", "      <td>3.8941</td>\n", "      <td>3.7751</td>\n", "      <td>3.6501</td>\n", "      <td>3.5948</td>\n", "      <td>...</td>\n", "      <td>3.5749</td>\n", "      <td>3.3915</td>\n", "      <td>3.2092</td>\n", "      <td>4.6470</td>\n", "      <td>4.4850</td>\n", "      <td>4.269</td>\n", "      <td>4.0410</td>\n", "      <td>3.8580</td>\n", "      <td>3.6980</td>\n", "      <td>3.5930</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-18</th>\n", "      <td>4.8490</td>\n", "      <td>4.8512</td>\n", "      <td>4.8135</td>\n", "      <td>4.7537</td>\n", "      <td>4.6895</td>\n", "      <td>4.6245</td>\n", "      <td>3.8707</td>\n", "      <td>3.7522</td>\n", "      <td>3.6296</td>\n", "      <td>3.5781</td>\n", "      <td>...</td>\n", "      <td>3.5790</td>\n", "      <td>3.3956</td>\n", "      <td>3.2143</td>\n", "      <td>4.6350</td>\n", "      <td>4.4640</td>\n", "      <td>4.258</td>\n", "      <td>4.0160</td>\n", "      <td>3.8330</td>\n", "      <td>3.6760</td>\n", "      <td>3.5755</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-21</th>\n", "      <td>4.8480</td>\n", "      <td>4.8595</td>\n", "      <td>4.8065</td>\n", "      <td>4.7516</td>\n", "      <td>4.7039</td>\n", "      <td>4.6460</td>\n", "      <td>3.9523</td>\n", "      <td>3.8422</td>\n", "      <td>3.7292</td>\n", "      <td>3.6838</td>\n", "      <td>...</td>\n", "      <td>3.6821</td>\n", "      <td>3.4990</td>\n", "      <td>3.3187</td>\n", "      <td>4.6590</td>\n", "      <td>4.5000</td>\n", "      <td>4.307</td>\n", "      <td>4.0990</td>\n", "      <td>3.9240</td>\n", "      <td>3.7765</td>\n", "      <td>3.6830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-22</th>\n", "      <td>4.8490</td>\n", "      <td>4.8535</td>\n", "      <td>4.7915</td>\n", "      <td>4.7404</td>\n", "      <td>4.6869</td>\n", "      <td>4.6296</td>\n", "      <td>3.9515</td>\n", "      <td>3.8471</td>\n", "      <td>3.7431</td>\n", "      <td>3.6998</td>\n", "      <td>...</td>\n", "      <td>3.6777</td>\n", "      <td>3.4945</td>\n", "      <td>3.3138</td>\n", "      <td>4.6429</td>\n", "      <td>4.4875</td>\n", "      <td>4.288</td>\n", "      <td>4.0845</td>\n", "      <td>3.9200</td>\n", "      <td>3.7795</td>\n", "      <td>3.6880</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-23</th>\n", "      <td>4.8558</td>\n", "      <td>4.8590</td>\n", "      <td>4.7852</td>\n", "      <td>4.7414</td>\n", "      <td>4.6819</td>\n", "      <td>4.6288</td>\n", "      <td>3.9858</td>\n", "      <td>3.8910</td>\n", "      <td>3.7945</td>\n", "      <td>3.7509</td>\n", "      <td>...</td>\n", "      <td>3.6985</td>\n", "      <td>3.5152</td>\n", "      <td>3.3330</td>\n", "      <td>4.6445</td>\n", "      <td>4.4940</td>\n", "      <td>4.307</td>\n", "      <td>4.1100</td>\n", "      <td>3.9480</td>\n", "      <td>3.8180</td>\n", "      <td>3.7385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-24</th>\n", "      <td>4.8560</td>\n", "      <td>4.7971</td>\n", "      <td>4.7485</td>\n", "      <td>4.7106</td>\n", "      <td>4.6554</td>\n", "      <td>4.6079</td>\n", "      <td>3.9625</td>\n", "      <td>3.8686</td>\n", "      <td>3.7712</td>\n", "      <td>3.7241</td>\n", "      <td>...</td>\n", "      <td>3.6546</td>\n", "      <td>3.4707</td>\n", "      <td>3.2895</td>\n", "      <td>4.6380</td>\n", "      <td>4.4810</td>\n", "      <td>4.285</td>\n", "      <td>4.0900</td>\n", "      <td>3.9325</td>\n", "      <td>3.7990</td>\n", "      <td>3.7200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-25</th>\n", "      <td>4.8560</td>\n", "      <td>4.7886</td>\n", "      <td>4.7333</td>\n", "      <td>4.6992</td>\n", "      <td>4.6445</td>\n", "      <td>4.5939</td>\n", "      <td>3.9808</td>\n", "      <td>3.8910</td>\n", "      <td>3.7952</td>\n", "      <td>3.7490</td>\n", "      <td>...</td>\n", "      <td>3.6762</td>\n", "      <td>3.4924</td>\n", "      <td>3.3110</td>\n", "      <td>4.6203</td>\n", "      <td>4.4700</td>\n", "      <td>4.276</td>\n", "      <td>4.0960</td>\n", "      <td>3.9530</td>\n", "      <td>3.8260</td>\n", "      <td>3.7440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-10-28</th>\n", "      <td>4.8530</td>\n", "      <td>4.7745</td>\n", "      <td>4.7176</td>\n", "      <td>4.6926</td>\n", "      <td>4.6399</td>\n", "      <td>4.5933</td>\n", "      <td>4.0061</td>\n", "      <td>3.9244</td>\n", "      <td>3.8362</td>\n", "      <td>3.7901</td>\n", "      <td>...</td>\n", "      <td>3.7027</td>\n", "      <td>3.5178</td>\n", "      <td>3.3375</td>\n", "      <td>4.6245</td>\n", "      <td>4.4800</td>\n", "      <td>4.290</td>\n", "      <td>4.1060</td>\n", "      <td>3.9670</td>\n", "      <td>3.8470</td>\n", "      <td>3.7800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>20 rows × 31 columns</p>\n", "</div>"], "text/plain": ["            USOSFR1Z Curncy  USOSFR2Z Curncy  USOSFR3Z Curncy  USOSFRA Curncy  \\\n", "2024-10-01           4.8493           4.8539           4.8554          4.8576   \n", "2024-10-02           4.8507           4.8487           4.8510          4.8530   \n", "2024-10-03           4.8355           4.8370           4.8395          4.8443   \n", "2024-10-04           4.8355           4.8375           4.8400          4.8463   \n", "2024-10-07           4.8400           4.8430           4.8445          4.8263   \n", "2024-10-08           4.8420           4.8464           4.8480          4.8270   \n", "2024-10-09           4.8400           4.8432           4.8455          4.8252   \n", "2024-10-10           4.8405           4.8430           4.8460          4.8033   \n", "2024-10-11           4.8390           4.8412           4.8435          4.7849   \n", "2024-10-14           4.8390           4.8412           4.8435          4.7878   \n", "2024-10-15           4.8420           4.8455           4.8500          4.7793   \n", "2024-10-16           4.8486           4.8510           4.8510          4.7824   \n", "2024-10-17           4.8505           4.8532           4.8177          4.7672   \n", "2024-10-18           4.8490           4.8512           4.8135          4.7537   \n", "2024-10-21           4.8480           4.8595           4.8065          4.7516   \n", "2024-10-22           4.8490           4.8535           4.7915          4.7404   \n", "2024-10-23           4.8558           4.8590           4.7852          4.7414   \n", "2024-10-24           4.8560           4.7971           4.7485          4.7106   \n", "2024-10-25           4.8560           4.7886           4.7333          4.6992   \n", "2024-10-28           4.8530           4.7745           4.7176          4.6926   \n", "\n", "            USOSFRB Curncy  USOSFRC Curncy  USOSFR1F Curncy  USOSFR2 Curncy  \\\n", "2024-10-01          4.7273          4.6156           3.5433          3.3985   \n", "2024-10-02          4.7234          4.6049           3.5753          3.4388   \n", "2024-10-03          4.6994          4.5936           3.6331          3.5001   \n", "2024-10-04          4.7376          4.6488           3.8514          3.7265   \n", "2024-10-07          4.7583          4.6788           3.9200          3.7989   \n", "2024-10-08          4.7538          4.6703           3.8905          3.7670   \n", "2024-10-09          4.7559          4.6755           3.9556          3.8376   \n", "2024-10-10          4.7362          4.6555           3.8879          3.7705   \n", "2024-10-11          4.7268          4.6473           3.8781          3.7620   \n", "2024-10-14             NaN          4.6553              NaN             NaN   \n", "2024-10-15          4.7116          4.6325           3.8710          3.7486   \n", "2024-10-16          4.7163          4.6300           3.8625          3.7405   \n", "2024-10-17          4.7016          4.6401           3.8941          3.7751   \n", "2024-10-18          4.6895          4.6245           3.8707          3.7522   \n", "2024-10-21          4.7039          4.6460           3.9523          3.8422   \n", "2024-10-22          4.6869          4.6296           3.9515          3.8471   \n", "2024-10-23          4.6819          4.6288           3.9858          3.8910   \n", "2024-10-24          4.6554          4.6079           3.9625          3.8686   \n", "2024-10-25          4.6445          4.5939           3.9808          3.8910   \n", "2024-10-28          4.6399          4.5933           4.0061          3.9244   \n", "\n", "            USOSFR3 Curncy  USOSFR4 Curncy  ...  USOSFR30 Curncy  \\\n", "2024-10-01          3.2639          3.2125  ...           3.2555   \n", "2024-10-02          3.3080          3.2574  ...           3.3116   \n", "2024-10-03          3.3726          3.3240  ...           3.3565   \n", "2024-10-04          3.5905          3.5205  ...           3.4261   \n", "2024-10-07          3.6583          3.5859  ...           3.4871   \n", "2024-10-08          3.6319          3.5616  ...           3.4744   \n", "2024-10-09          3.7038          3.6325  ...           3.5234   \n", "2024-10-10          3.6438          3.5845  ...           3.5400   \n", "2024-10-11          3.6417          3.5908  ...           3.5903   \n", "2024-10-14             NaN             NaN  ...              NaN   \n", "2024-10-15          3.6178          3.5564  ...           3.5047   \n", "2024-10-16          3.6096          3.5479  ...           3.4894   \n", "2024-10-17          3.6501          3.5948  ...           3.5749   \n", "2024-10-18          3.6296          3.5781  ...           3.5790   \n", "2024-10-21          3.7292          3.6838  ...           3.6821   \n", "2024-10-22          3.7431          3.6998  ...           3.6777   \n", "2024-10-23          3.7945          3.7509  ...           3.6985   \n", "2024-10-24          3.7712          3.7241  ...           3.6546   \n", "2024-10-25          3.7952          3.7490  ...           3.6762   \n", "2024-10-28          3.8362          3.7901  ...           3.7027   \n", "\n", "            USOSFR40 Curncy  USOSFR50 Curncy  USSOSR1 Curncy  USSOSR2 Curncy  \\\n", "2024-10-01           3.0735           2.8951          4.5276          4.1960   \n", "2024-10-02           3.1308           2.9521          4.5380          4.2120   \n", "2024-10-03           3.1759           2.9970          4.5400          4.2365   \n", "2024-10-04           3.2437           3.0645          4.6190          4.3490   \n", "2024-10-07           3.3035           3.1262          4.6580          4.4085   \n", "2024-10-08           3.2890           3.1099          4.6540          4.4080   \n", "2024-10-09           3.3389           3.1580          4.6635          4.4375   \n", "2024-10-10           3.3560           3.1758          4.6550          4.4455   \n", "2024-10-11           3.4070           3.2252          4.6470          4.4460   \n", "2024-10-14              NaN              NaN          4.6550          4.4560   \n", "2024-10-15           3.3215           3.1395          4.6280          4.4375   \n", "2024-10-16           3.3055           3.1255          4.6340          4.4410   \n", "2024-10-17           3.3915           3.2092          4.6470          4.4850   \n", "2024-10-18           3.3956           3.2143          4.6350          4.4640   \n", "2024-10-21           3.4990           3.3187          4.6590          4.5000   \n", "2024-10-22           3.4945           3.3138          4.6429          4.4875   \n", "2024-10-23           3.5152           3.3330          4.6445          4.4940   \n", "2024-10-24           3.4707           3.2895          4.6380          4.4810   \n", "2024-10-25           3.4924           3.3110          4.6203          4.4700   \n", "2024-10-28           3.5178           3.3375          4.6245          4.4800   \n", "\n", "            USSOSR3 Curncy  USSOSR4 Curncy  USSOSR5 Curncy  USSOSR6 Curncy  \\\n", "2024-10-01           3.861          3.5768          3.3730          3.2250   \n", "2024-10-02           3.881          3.6076          3.4130          3.2740   \n", "2024-10-03           3.930          3.6790          3.4970          3.3660   \n", "2024-10-04           4.109          3.9099          3.7630          3.6410   \n", "2024-10-07           4.189          3.9835          3.8330          3.7150   \n", "2024-10-08           4.172          3.9560          3.8010          3.6730   \n", "2024-10-09           4.230          4.0300          3.8865          3.7610   \n", "2024-10-10           4.205          3.9800          3.8055          3.6780   \n", "2024-10-11           4.215          3.9789          3.8015          3.6670   \n", "2024-10-14           4.239          4.0010          3.8270          3.7000   \n", "2024-10-15           4.221          3.9890          3.8125          3.6800   \n", "2024-10-16           4.221          3.9890          3.8075          3.6510   \n", "2024-10-17           4.269          4.0410          3.8580          3.6980   \n", "2024-10-18           4.258          4.0160          3.8330          3.6760   \n", "2024-10-21           4.307          4.0990          3.9240          3.7765   \n", "2024-10-22           4.288          4.0845          3.9200          3.7795   \n", "2024-10-23           4.307          4.1100          3.9480          3.8180   \n", "2024-10-24           4.285          4.0900          3.9325          3.7990   \n", "2024-10-25           4.276          4.0960          3.9530          3.8260   \n", "2024-10-28           4.290          4.1060          3.9670          3.8470   \n", "\n", "            USSOSR7 Curncy  \n", "2024-10-01          3.1125  \n", "2024-10-02          3.1590  \n", "2024-10-03          3.2540  \n", "2024-10-04          3.5340  \n", "2024-10-07          3.6110  \n", "2024-10-08          3.5710  \n", "2024-10-09          3.6640  \n", "2024-10-10          3.5730  \n", "2024-10-11          3.5620  \n", "2024-10-14          3.5850  \n", "2024-10-15          3.5640  \n", "2024-10-16          3.5720  \n", "2024-10-17          3.5930  \n", "2024-10-18          3.5755  \n", "2024-10-21          3.6830  \n", "2024-10-22          3.6880  \n", "2024-10-23          3.7385  \n", "2024-10-24          3.7200  \n", "2024-10-25          3.7440  \n", "2024-10-28          3.7800  \n", "\n", "[20 rows x 31 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data[0].loc[dt.date(2024,10,1):dt.date(2024,10,29)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}