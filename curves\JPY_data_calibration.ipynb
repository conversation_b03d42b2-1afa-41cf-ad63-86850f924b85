%load_ext autoreload
%autoreload 2

import sys
import os
investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), ".."))
sys.path.append(investment_parent_path)
import pandas as pd
import datetime as dt
import QuantLib as ql
from IPython.core.interactiveshell import InteractiveShell
# Set this to ensure that all outputs are displayed without truncation
InteractiveShell.ast_node_interactivity = "all"

from loader.curve_calibrator import curveCalibrator
from loader.curve_data_loader import curveBenchmarkLoader
from helpers.plot_tools import plot_curves, plot_discount_curves
from pricer.swap import oisSwapPricer, iborSwapPricer

calibrator = curveCalibrator("USD.SOFR", data_source="JPM")

calibrator.recalibrate(dt.date(2025,1,1), dt.date(2025,1,10))

ois_pricer = oisSwapPricer("USD.SOFR", data_source="JPM")

ois_pricer.fairRate(dt.date(2022,1,1), dt.date(2025,6,10), ["1Y", "1Y1Y", "2Y", "2Y1Y", "3Y1Y"])

