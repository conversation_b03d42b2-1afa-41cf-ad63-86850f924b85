import os
import sys
import datetime as dt
import numpy as np
import pandas as pd
import xlwings as xw
import QuantLib as ql

# sys.path.append(r"C:\Users\<USER>\Documents\investment")
sys.path.append(r"n:\PMs\FIRV\FC\investment")
from loader.ir_future_option_data_loader import interestRateFutureOptionLoader
from pricer.ir_future_option_pricer import interestRateFutureOptionPricer
from loader.ir_future_option_data_scenario_analyzer import (
    interestRateFutureOptionScenarioAnalyzer,
)
from backtest.option_pair_calendar import OptionCalendarBacktester
from helpers.date_helpers import is_dayfirst_format


@xw.func
@xw.arg("n", numbers=int)
def generate_range(n: int):
    return [[i] for i in range(1, n + 1)]


@xw.func
@xw.arg("pricing_date", dates=dt.date)
@xw.arg("option_tickers", ndim=1)
@xw.arg("fut_tickers", ndim=1)
@xw.arg("fut_px_bump", ndim=1, numbers=float)
@xw.arg("bd_bump", ndim=1, numbers=int, empty=0)
def calculate_rolldown_vol(
    pricing_date: dt.date,
    option_tickers: list[str],
    fut_tickers: list[str],
    fut_px_bump: list[float],
    bd_bump: list[int] = [0],
    use_live: bool = True,
    moneyness_type: str = "log-normal",
    debug_mode: bool = False,
):
    assert any(fut_px_bump), "Future price bump cannot be all None!"
    all_positions = pd.DataFrame({"option_tickers": option_tickers})
    all_positions["ir_option_name"] = all_positions["option_tickers"].str.extract(
        r"(\w+)[a-zA-Z]\d{1,2}[cpCP]\s+.*"
    )
    all_positions["ir_option_name"] = all_positions["ir_option_name"].str.upper()
    all_positions["ir_option_name"] = all_positions["ir_option_name"].map(
        lambda name: (
            interestRateFutureOptionScenarioAnalyzer.WEEKLIES_TO_PRIMARY_MAPPER.get(
                name[1], name
            )
            if isinstance(name, str) and "1" <= name[0] <= "5"
            else name
        )
    )
    num_scenarios = len(fut_px_bump) * len(bd_bump)
    if fut_tickers:
        num_scenarios = len(bd_bump)
    vol_result = np.full((num_scenarios, len(option_tickers)), np.nan)
    for ir_option_name, ir_option_group in all_positions.groupby("ir_option_name"):
        # dropna by default
        loader = interestRateFutureOptionScenarioAnalyzer(
            ir_option_name, debug_mode=debug_mode
        )
        output = loader.load_and_calculate_rolldown_vol(
            pricing_date,
            ir_option_group["option_tickers"].tolist(),
            fut_px_bump,
            bd_bump,
            use_live,
            moneyness_type,
            fut_tickers,
        )
        if debug_mode:
            output = output["output"]
        vol_result[:, ir_option_group.index] = output["vol"].values.reshape(
            num_scenarios, -1
        )

    if loader.is_bond_future:
        return vol_result.T
    return 100 * vol_result.T


@xw.func
@xw.arg("start_date", dates=dt.date)
@xw.arg("end_date", dates=dt.date)
def get_business_days_between(ccy: str, start_date: dt.date, end_date: dt.date):
    calendar = interestRateFutureOptionScenarioAnalyzer.get_calendar(ccy)
    if calendar is not None:
        return calendar.businessDaysBetween(
            ql.Date.from_date(
                pd.to_datetime(start_date, dayfirst=is_dayfirst_format())
            ),
            ql.Date.from_date(pd.to_datetime(end_date, dayfirst=is_dayfirst_format())),
        )
    return None


@xw.func
@xw.arg("option_ticker")
@xw.arg("pricing_date", dates=dt.date)
def get_udl_future_ticker(option_ticker: str, pricing_date: dt.date):
    pricing_date = ql.Date.from_date(pricing_date)
    if option_ticker:
        option_ticker = option_ticker.upper().lstrip().rstrip()
        option_name = option_ticker[:-2]
        if "1" <= option_ticker[0] <= "5":
            option_name = (
                interestRateFutureOptionScenarioAnalyzer.WEEKLIES_TO_PRIMARY_MAPPER.get(
                    option_ticker[1], option_name
                )
            )
        return interestRateFutureOptionLoader.get_underlying_futures_tickers(
            [option_ticker],
            option_name,
            pricing_date,
        )
    return None


@xw.sub
def vol_plot():
    import webbrowser
    import plotly.io as pio

    wb = xw.Book.caller()
    sht = wb.sheets["Plots"]
    ir_option_names = sht.range("B21:B24").value
    forward_starts = sht.range("G21:G24").value
    forward_starts = [0 if fs is None else fs for fs in forward_starts]
    maturities = sht.range("C21:C24").value
    option_types = sht.range("D21:D24").value
    start_dates = sht.range("E21:E24").value
    end_dates = sht.range("F21:F24").value
    output = sht.range("C28").value
    spread_ratio_to_plot = sht.range("C29:C34").value
    use_live = sht.range("C35").value
    spread_ratio_to_plot = [s for s in spread_ratio_to_plot if s is not None]
    static_data = pd.DataFrame(
        {
            "ir_option_name": ir_option_names,
            "forward_start": forward_starts,
            "maturity": maturities,
            "option_type": option_types,
            "start_date": start_dates,
            "end_date": end_dates,
        }
    ).dropna(axis=0)
    static_data["ir_option_name"] = static_data["ir_option_name"].str.upper()
    static_data["forward_start"] = static_data["forward_start"].astype(int)
    static_data["maturity"] = static_data["maturity"].astype(int)
    static_data["start_date"] = static_data["start_date"].dt.date
    static_data["end_date"] = static_data["end_date"].dt.date
    static_data["option_type"] = (
        static_data["option_type"].str.upper().replace(" ", "_")
    )
    static_data["column_name"] = static_data.apply(
        lambda row: f"{row['ir_option_name']}_"
        + (f"{row["forward_start"]}-" if row["forward_start"] != 0 else "")
        + f"{row['maturity']}_{row['option_type']}",
        axis=1,
    )
    print(static_data)

    vol_results = []
    fixed_strike_data = pd.DataFrame()  # For example, 0Q strikes can be used for 2Q
    for ir_option_name, ir_option_group in static_data.groupby("ir_option_name"):
        pricer = interestRateFutureOptionPricer(ir_option_name)
        maturity_lst = sorted(
            set(ir_option_group["forward_start"] + ir_option_group["maturity"]).union(
                ir_option_group[ir_option_group["forward_start"] != 0]["forward_start"]
            )
        )
        option_types_lst = None
        if use_live:
            option_types_lst = [None] * len(maturity_lst)
            for i, maturity in enumerate(maturity_lst):
                option_types_lst[i] = set(
                    ir_option_group[
                        ir_option_group["forward_start"] + ir_option_group["maturity"]
                        == maturity
                    ]["option_type"]
                ).union(
                    ir_option_group[ir_option_group["forward_start"] == maturity][
                        "option_type"
                    ]
                )
        start_date = min(ir_option_group["start_date"])
        end_date = max(ir_option_group["end_date"])
        option_px_data = pricer.loader.load_option_price(
            start_date,
            end_date,
            maturity=maturity_lst,
            option_types=option_types_lst,
            use_live=use_live,
        )
        if (
            fixed_strike_data.empty
            and "FIXED_STRIKE" in static_data["option_type"].values
        ):
            try:
                fixed_strike_row = ir_option_group[
                    ir_option_group["option_type"] != "FIXED_STRIKE"
                ].iloc[0]
                print(
                    f"Calculating fixed strikes for {ir_option_name} with maturity {fixed_strike_row["maturity"]}..."
                )
                idx = maturity_lst.index(fixed_strike_row["maturity"])
                fixed_strike_data = pricer.calculate_vol(
                    start_date,
                    end_date,
                    fixed_strike_row["maturity"],
                    fixed_strike_row["option_type"],
                    output="Strike",
                    option_px_data=option_px_data[idx],
                )
            except Exception as e:
                print(
                    f"Failed to calculate fixed strikes for {ir_option_name} with maturity {fixed_strike_row["maturity"]}:\n {e}"
                )
        for (forward_start, maturity), maturity_group in ir_option_group.groupby(
            ["forward_start", "maturity"]
        ):
            try:
                if forward_start == 0:
                    idx = maturity_lst.index(maturity)
                    vol = pricer.calculate_vol(
                        start_date,
                        end_date,
                        maturity,
                        maturity_group["option_type"].unique().tolist(),
                        output=output,
                        option_px_data=option_px_data[idx],
                        fixed_strike_data=fixed_strike_data,
                    )
                else:
                    idx1, idx2 = maturity_lst.index(forward_start), maturity_lst.index(
                        forward_start + maturity
                    )
                    vol = pricer.calculate_forward_vol(
                        start_date,
                        end_date,
                        forward_start,
                        maturity,
                        maturity_group["option_type"].unique().tolist(),
                        option_px_data[idx1],
                        option_px_data[idx2],
                        fixed_strike_data=fixed_strike_data,
                    )
                vol_results.append(vol)
            except Exception as e:
                print(
                    f"Failed to calculate vol for {ir_option_name} with maturity {maturity}:\n {e}"
                )
    vol_results = pd.concat(vol_results, axis=1)
    pd.options.plotting.backend = "plotly"
    vol_fig = vol_results.plot(title="Vol Plot", template="plotly_white")
    cache_dir = os.path.join(os.path.dirname(__file__), "..", "vol_plot_cache")
    username = os.environ.get("USERNAME", "user")
    html_path = os.path.join(
        cache_dir,
        f"vplot_{username}_{dt.datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
    )
    html_parts = []
    html_parts.append("<html><head><meta charset='utf-8'></head><body>")
    html_parts.append(f"<h1>Vol Plot: {output}</h1>")
    html_parts.append(pio.to_html(vol_fig, full_html=False, include_plotlyjs="cdn"))
    if len(spread_ratio_to_plot) > 0:
        spread_results, ratio_results = [], []
        for spread_ratio in spread_ratio_to_plot:
            try:
                spread_ratio_split = spread_ratio.split("-")
                if len(spread_ratio_split) == 2:
                    idx1, idx2 = int(spread_ratio_split[0]), int(spread_ratio_split[1])
                    column1, column2 = (
                        static_data.iloc[idx1 - 1]["column_name"],
                        static_data.iloc[idx2 - 1]["column_name"],
                    )
                    spread = vol_results[column1] - vol_results[column2]
                    ratio = vol_results[column1] / vol_results[column2]
                    spread_results.append(spread.rename(spread_ratio))
                    ratio_results.append(ratio.rename(spread_ratio))
                elif len(spread_ratio_split) == 1:
                    idx = int(spread_ratio_split[0])
                    column = static_data.iloc[idx - 1]["column_name"]
                    spread_results.append(vol_results[column].rename(spread_ratio))
                    ratio_results.append(vol_results[column].rename(spread_ratio))
            except Exception as e:
                print(f"Failed to calculate spread/ratio for {spread_ratio}:\n {e}")
        if len(spread_results) > 0 and len(ratio_results) > 0:
            spread_fig = pd.concat(spread_results, axis=1).plot(
                title="Spread Plot", template="plotly_white"
            )
            ratio_fig = pd.concat(ratio_results, axis=1).plot(
                title="Ratio Plot", template="plotly_white"
            )
            html_parts.append("<h1>Spread Plot</h1>")
            html_parts.append(
                pio.to_html(spread_fig, full_html=False, include_plotlyjs=False)
            )
            html_parts.append("<h1>Ratio Plot</h1>")
            html_parts.append(
                pio.to_html(ratio_fig, full_html=False, include_plotlyjs=False)
            )
    elif len(vol_results.columns) == 2:
        spread = vol_results.iloc[:, 0] - vol_results.iloc[:, 1]
        ratio = vol_results.iloc[:, 0] / vol_results.iloc[:, 1]
        spread_fig = spread.plot(title="Spread Plot", template="plotly_white")
        ratio_fig = ratio.plot(title="Ratio Plot", template="plotly_white")
        html_parts.append("<h1>Spread Plot</h1>")
        html_parts.append(
            pio.to_html(spread_fig, full_html=False, include_plotlyjs=False)
        )
        html_parts.append("<h1>Ratio Plot</h1>")
        html_parts.append(
            pio.to_html(ratio_fig, full_html=False, include_plotlyjs=False)
        )
    html_parts.append("</body></html>")
    with open(html_path, "w", encoding="utf-8") as f:
        f.write("\n".join(html_parts))
    webbrowser.open_new_tab("file://" + html_path)


@xw.sub
def option_calendar_pair_backtest(output: str = ""):
    wb = xw.Book.caller()
    sht = wb.sheets["Backtest"]
    static_data = sht.range("B2:B10").value
    (
        ir_option_name,
        short_period,
        long_period,
        option_type,
        start_date,
        end_date,
        value_1pt,
        qty,
        initial_cash,
    ) = static_data
    long_vol_ratio_entry = sht.range("F3:F5").value
    long_premium_ratio_entry = sht.range("F7:F9").value
    long_vol_ratio_exit = sht.range("M3:M5").value
    long_premium_ratio_exit = sht.range("M7:M9").value
    backtester = OptionCalendarBacktester(
        ir_option_name=ir_option_name,
        start_date=start_date.date(),
        end_date=end_date.date(),
        short_period=int(short_period),
        long_period=int(long_period),
        option_type=option_type,
        long_premium_ratio_entry=long_premium_ratio_entry[0],
        long_premium_ratio_entry_pct=long_premium_ratio_entry[1],
        long_premium_ratio_entry_roll_window=long_premium_ratio_entry[2],
        long_vol_ratio_entry=long_vol_ratio_entry[0],
        long_vol_ratio_entry_pct=long_vol_ratio_entry[1],
        long_vol_ratio_entry_roll_window=long_vol_ratio_entry[2],
        long_premium_ratio_exit=long_premium_ratio_exit[0],
        long_premium_ratio_exit_pct=long_premium_ratio_exit[1],
        long_vol_ratio_exit=long_vol_ratio_exit[0],
        long_vol_ratio_exit_pct=long_vol_ratio_exit[1],
        exit_days=sht.range("M10").value,
        position_size=qty * value_1pt,
        initial_cash=initial_cash,
    )
    if output == "signal":
        backtester.prepare_signal(calculate_all=True)
        backtester.generate_sigmal_report()
    else:
        backtester.prepare_signal()
        backtester.generate_report()
