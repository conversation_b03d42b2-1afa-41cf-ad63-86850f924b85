import datetime as dt
import numpy as np
import pandas as pd
import QuantLib as ql
from curves.config import available_curves
from loader.curve_calibrator import curveCalibrator
from helpers.date_helpers import strp_tenor
from typing import List


class swapPricer:
    def __init__(self, name: str, **params) -> None:
        self.name = name.replace("_", ".")
        if name not in available_curves:
            raise ValueError(
                f"Curve name {name} is not valid. Available curves: {list(available_curves.keys())}"
            )
        self.curve_name = self.name.replace(".", "_")
        self.curve = available_curves[name]("19010101", **params)
        self.debug_mode = params.get("debug_mode", False)
        self.data_source = params.get("data_source", None)
        assert (
            self.data_source is not None
        ), "Data source (e.g. BBG, JPM) is not specified!"

    def _print(self, *msg):
        if self.debug_mode:
            print(*msg)

    def _make_swap(self, swapTenor, index, fixedRate, forwardStart, swap_config):
        raise NotImplementedError

    def _reconstruct_curve(self, curve_data):
        flat_pillar_size = int(curve_data.get("flat_pillar_size", 0))
        dates = [ql.Date.from_date(date) for date in curve_data["pillar_dates"]]
        if self.data_source != "BBG" and not flat_pillar_size:
            curve = ql.MonotonicLogCubicDiscountCurve(
                dates,
                curve_data["discount_factors"],
                self.curve.index.dayCounter(),
                self.curve.fixed_calendar,
            )
        else:
            curve = ql.LogMixedLinearCubicDiscountCurve(
                dates,
                curve_data["discount_factors"],
                self.curve.index.dayCounter(),
                self.curve.fixed_calendar,
                ql.LogMixedLinearCubic(
                    n=flat_pillar_size, behavior=ql.MixedInterpolation.SplitRanges
                ),
            )
        return curve

    @staticmethod
    def _calculate_npv_on_the_fly(
        bbg_bar_data,
        curve_name: str,
        swap: ql.Swap,
        index_yts: ql.RelinkableYieldTermStructureHandle,
        discounting_yts: ql.RelinkableYieldTermStructureHandle | None = None,
    ):
        date = bbg_bar_data.name
        curve = available_curves[curve_name](date.isoformat())
        benchmarks_meta_data = curve.loader.get_benchmarks(date)["benchmarks"]
        if bbg_bar_data[benchmarks_meta_data["ticker"]].isna().any():
            return np.nan
        curve.calibrate(benchmarks_meta_data, bbg_bar_data)
        index_yts.linkTo(curve.curve)
        if discounting_yts is not None:
            discounting_yts.linkTo(curve.ois_curve.curve)
        try:
            return swap.NPV()
        except:
            return np.nan

    @staticmethod
    def _calculate_dv01_on_the_fly(
        bbg_bar_data,
        curve_name: str,
        swap: ql.Swap,
        index_yts: ql.RelinkableYieldTermStructureHandle,
        discounting_yts: ql.RelinkableYieldTermStructureHandle | None = None,
    ):
        date = bbg_bar_data.name
        curve = available_curves[curve_name](date.isoformat())
        benchmarks_meta_data = curve.loader.get_benchmarks(date)["benchmarks"]
        if bbg_bar_data[benchmarks_meta_data["ticker"]].isna().any():
            return np.nan
        curve.calibrate(benchmarks_meta_data, bbg_bar_data)
        index_yts.linkTo(curve.curve)
        if discounting_yts is not None:
            discounting_yts.linkTo(curve.ois_curve.curve)
        npv = swap.NPV()
        bumped_curve = ql.ZeroSpreadedTermStructure(
            ql.YieldTermStructureHandle(curve.curve),
            ql.QuoteHandle(ql.SimpleQuote(0.0001)),
        )
        index_yts.linkTo(bumped_curve)
        if discounting_yts is not None:
            bumped_discounting_curve = ql.ZeroSpreadedTermStructure(
                ql.YieldTermStructureHandle(curve.ois_curve.curve),
                ql.QuoteHandle(ql.SimpleQuote(0.0001)),
            )
            discounting_yts.linkTo(bumped_discounting_curve)
        return swap.NPV() - npv

    @staticmethod
    def _calculate_fair_rate_on_the_fly(
        bbg_bar_data,
        curve_name: str,
        swap: ql.Swap,
        index_yts: ql.YieldTermStructure,
        discounting_yts: ql.YieldTermStructure | None = None,
    ):
        date = bbg_bar_data.name
        curve = available_curves[curve_name](date.isoformat())
        benchmarks_meta_data = curve.loader.get_benchmarks(date)["benchmarks"]
        if bbg_bar_data[benchmarks_meta_data["ticker"]].isna().any():
            return np.nan
        curve.calibrate(benchmarks_meta_data, bbg_bar_data)
        index_yts.linkTo(curve.curve)
        if discounting_yts is not None:
            discounting_yts.linkTo(curve.ois_curve.curve)
        return swap.fairRate()

    def fairRate(
        self, start_date: dt.date, end_date: dt.date, tenors: str | List[str]
    ) -> pd.DataFrame:
        curve_calibrator = curveCalibrator(self.curve_name, self.data_source)
        period_calibration_results = curve_calibrator.load(start_date, end_date)
        if not isinstance(tenors, list):
            tenors = [tenors]
        start_tenors, end_tenors, _ = strp_tenor(tenors)
        period_swap_rates = []
        for calibration_results in period_calibration_results:
            swap_rates = calibration_results.apply(
                lambda calibration_data: self._calculate_fair_rate_from_calibration(
                    calibration_data, start_tenors, end_tenors
                ),
                axis=1,
                result_type="expand",
            )
            swap_rates.columns = [self.curve_name + " " + tenor for tenor in tenors]
            period_swap_rates.append(swap_rates)
        return pd.concat(period_swap_rates)

    def _calculate_fair_rate_from_calibration(
        self,
        curve_data: pd.Series,
        start_tenors: List[str] | None,
        end_tenors: List[str] | None,
        ois_curve_data: pd.Series | None = None,
    ):
        swap_rates = []
        curve = self._reconstruct_curve(curve_data)
        index_yts = ql.YieldTermStructureHandle(curve)
        index = self.curve.index.clone(index_yts)
        if ois_curve_data is not None:
            ois_curve = self._reconstruct_curve(ois_curve_data)
            discounting_yts = ql.YieldTermStructureHandle(ois_curve)
            discounting_engine = ql.DiscountingSwapEngine(discounting_yts)
        else:
            discounting_engine = ql.DiscountingSwapEngine(index_yts)
        swap_config = self.swap_config.copy()
        swap_config["pricingEngine"] = discounting_engine
        ql.Settings.instance().evaluationDate = ql.Date.from_date(curve_data.name)
        for start_tenor, end_tenor in zip(start_tenors, end_tenors):
            fwd_start = ql.Period()
            if start_tenor:
                fwd_start = ql.Period(start_tenor)
            swap = self._make_swap(
                ql.Period(end_tenor), index, 0, fwd_start, swap_config
            )
            swap_rates.append(swap.fairRate())
        return swap_rates


class iborSwapPricer(swapPricer):
    def __init__(self, name: str, **params) -> None:
        super().__init__(name, **params)
        self.swap_config = {
            "fixedLegCalendar": self.curve.fixed_calendar,  # NB: by default both legs uses index's calendar
            "settlementDays": self.curve.swap_settlement_days,
            "paymentConvention": self.curve.swap_config.get(
                "paymentConvention", ql.ModifiedFollowing
            ),  # NB: same as above
            "fixedLegConvention": self.curve.fixed_convention,
            "fixedLegDayCount": self.curve.fixed_day_counter,  # NB: overnightLeg uses index's day counter
        }

    def _make_swap(self, swapTenor, index, fixedRate, forwardStart, swap_config):
        return ql.MakeVanillaSwap(
            swapTenor,
            index,
            fixedRate,
            forwardStart,
            **swap_config,
        )

    def NPV(
        self,
        pricingDates: list[dt.date] = [],
        fixedRate: float | None = None,
        receiveFixed: bool = False,
        swapTenor: str = "",
        pricingStartDate: dt.date | None = None,
        pricingEndDate: dt.date | None = None,
        tradeDate: dt.date | None = None,
        settlementDate: dt.date | None = None,
        maturityDate: dt.date | None = None,
        nominal: float | None = None,
        dv01: float | None = None,
    ):
        """
        Parameters
        ----------
        pricingDates: List of pricing dates, this overrides the pricing start date and end date
        pricingStartDate : Start of pricing date
        pricingEndDate : End of pricing date, default = pricing start date
        fixedRate : Swap fixed rate
        receiveFixed: Receiver or payer, default to payer swap, i.e. default = False
        swapTenor : Swap tenor, e.g. ['3M', '1Y', '2Y', '1Yx1Y', '1Y2Y']
        tradeDate: Swap trade date, default = pricing start date
        effectiveDate: Swap settlement date, this overrides trade date and forward start date of the swap. NB: settlement date is usually T+2 of trade date
        maturityDate: Swap maturity date, this overrides maturity date of the swap
        """
        if not pricingStartDate and not pricingDates:
            raise ValueError("Please provide swap pricing dates")
        if not (swapTenor or maturityDate):
            raise ValueError("Please provide the swap tenor or maturity date!")
        if len(pricingDates) > 0:
            pricingStartDate, pricingEndDate = pricingDates[0], pricingDates[-1]
        if not tradeDate:
            tradeDate = pricingStartDate
        if not pricingEndDate:
            pricingEndDate = pricingStartDate

        forwardStart, swapTenor, _ = (t[0] for t in strp_tenor([swapTenor]))
        forwardStart = ql.Period(forwardStart) if forwardStart else ql.Period()
        swapTenor = ql.Period(swapTenor) if swapTenor else ql.Period()
        if not nominal:
            nominal = 1e6
        index_yts = ql.RelinkableYieldTermStructureHandle()
        index = self.curve.index.clone(index_yts)
        discounting_yts = ql.RelinkableYieldTermStructureHandle()
        discounting_engine = ql.DiscountingSwapEngine(discounting_yts)
        swap_config = self.swap_config.copy()
        swap_config.update(
            {
                "receiveFixed": receiveFixed,
                "nominal": nominal,
                "pricingEngine": discounting_engine,
            }
        )
        if settlementDate:
            swap_config["effectiveDate"] = ql.Date.from_date(settlementDate)
        if maturityDate:
            swap_config["terminationDate"] = ql.Date.from_date(maturityDate)

        "**************** Calculate proxy swap and Load data ****************"
        ql.Settings.instance().evaluationDate = ql.Date.from_date(tradeDate)
        proxy_swap = ql.MakeVanillaSwap(
            swapTenor,
            index,
            0,
            forwardStart,
            **swap_config,
        )

        fixing_dates = [
            cf.fixingDate().to_date()
            for cf in map(ql.as_floating_rate_coupon, proxy_swap.floatingLeg())
        ]
        fixing_start_date = fixing_dates[0]

        _, bbg_bar_data = self.curve.loader.load_close_px_for_period(
            min(fixing_start_date, tradeDate, pricingStartDate),
            pricingEndDate,
            check_missing_tickers=True,
        )
        bbg_bar_data = pd.concat(bbg_bar_data, axis=0)

        "**************** Calculate market rate and Create swap ****************"
        if not fixedRate:
            fixedRate = swapPricer._calculate_fair_rate_on_the_fly(
                bbg_bar_data.loc[tradeDate],
                self.name,
                proxy_swap,
                index_yts,
                discounting_yts,
            )  # Market fair rate on the trade date

        swap = ql.MakeVanillaSwap(
            swapTenor,
            index,
            fixedRate,
            forwardStart,
            **swap_config,
        )

        print(
            "Swap start date:",
            swap.startDate().ISO(),
            "Swap maturity date:",
            swap.maturityDate().ISO(),
        )

        "**************** Add Fixing ****************"
        if fixing_start_date < pricingEndDate:
            benchmarks_meta_data = self.curve.loader.get_benchmarks(pricingStartDate)[
                "benchmarks"
            ]
            fixing_ticker = benchmarks_meta_data[
                benchmarks_meta_data["instrument_type"] == "Deposit"
            ]["ticker"].values[0]
            self._print(
                "Populate fixings data:",
                fixing_ticker,
                fixing_dates,
                "<",
                pricingEndDate,
            )
            fixing_data = bbg_bar_data.loc[
                [date for date in fixing_dates if date < pricingEndDate], fixing_ticker
            ]
            index.addFixings(
                fixing_data.index.map(ql.Date.from_date),
                fixing_data.values / 100,
            )

        "**************** Pricing ****************"
        multiply_factor = 1
        if dv01:
            bps_dv01 = swapPricer._calculate_dv01_on_the_fly(
                bbg_bar_data.loc[tradeDate],
                self.name,
                swap,
                index_yts,
                discounting_yts,
            )
            multiply_factor = dv01 / bps_dv01
        print("Swap Notional:", "{:,}".format(swap.nominal() * multiply_factor))

        bbg_bar_data = bbg_bar_data.loc[pricingStartDate:pricingEndDate]
        if len(pricingDates) > 0:
            bbg_bar_data = bbg_bar_data.loc[pricingDates]

        npv = bbg_bar_data.apply(
            swapPricer._calculate_npv_on_the_fly,
            curve_name=self.name,
            swap=swap,
            index_yts=index_yts,
            discounting_yts=discounting_yts,
            axis=1,
        )

        return npv * multiply_factor

    def fairRate(
        self, start_date: dt.date, end_date: dt.date, tenors: str | List[str]
    ) -> pd.DataFrame:
        curve_calibrator = curveCalibrator(self.curve_name, self.data_source)
        period_calibration_results = curve_calibrator.load(start_date, end_date)
        ois_curve_calibrator = curveCalibrator(
            self.curve.ois_curve.curve_name, self.data_source
        )
        period_ois_calibration_results = ois_curve_calibrator.load(start_date, end_date)
        if not isinstance(tenors, list):
            tenors = [tenors]
        start_tenors, end_tenors, _ = strp_tenor(tenors)
        period_swap_rates = []
        for calibration_results, ois_calibration_results in zip(
            period_calibration_results, period_ois_calibration_results
        ):
            common_index = calibration_results.index.intersection(
                ois_calibration_results.index
            )
            swap_rates = [
                self._calculate_fair_rate_from_calibration(
                    calibration_results.loc[idx],
                    start_tenors,
                    end_tenors,
                    ois_calibration_results.loc[idx],
                )
                for idx in common_index
            ]
            swap_rates = pd.DataFrame(swap_rates, index=calibration_results.index)
            swap_rates.columns = [self.curve_name + " " + tenor for tenor in tenors]
            period_swap_rates.append(swap_rates)
        return pd.concat(period_swap_rates)


class oisSwapPricer(swapPricer):
    def __init__(self, name: str, **params) -> None:
        super().__init__(name, **params)
        self.swap_config = {
            "fixedLegCalendar": self.curve.fixed_calendar,  # NB: by default both legs uses index's calendar
            "settlementDays": self.curve.swap_settlement_days,
            "paymentLag": self.curve.swap_payment_lag,
            "paymentFrequency": self.curve.swap_config.get(
                "paymentFrequency", ql.Annual
            ),  # NB: paymentFrequency sets both legs (fixedLeg and overnightLeg)
            "fixedLegPaymentFrequency": self.curve.swap_config.get(
                "fixedPaymentFrequency", ql.Annual
            ),
            "convention": self.curve.swap_config.get(
                "paymentConvention", ql.ModifiedFollowing
            ),  # NB: same as above
            "fixedLegConvention": self.curve.fixed_convention,
            "fixedLegDayCount": self.curve.fixed_day_counter,  # NB: overnightLeg uses index's day counter
        }

    def _make_swap(self, swapTenor, index, fixedRate, forwardStart, swap_config):
        return ql.MakeOIS(
            swapTenor,
            index,
            fixedRate,
            forwardStart,
            **swap_config,
        )

    def NPV(
        self,
        pricingDates: list[dt.date] = [],
        fixedRate: float | None = None,
        receiveFixed: bool = False,
        swapTenor: str = "",
        pricingStartDate: dt.date | None = None,
        pricingEndDate: dt.date | None = None,
        tradeDate: dt.date | None = None,
        settlementDate: dt.date | None = None,
        maturityDate: dt.date | None = None,
        nominal: float | None = None,
        dv01: float | None = None,
    ):
        """
        Parameters
        ----------
        pricingDates: List of pricing dates, this overrides the pricing start date and end date
        pricingStartDate : Start of pricing date
        pricingEndDate : End of pricing date, default = pricing start date
        fixedRate : Swap fixed rate
        receiveFixed: Receiver or payer, default to payer swap, i.e. default = False
        swapTenor : Swap tenor, e.g. ['3M', '1Y', '2Y', '1Yx1Y', '1Y2Y']
        tradeDate: Swap trade date, default = pricing start date
        effectiveDate: Swap settlement date, this overrides trade date and forward start date of the swap. NB: settlement date is usually T+2 of trade date
        maturityDate: Swap maturity date, this overrides maturity date of the swap
        """
        if not pricingStartDate and not pricingDates:
            raise ValueError("Please provide swap pricing dates")
        if not (swapTenor or maturityDate):
            raise ValueError("Please provide the swap tenor or maturity date!")
        if len(pricingDates) > 0:
            pricingStartDate, pricingEndDate = pricingDates[0], pricingDates[-1]
        if not tradeDate:
            tradeDate = pricingStartDate
        if not pricingEndDate:
            pricingEndDate = pricingStartDate

        forwardStart, swapTenor, _ = (t[0] for t in strp_tenor([swapTenor]))
        forwardStart = ql.Period(forwardStart) if forwardStart else ql.Period()
        swapTenor = ql.Period(swapTenor) if swapTenor else ql.Period()
        if not nominal:
            nominal = 1e6
        index_yts = ql.RelinkableYieldTermStructureHandle()
        index = self.curve.index.clone(index_yts)
        discounting_engine = ql.DiscountingSwapEngine(index_yts)
        swap_config = self.swap_config.copy()
        swap_config.update(
            {
                "receiveFixed": receiveFixed,
                "nominal": nominal,
                "pricingEngine": discounting_engine,
            }
        )
        if settlementDate:
            swap_config["effectiveDate"] = ql.Date.from_date(settlementDate)
        if maturityDate:
            swap_config["terminationDate"] = ql.Date.from_date(maturityDate)

        "**************** Calculate proxy swap and Load data ****************"
        ql.Settings.instance().evaluationDate = ql.Date.from_date(tradeDate)
        proxy_swap = ql.MakeOIS(
            swapTenor,
            index,
            0,
            forwardStart,
            **swap_config,
        )

        # E.g., SOFR swap with accrual start 2024-12-06 uses fixing from 2024-12-06,
        # 2024-12-06 SOFR fixing is published on 2024-12-09 (next Monday)
        fixing_start_date = (
            ql.as_floating_rate_coupon(proxy_swap.floatingLeg()[0])
            .accrualStartDate()
            .to_date()
        )

        _, bbg_bar_data = self.curve.loader.load_close_px_for_period(
            min(fixing_start_date, tradeDate, pricingStartDate),
            pricingEndDate,
            check_missing_tickers=True,
        )
        bbg_bar_data = pd.concat(bbg_bar_data, axis=0)

        "**************** Calculate market rate and Create swap ****************"
        if not fixedRate:
            fixedRate = swapPricer._calculate_fair_rate_on_the_fly(
                bbg_bar_data.loc[tradeDate], self.name, proxy_swap, index_yts
            )  # Market fair rate on the trade date

        swap = ql.MakeOIS(
            swapTenor,
            index,
            fixedRate,
            forwardStart,
            **swap_config,
        )

        "**************** Add Fixing ****************"
        if fixing_start_date < pricingEndDate:
            benchmarks_meta_data = self.curve.loader.get_benchmarks(pricingStartDate)[
                "benchmarks"
            ]
            fixing_ticker = benchmarks_meta_data[
                benchmarks_meta_data["instrument_type"] == "Deposit"
            ]["ticker"].values[0]
            self._print(
                "Populate fixings data:",
                fixing_ticker,
                fixing_start_date,
                "<",
                pricingEndDate,
            )
            fixing_data = bbg_bar_data.loc[
                fixing_start_date : pricingEndDate - dt.timedelta(days=1), fixing_ticker
            ]
            index.addFixings(
                fixing_data.index.map(ql.Date.from_date),
                fixing_data.values / 100,
            )

        "**************** Pricing ****************"
        multiply_factor = 1
        if dv01:
            bps_dv01 = swapPricer._calculate_dv01_on_the_fly(
                bbg_bar_data.loc[tradeDate],
                self.name,
                swap,
                index_yts,
            )
            multiply_factor = dv01 / bps_dv01
        print("Swap Notional:", "{:,}".format(swap.nominal() * multiply_factor))

        bbg_bar_data = bbg_bar_data.loc[pricingStartDate:pricingEndDate]
        if len(pricingDates) > 0:
            bbg_bar_data = bbg_bar_data.loc[pricingDates]

        npv = bbg_bar_data.apply(
            swapPricer._calculate_npv_on_the_fly,
            curve_name=self.name,
            swap=swap,
            index_yts=index_yts,
            axis=1,
        )

        return npv * multiply_factor
