import os
import sys
import QuantLib as ql
import datetime as dt
import numpy as np
import pandas as pd
import glob
import re
import time
import blpapi
from xbbg import blp
from loader.config import load_yaml_config
from functools import lru_cache


def is_bloomberg_connected():
    sess_opts = blpapi.SessionOptions()
    sess_opts.setNumStartAttempts(numStartAttempts=3)
    sess_opts.setAutoRestartOnDisconnection(autoRestart=True)
    # user = blpapi.AuthUser.createWithLogonName()
    # auth = blpapi.AuthOptions.createWithUser(user=user)
    # sess_opts.setSessionIdentityOptions(authOptions=auth)
    session = blpapi.Session(sess_opts)
    if session.start():
        session.stop()
        return True
    return False


def bloomberg_session(key: str, port: int):
    sess_opts = blpapi.SessionOptions()
    sess_opts.setApplicationIdentityKey(key)
    sess_opts.setAuthenticationOptions("APIKEY=" + key)
    sess_opts.setServerHost("localhost")
    sess_opts.setServerPort(port)
    session = blpapi.Session(sess_opts)
    return session


class interestRateFutureOptionLoader:
    """
    Loader to load the entire opion chain on any dates.

    Since the next closest option expires on the Friday before the IMM date,
    the option chain is maintained monthly, from the Monday of the IMM week to the option expiry date.
    """

    OPTION_DATA_FOLDER = "ir_future_option_data"
    _API_KEY_ = "14496a7a-482b-4542-8a94-f9e9f423513b"
    _PORT_ = 8194

    def __init__(self, name: str, **params):
        self.ir_option_name = name.upper()
        self.is_mid_curve = name[0].isnumeric()
        self.is_bond_future = interestRateFutureOptionLoader.check_is_bond_future(
            self.ir_option_name
        )
        self.udl_name = interestRateFutureOptionLoader.get_underlying_futures_root(
            self.ir_option_name
        )
        self.currency = interestRateFutureOptionLoader.get_ccy(self.ir_option_name)
        self.calendar = interestRateFutureOptionLoader.get_calendar(self.currency)
        self.ois_root = interestRateFutureOptionLoader.get_ois_root(self.currency)
        self.day_counter_denominator = interestRateFutureOptionLoader.get_day_counter(
            self.currency
        )
        self.debug_mode = params.get("debug_mode", False)
        self.ticker_formatter = np.vectorize(
            lambda imm_code, strike, option_type: f"{self.ir_option_name}{imm_code}{option_type} {np.format_float_positional(strike, precision=5, trim='-', fractional=True)} Comdty"
        )
        self.bbg_session = None

    def _print(self, *msg, **kwargs):
        if self.debug_mode:
            print(*msg, **kwargs)

    @lru_cache(maxsize=1)
    @staticmethod
    def _in_jupyter():
        try:
            get_ipython
            return True
        except NameError:
            return False

    @lru_cache(maxsize=1)
    def cached_file_dir(self):
        return os.path.join(
            os.path.dirname(__file__),
            self.OPTION_DATA_FOLDER,
            self.ir_option_name.rstrip(),
        )

    def cached_file_path(self, date: dt.date):
        return os.path.join(
            self.cached_file_dir(),
            f"{self.ir_option_name.rstrip()}_{date.strftime('%Y%m%d')}.parquet",
        )

    @lru_cache(maxsize=1)
    def cached_underlying_dir(self):
        """Underlying futures directory"""
        return os.path.join(
            os.path.dirname(__file__),
            self.OPTION_DATA_FOLDER,
            self.ir_option_name.rstrip() + "_underlying",
        )

    def cached_underlying_file_path(self, date: dt.date):
        """Underlying futures file path"""
        return os.path.join(
            self.cached_underlying_dir(),
            f"{self.ir_option_name.rstrip()}_{date.strftime('%Y%m%d')}.parquet",
        )

    def cached_ois_dir(self):
        """Discount OIS rate directory"""
        return os.path.join(
            os.path.dirname(__file__),
            "ir_future_option_data",
            self.currency + "_discount",
        )

    def cached_ois_file_path(self, date: dt.date):
        """Discount OIS rate file path"""
        return os.path.join(
            self.cached_ois_dir(),
            f"{self.currency}_{date.strftime('%Y%m%d')}.parquet",
        )

    @lru_cache(maxsize=16)
    @staticmethod
    def _check_ticker_exists(ticker):
        data = blp.bdp(ticker, flds="NAME")
        if data.empty:
            print("Ticker does not exist:", ticker)
            return False
        return True

    @lru_cache(maxsize=16)
    @staticmethod
    def _check_weekly_underlying_ticker(option_ticker_root, future_ticker):
        future_px = blp.bdp(future_ticker, flds="PX_LAST")
        if future_px.empty:
            print("Future ticker does not exist or expired:", future_ticker)
            return future_ticker
        future_px = round(future_px.squeeze())
        atm_option_ticker = f"{option_ticker_root}C {future_px} Comdty"
        real_future_ticker = blp.bdp(atm_option_ticker, flds="REAL_UNDERLYING_TICKER")
        if real_future_ticker.empty:
            print(
                "Option ticker does not exist or REAL_UNDERLYING_TICKER not available:",
                atm_option_ticker,
            )
            return future_ticker
        return f"{real_future_ticker.squeeze()} Comdty"

    @staticmethod
    def get_friday_before_second_last_business_day(
        date: ql.Date, calendar: ql.Calendar, prev_months: int = 0
    ) -> ql.Date:
        # End of month
        eom = calendar.endOfMonth(date - ql.Period(prev_months, ql.Months))
        # 2nd last business day - 1d
        second_last = calendar.advance(eom, -1, ql.Days) - 1
        # Friday
        return second_last - (second_last.weekday() - ql.Friday) % 7

    def get_periods(self, start_date, end_date) -> list[tuple[ql.Date, ql.Date]]:
        if self.is_bond_future:
            return interestRateFutureOptionLoader._get_periods_for_us_bond_futures(
                start_date, end_date, self.calendar
            )
        return interestRateFutureOptionLoader._get_periods(start_date, end_date)

    @staticmethod
    def _get_periods(start_date, end_date) -> list[tuple[ql.Date, ql.Date]]:
        """
        Returns
        ----------
        List[(ql.Date, ql.Date)]: List of (Monday before the 3rd Wednesday, i.e. IMM date of current month, Friday before the 3rd Wednesday of next month).
        """
        start_date, end_date = ql.Date.from_date(start_date), ql.Date.from_date(
            end_date
        )
        start_date = ql.IMM.nextDate(ql.Date.startOfMonth(start_date), False)
        periods = []
        while start_date - end_date <= 2:
            next_expiry_date = ql.IMM.nextDate(start_date, False)
            periods.append(
                (
                    start_date - 2,
                    min(next_expiry_date - 5, end_date),
                )
            )
            start_date = next_expiry_date
        return periods

    @staticmethod
    def _get_periods_for_us_bond_futures(
        start_date, end_date, calendar: ql.Calendar
    ) -> list[tuple[ql.Date, ql.Date]]:
        """
        Returns
        ----------
        List[(ql.Date, ql.Date)]: List of (Monday after the Friday before the 2nd last bdays of current month, Friday before the 2nd last bdays of next month).
        """
        start_date, end_date = ql.Date.from_date(start_date), ql.Date.from_date(
            end_date
        )
        start_date = (
            interestRateFutureOptionLoader.get_friday_before_second_last_business_day(
                start_date, calendar, 1
            )
        )
        periods = []
        while start_date - end_date <= -3:
            next_friday = interestRateFutureOptionLoader.get_friday_before_second_last_business_day(
                start_date + ql.Period(1, ql.Months), calendar
            )
            periods.append(
                (
                    start_date + 3,
                    min(next_friday, end_date),
                )
            )
            start_date = next_friday
        return periods

    @lru_cache(maxsize=12)
    @staticmethod
    def _get_imm_codes(serial_num: int, quarter_num: int, date: ql.Date) -> list[str]:
        """
        Returns
        ----------
        List[ql.Date] : IMM codes of the entire quoted option chain on the input date.
        """
        this_month_imm_date = ql.IMM.nextDate(ql.Date.startOfMonth(date), False)

        # The newly appeared option starts trading on the Monday of the IMM week
        if date < this_month_imm_date - 2:
            date = ql.IMM.nextDate(
                ql.Date.startOfMonth(date - ql.Period(1, ql.Months)), False
            )
        else:
            date = this_month_imm_date
        imm_codes = []
        imm_code = ql.IMM.code(date)
        for _ in range(quarter_num):
            imm_code = ql.IMM.nextCode(imm_code, True, date)  # Quarterly IMM dates
            imm_codes.append(imm_code)
        imm_code = ql.IMM.code(date)
        total_num = serial_num + quarter_num
        for _ in range(total_num):
            imm_code = ql.IMM.nextCode(imm_code, False, date)
            if imm_code not in imm_codes:
                imm_codes.append(imm_code)
            if len(imm_codes) == total_num:
                break

        return imm_codes

    @lru_cache(maxsize=12)
    @staticmethod
    def _get_imm_codes_for_us_bond_futures(
        serial_num: int, quarter_num: int, date: ql.Date, calendar: ql.Calendar
    ) -> list[str]:
        """
        Returns
        ----------
        List[ql.Date] : IMM codes of the entire quoted option chain on the input date.
        """
        this_month_friday = (
            interestRateFutureOptionLoader.get_friday_before_second_last_business_day(
                date, calendar
            )
        )

        # The newly appeared option starts trading on the Monday following the Friday
        if date <= this_month_friday:
            date = ql.IMM.nextDate(ql.Date.startOfMonth(date), False)
        else:
            date = ql.IMM.nextDate(ql.Date.endOfMonth(date), False)
        imm_codes = []
        imm_code = ql.IMM.code(date)  # NB: date is not included in the returned list
        for _ in range(quarter_num):
            imm_code = ql.IMM.nextCode(imm_code, True, date)  # Quarterly IMM dates
            imm_codes.append(imm_code)
        imm_code = ql.IMM.code(date)
        total_num = serial_num + quarter_num
        for _ in range(total_num):
            imm_code = ql.IMM.nextCode(imm_code, False, date)
            if imm_code not in imm_codes:
                imm_codes.append(imm_code)
            if len(imm_codes) == total_num:
                break

        return imm_codes

    def get_single_period_option_chain_imm_codes(
        self, date: ql.Date, **kwargs
    ) -> list[str]:
        """
        Returns
        ----------
        List[str] : All the (candidate) option IMM codes on the pricing date. e.g. *['H5', 'M5', 'U5', 'Z5', 'F5', 'G5', 'J5', 'K5']*
        """
        imm_codes = kwargs.get("imm_codes")
        if imm_codes is not None:
            return imm_codes
        config = load_yaml_config(
            self.ir_option_name, "ir_future_option_config", self.currency
        )
        serial_num, quarter_num = kwargs.get("serial", config["serial"]), kwargs.get(
            "quarter", config["quarter"]
        )
        if self.is_bond_future:
            return interestRateFutureOptionLoader._get_imm_codes_for_us_bond_futures(
                serial_num, quarter_num, date, self.calendar
            )
        return interestRateFutureOptionLoader._get_imm_codes(
            serial_num, quarter_num, date
        )

    def get_single_period_option_chain_tickers(
        self, date: ql.Date, underlying_px_data: pd.DataFrame, **kwargs
    ) -> list[str]:
        """
        Returns
        ----------
        List[str] : All the (candidate) option full ticker names on the pricing date.
        There's a lot of hack in this code to limit the number of tickers to query based on the underlying price data.
        """
        if underlying_px_data.empty:
            return list()
        imm_codes = self.get_single_period_option_chain_imm_codes(date, **kwargs)
        config = load_yaml_config(
            self.ir_option_name, "ir_future_option_config", self.currency
        )
        option_tickers = []
        for imm_code in imm_codes:
            underlying_ticker = self.get_underlying_futures_tickers(
                [imm_code], self.ir_option_name, date
            )[0]
            if underlying_ticker not in underlying_px_data.index.get_level_values(1):
                continue
            data = underlying_px_data.xs(underlying_ticker, level=1, drop_level=True)[
                "price"
            ]
            Q1, Q3 = data.quantile(0.25), data.quantile(0.75)
            IQR = Q3 - Q1
            data = data[(data >= Q1 - 3 * IQR) & (data <= Q3 + 3 * IQR)]
            option_expiry_date = self._get_option_expiry_date(imm_code, date)
            vol, tte = config["vol"], option_expiry_date - date
            assert tte >= 0
            if self.is_bond_future:
                if (
                    underlying_ticker + " CTD_FORWARD_DV01"
                    not in underlying_px_data.index.get_level_values(1)
                ):
                    continue
                dv01_data = underlying_px_data.xs(
                    underlying_ticker + " CTD_FORWARD_DV01", level=1, drop_level=True
                )["price"]
                dv01_data = dv01_data[dv01_data.index.isin(data.index)]
                vol *= (
                    dv01_data.max() / data.min()
                )  # Convert bps vol to Black–Scholes pct vol
            var = (
                vol * np.sqrt(tte / self.day_counter_denominator) / 100
            )  # var = vol * sqrt(T)
            vol_range = var * 1.65  # 10-delta is around 1.3, 5-delta is around 1.645
            min_tick_size, tick_size = config["min_tick_size"], config["tick_size"]
            if (
                "min_tick_size_constraint_month" in config
                and tte > 32 * config["min_tick_size_constraint_month"]
            ):  # 32 days is largest difference between option expiry dates
                min_tick_size = tick_size
            atm_low, atm_high = (
                np.floor(data.min() / min_tick_size) * min_tick_size,
                np.ceil(data.max() / min_tick_size) * min_tick_size,
            )
            self._print("ATM Low:", data.min(), "Rounded:", atm_low)
            self._print("ATM High:", data.max(), "Rounded:", atm_high)

            low, high = (
                atm_low - config["min_tick_size_range"],
                atm_high + config["min_tick_size_range"],
            )
            mid_strikes = np.arange(low, high + min_tick_size, min_tick_size)
            # For calls, download most of data with strike >= atm_low
            call_strikes = mid_strikes[
                mid_strikes >= atm_low - min_tick_size  # - var * 0.1
            ]  # Near OOM calls, or add a little ITM calls
            high_5_delta_call = (
                atm_high * np.exp(vol_range + 0.5 * var**2)
                if self.is_bond_future
                else atm_high + vol_range
            )
            high_strikes = np.arange(
                np.ceil(high_5_delta_call / tick_size) * tick_size,
                high,
                -tick_size,
            )  # Far OOM calls, be careful: start needs to be multiples of tick_size!
            if "max_strike_num" in config:
                max_strike_num = int(
                    1 + config["max_strike_num"] + (atm_high - atm_low) / min_tick_size
                )
                call_strikes = call_strikes[:max_strike_num]
                diff = int(len(call_strikes) - max_strike_num)
                high_strikes = high_strikes[
                    (diff if diff <= -1 else len(high_strikes)) :
                ]
            low_strikes = np.arange(
                np.ceil(
                    (atm_low - var * (atm_low if self.is_bond_future else 1) * 0.1)
                    / min_tick_size
                )
                * min_tick_size,
                atm_low - min_tick_size,
                min_tick_size,
            )  # A little ITM calls
            self._print("Call Tickers (low):", low_strikes)
            self._print("Call Tickers (mid):", call_strikes)
            self._print("Call Tickers (high):", high_strikes)
            call_strikes = np.concatenate([low_strikes, call_strikes, high_strikes])
            call_strikes = call_strikes[call_strikes <= config["max_strike"]]

            # For puts, download most of data with strike <= atm_high
            put_strikes = mid_strikes[
                mid_strikes <= atm_high + min_tick_size  # + var * 0.1
            ]  # Near OOM puts, and add a little ITM puts
            low_5_delta_put = (
                atm_low * np.exp(-vol_range + 0.5 * var**2)
                if self.is_bond_future
                else atm_low - vol_range
            )
            low_strikes = np.arange(
                np.floor(low_5_delta_put / tick_size) * tick_size,
                low,
                tick_size,
            )  # Far OOM puts, be careful: start needs to be multiples of tick_size!
            if "max_strike_num" in config:
                put_strikes = put_strikes[-max_strike_num:]
                diff = int(len(put_strikes) - max_strike_num)
                low_strikes = low_strikes[(diff if diff <= -1 else len(low_strikes)) :]
            high_strikes = np.arange(
                np.floor(
                    (atm_high + var * (atm_low if self.is_bond_future else 1) * 0.1)
                    / tick_size
                )
                * min_tick_size,
                atm_high + min_tick_size,
                -min_tick_size,
            )  # A little ITM puts
            self._print("Put Tickers (low):", low_strikes)
            self._print("Put Tickers (mid):", put_strikes)
            self._print("Put Tickers (high):", high_strikes)
            put_strikes = np.concatenate([low_strikes, put_strikes, high_strikes])
            put_strikes = put_strikes[put_strikes >= config["min_strike"]]

            option_tickers.append(
                np.concatenate(
                    [
                        np.unique(self.ticker_formatter(imm_code, call_strikes, "C")),
                        np.unique(self.ticker_formatter(imm_code, put_strikes, "P")),
                    ]
                )
            )
            # print(
            #     imm_code,
            #     "| tte:",
            #     tte,
            #     "days | ATM: ",
            #     data.min(),
            #     data.max(),
            #     "| var:",
            #     var,
            # )
            # print("Call strikes: ", call_strikes, "\nPut strikes: ", put_strikes, "\n")
        return np.concatenate(option_tickers).tolist()

    @lru_cache(maxsize=2)
    @staticmethod
    def get_underlying_futures_root(ir_option_name: str):
        underlying_root = ir_option_name
        if ir_option_name[0].isnumeric():
            match ir_option_name[1]:
                case "Q":
                    underlying_root = "SFR"  # SOFR
                case "N":
                    underlying_root = "SFI"  # SONIA
                case "R":
                    underlying_root = "ER"  # EURIBOR
                case "E":
                    underlying_root = "ED"  # Eurodollar, USD LIBOR
                case "L":
                    underlying_root = "L "  # GBP LIBOR
                case _:
                    raise ValueError(f"Unknown option ticker {ir_option_name}")
        return underlying_root

    @staticmethod
    def get_underlying_futures_tickers(
        option_tickers: list[str], ir_option_name: str, date: ql.Date
    ) -> list[str]:
        """
        Given the list of interest rate option tickers, return the list of underlying futures tickers.

        Parameters
        ----------
        option_tickers : A list of interest rate option tickers, e.g. *['0QZ4C', '0QF5P', '0QG5']*...
        ir_option_name : Option name, e.g. *SFR, 0Q*
        date : Option pricing date, must be smaller than the option_tickers maturities
        """
        option_imm_codes = [
            option_ticker.upper().split()[0].removeprefix(ir_option_name).rstrip("cpCP")
            for option_ticker in option_tickers
        ]
        option_imm_codes = set(option_imm_codes)
        future_tickers = []
        mid_curve, underlying_root = 0, ir_option_name
        if ir_option_name[0].isnumeric():
            mid_curve = max(1, int(ir_option_name[0]))
            underlying_root = (
                interestRateFutureOptionLoader.get_underlying_futures_root(
                    ir_option_name
                )
            )

        underlying_expire_in_arrears = underlying_root in ("SFR", "SFI")
        # ql.Date(20, 3, 2024)  # SFRU23, SFRZ23, SFRH4; TYZ23, TYH24, TYM4; ERZ23, ERH24, ERM4
        cutoff_date_short = ql.Date.startOfMonth(
            ql.Date.todaysDate() - ql.Period(14, ql.Months)
        )
        if not underlying_expire_in_arrears:
            cutoff_date_short = cutoff_date_short + ql.Period(3, ql.Months)
        # Review this!
        cutoff_date_long = ql.Date.startOfMonth(
            ql.Date.todaysDate() + ql.Period(45, ql.Months)
        )
        # ql.Date(1, 9, 2028)  # SFRM8, SFRU28; SFIU9, SFIZ9

        for imm_code in option_imm_codes:
            if len(imm_code) >= 4:  # Friday Weeklies
                underlying_imm_code = ql.IMM.nextCode(imm_code[2:], True)
                future_ticker = f"{underlying_root}{underlying_imm_code} Comdty"
                # Special handling for end of month friday weeklies in month Feb, May, Aug, Nov
                if imm_code[2] in ("G", "K", "Q", "X") and int(imm_code[0]) >= 4:
                    imm_date = ql.IMM.date(imm_code[2:], date - ql.Period(1, ql.Months))
                    # Check if it's end of month friday
                    num_fridays_in_month = (
                        ql.Date.endOfMonth(imm_date)
                        - ql.Date.nthWeekday(
                            1, ql.Friday, imm_date.month(), imm_date.year()
                        )
                    ) // 7 + 1
                    if int(imm_code[0]) >= num_fridays_in_month:
                        future_ticker = interestRateFutureOptionLoader._check_weekly_underlying_ticker(
                            imm_code, future_ticker
                        )
                if future_ticker not in future_tickers:
                    future_tickers.append(future_ticker)
                continue
            imm_date = ql.IMM.date(imm_code, date)
            underlying_imm_date = ql.IMM.nextDate(
                ql.Date.startOfMonth(imm_date + ql.Period(mid_curve, ql.Years)), True
            )
            underlying_imm_code = ql.IMM.code(underlying_imm_date)
            if underlying_imm_date < cutoff_date_short:
                underlying_imm_code = (
                    f"{underlying_imm_code[:-1]}{underlying_imm_date.year() % 100:02d}"
                )
            elif underlying_imm_date <= cutoff_date_short + ql.Period(
                3, ql.Months
            ) and not interestRateFutureOptionLoader._check_ticker_exists(
                f"{underlying_root}{underlying_imm_code} Comdty"
            ):
                underlying_imm_code = (
                    f"{underlying_imm_code[:-1]}{underlying_imm_date.year() % 100:02d}"
                )
            future_ticker = f"{underlying_root}{underlying_imm_code} Comdty"
            if underlying_imm_date > cutoff_date_long:
                future_ticker = f"{ir_option_name}{underlying_imm_code} Comdty"
            if future_ticker not in future_tickers:
                future_tickers.append(future_ticker)
        return future_tickers

    def get_single_period_option_chain_underlying_futures_and_ois_tickers(
        self, period_start_date: ql.Date, period_end_date: ql.Date
    ) -> tuple[list[str], list[str]]:
        """
        Returns
        ----------
        List[str] : All the underlying futures full ticker names on the pricing date.
        """
        if not self.is_bond_future and not ql.IMM.isIMMdate(
            period_start_date + 2, False
        ):
            raise ValueError(f"{period_start_date} + 2d is not an IMM date")
        elif self.is_bond_future and period_start_date.weekday() != ql.Monday:
            raise ValueError(f"{period_start_date} is not a Monday")
        option_imm_codes = self.get_single_period_option_chain_imm_codes(
            period_start_date
        )
        underlying_tickers = self.get_underlying_futures_tickers(
            option_imm_codes,
            self.ir_option_name,
            period_start_date,
        )
        ois_tickers = set()
        for option_imm_code in option_imm_codes:
            option_expiry_date = self._get_option_expiry_date(
                option_imm_code, period_start_date
            )
            tte_start = option_expiry_date - period_end_date
            tte_end = option_expiry_date - period_start_date
            assert tte_start >= 0 and tte_end >= 0
            ois_tickers.update(
                self.get_ois_candidate_tickers(self.ois_root, tte_start, tte_end)
            )
        return underlying_tickers, list(ois_tickers)

    def _get_option_expiry_date(self, option_imm_code: str, date: ql.Date) -> ql.Date:
        # Last trading day is the Friday before the IMM date for STIRs
        option_expiry_date = ql.IMM.date(option_imm_code, date) - 5
        if self.ir_option_name == "ER" and ql.IMM.isIMMcode(option_imm_code, True):
            option_expiry_date += 3
        if self.is_bond_future:
            option_expiry_date = interestRateFutureOptionLoader.get_friday_before_second_last_business_day(
                option_expiry_date, self.calendar, 1
            )
        return option_expiry_date

    @staticmethod
    def _min_start_date(underlying_future_name: str, ir_option_name: str) -> dt.date:
        # TODO: Add more cases
        match underlying_future_name:
            case "SFR" | "SER":
                # NB: 0Q, etc is missing serials before X11
                return dt.date(2020, 12, 1)
            case "ED":  # Check 0E, 2E, etc
                return dt.date(2016, 6, 1)
            case "TU" | "FV" | "TY" | "US":
                return dt.date(2015, 12, 1)
            case "ER":  # Check 0R, 2R, etc
                return dt.date(2017, 3, 1)
            case "DU" | "OE" | "RX":
                return dt.date(2015, 9, 1)
            case "SFI":
                return dt.date(2021, 2, 1)
            case "L ":
                # NB: 0L is missing quarties before Z13, 2L/3/4 is missing before M15 or later
                return dt.date(2013, 12, 1)
            case _:
                return dt.date(2017, 3, 1)

    @staticmethod
    def check_is_bond_future(ir_option_name: str) -> bool:
        match ir_option_name:
            case "TU" | "FV" | "TY" | "UXY" | "US" | "WN":
                return True
            # Germany (Schatz, Bobl，Bund，Buxl), Italy (BTP), France (OAT)
            case "DU" | "OE" | "RX" | "UB" | "BTS" | "MFB" | "IK" | "BTA" | "OAT":
                return True
            case "G ":
                return True
            case "CN":
                return True
        return False

    @staticmethod
    def get_ccy(ir_option_name: str) -> str:
        underlying_future_name = (
            interestRateFutureOptionLoader.get_underlying_futures_root(ir_option_name)
        )
        match underlying_future_name:
            case "SFR" | "SER" | "ED" | "TU" | "FV" | "TY" | "UXY" | "US" | "WN":
                return "USD"
            case "ER" | "DU" | "OE" | "RX" | "BTS" | "IK" | "OAT":
                return "EUR"
            case "SFI" | "L " | "G ":
                return "GBP"
            case "BA" | "CN":
                return "CAD"
            case _:
                raise ValueError(
                    f"Unknown underlying futures ticker {underlying_future_name}"
                )

    @staticmethod
    def get_calendar(ccy: str) -> ql.Calendar:
        calendar = {
            "USD": ql.UnitedStates(ql.UnitedStates.NYSE),
            "GBP": ql.UnitedKingdom(ql.UnitedKingdom.Exchange),
            "EUR": ql.Germany(ql.Germany.Eurex),
        }
        return calendar.get(ccy)

    @staticmethod
    def get_day_counter(ccy: str) -> ql.Calendar:
        day_counter_denominator = {"USD": 360, "GBP": 365, "EUR": 360, "CAD": 365}
        return day_counter_denominator.get(ccy)

    @staticmethod
    def get_ois_root(ccy: str) -> str:
        match ccy:
            case "USD":
                return "USOSFR"
            case "EUR":
                return "EESWE"
            case "GBP":
                return "BPSWS"
            case "CAD":
                return "CDSO"
            case _:
                raise ValueError(f"Unknown currency {ccy}")

    @lru_cache(maxsize=1)
    @staticmethod
    def _load_ticker_maturity_mapping(ois_root: str):
        mapping = load_yaml_config("ticker_maturity_mapping", "ir_future_option_config")
        df = pd.DataFrame(list(mapping.items()), columns=["ticker", "tte"])
        df["ticker"] = ois_root + df["ticker"] + " Curncy"
        return df

    @staticmethod
    def get_ois_candidate_tickers(
        ois_root: str, tte_start: int, tte_end: int
    ) -> list[str]:
        df = interestRateFutureOptionLoader._load_ticker_maturity_mapping(ois_root)
        idx1 = abs(df["tte"] - tte_start).idxmin()
        idx2 = abs(df["tte"] - tte_end).idxmin()
        if idx1 > idx2:
            idx1, idx2 = idx2, idx1
        return df.loc[idx1:idx2, "ticker"].tolist()  # .loc: end of slice is inclusive

    @staticmethod
    def get_ois_ticker(ois_root: str, time_to_maturity: int) -> str:
        """
        Return the closest OIS ticker for the discounting rate in options pricing.
        """
        df = interestRateFutureOptionLoader._load_ticker_maturity_mapping(ois_root)
        df["difference"] = abs(df["tte"] - time_to_maturity)
        return df.loc[df["difference"].idxmin(), "ticker"]

    @staticmethod
    def _filter_by_time_to_maturity(group, time_to_maturity: int):
        group = group.sort_values(by="tte")
        less_than = group[group["tte"] <= time_to_maturity]
        closest_less_than = less_than.tail(2) if not less_than.empty else None
        greater_than = group[group["tte"] > time_to_maturity]
        closest_greater_than = greater_than.head(2) if not greater_than.empty else None
        return pd.concat([closest_less_than, closest_greater_than], axis=0)

    @staticmethod
    def _process_underlying_price_data(raw_futures_price: pd.DataFrame):
        second_levels = raw_futures_price.columns.get_level_values(1)
        fields = second_levels.unique()
        if len(fields) == 1:
            raw_futures_price = raw_futures_price.droplevel(level=1, axis=1)
        else:
            first_levels = raw_futures_price.columns.get_level_values(0)
            raw_futures_price.columns = first_levels + second_levels.map(
                {
                    "PX_SETTLE": "",
                    "CONVENTIONAL_CTD_FORWARD_FRSK": " CTD_FORWARD_DV01",
                    "PX_LAST": "",
                }
            )
        raw_futures_price = (
            raw_futures_price.stack()
            .rename_axis(index=["date", "ticker"])
            .to_frame(name="price")
            .dropna()
        )
        return raw_futures_price

    @staticmethod
    def _process_option_price_data(raw_option_price: pd.DataFrame):
        # Stack raw option data
        raw_option_price = (
            raw_option_price.droplevel(level=1, axis=1)
            .dropna(axis=1, how="all")  # unnecessary
            .rename_axis(index="date")
            .melt(var_name="ticker", value_name="price", ignore_index=False)
        )
        return interestRateFutureOptionLoader._split_option_by_ticker_and_strike(
            raw_option_price
        )

    @staticmethod
    def _split_option_by_ticker_and_strike(raw_option_price: pd.DataFrame):
        # Data formatting, split tickers to ticker_root and strike
        raw_option_price[["ticker_root", "strike"]] = raw_option_price[
            "ticker"
        ].str.extract(r"(\w+)\s+(\d+(?:\.\d+)?)\s+Comdty")
        # Set multi-index to be (date, ticker_root, strike)
        raw_option_price = (
            raw_option_price.drop(columns="ticker")
            .set_index(["ticker_root", "strike"], append=True)
            .sort_index()
            .dropna()
        )

        return raw_option_price

    def _diff_periods(self, exist_files: list[str], required_periods: list[tuple]):
        last_exist_period, periods = None, required_periods
        if exist_files:
            exist_periods_start_dates = pd.to_datetime(
                [f.rsplit(".")[0][-8:] for f in exist_files]
            ).date
            last_exist_period_start_date = exist_periods_start_dates.max()
            last_exist_period = self.get_periods(
                last_exist_period_start_date,
                min(
                    required_periods[-1][-1].to_date(),
                    last_exist_period_start_date + dt.timedelta(days=60),
                ),
            )  # Assume monthly-option
            periods = [
                period
                for period in required_periods
                if period[0].to_date() not in exist_periods_start_dates
            ]
        if last_exist_period:
            last_matched_period = next(
                (
                    period
                    for period in last_exist_period
                    if period[0].to_date() == last_exist_period_start_date
                ),
                None,
            )
            if last_matched_period and last_matched_period not in periods:
                periods.append(last_matched_period)
        return periods

    def load_raw_option_price(self, start_date: dt.date, end_date: dt.date, **params):
        """
        Returns
        ----------
        Tuple[DataFrame, DataFrame] : Whole option chain settlement price DataFrame, and the underlying futures price DataFrame.
        """
        # Check if new tickers are missing in the cached files (new strikes), which will be downloaded
        if not is_bloomberg_connected():
            raise ValueError("Please login to the Bloomberg Terminal!")
        self.bbg_session = bloomberg_session(self._API_KEY_, self._PORT_)
        load_start_time = time.time()
        check_missing_tickers = params.get("check_missing_tickers", False)
        start_date = max(
            start_date,
            interestRateFutureOptionLoader._min_start_date(
                self.udl_name, self.ir_option_name
            ),
        )
        end_date = min(end_date, dt.date.today() - dt.timedelta(days=1))
        """
        The below code calculates the required data periods minus the existing data periods.
        Since the last existent data may have missing data, add it to the list as well.
        The purpose is to reduce the for loop as much as possible.
        """
        periods = self.get_periods(start_date, end_date)
        if not periods:
            return None, None
        exist_option_files = exist_underlying_files = None
        new_option_periods = new_underlying_periods = periods
        if not check_missing_tickers:
            exist_option_files = glob.glob(self.cached_file_dir() + "\\*")
            new_option_periods = self._diff_periods(exist_option_files, periods)
            exist_underlying_files = glob.glob(self.cached_underlying_dir() + "\\*")
            new_underlying_periods = self._diff_periods(exist_underlying_files, periods)

        "**************** Start loading option px data and the underlying data ****************"
        whole_option_px_data = list()
        if exist_option_files:
            exist_option_data = pd.read_parquet(self.cached_file_dir()).sort_index()
            exist_option_data = exist_option_data[periods[0][0].to_date() : end_date]
            whole_option_px_data = [exist_option_data]
        whole_underlying_px_data = list()
        if exist_underlying_files:
            exist_underlying_data = pd.read_parquet(
                self.cached_underlying_dir()
            ).sort_index()
            exist_underlying_data = exist_underlying_data[
                periods[0][0].to_date() : end_date
            ]
            exist_ois_data = pd.read_parquet(self.cached_ois_dir()).sort_index()
            exist_ois_data = exist_ois_data[periods[0][0].to_date() : end_date]
            whole_underlying_px_data = [exist_underlying_data, exist_ois_data]

        if len(new_underlying_periods) > 24 or len(new_option_periods) > 12:
            in_jupyter = interestRateFutureOptionLoader._in_jupyter()
            new_underlying_periods_str = ", ".join(
                [
                    f"{period[0].ISO()} to {period[1].ISO()}"
                    for period in new_underlying_periods
                ]
            )
            new_option_periods_str = ", ".join(
                [
                    f"{period[0].ISO()} to {period[1].ISO()}"
                    for period in new_option_periods
                ]
            )
            warning_str = (
                f"It seems that you're about to download more than 24 months of underlying futures ({self.udl_name}) data (months={len(new_underlying_periods)}): \n{new_underlying_periods_str}. \n\n"
                f"Or more than 12 months of options ({self.ir_option_name}) data (months={len(new_option_periods)}): \n{new_option_periods_str}. \n\n"
                f"You may reach the BBG data limit afterwards! If so, please ask BBG support to reset your limit. \n\n"
                f"Do you want to continue? (y/n) \n\n"
            )

            if not in_jupyter:
                from tkinter import messagebox

                res = messagebox.askyesnocancel(
                    "Warning!",
                    warning_str,
                    icon="warning",
                )
                if res is None or not res:
                    sys.exit("Exiting program.")
            else:
                res = input(warning_str)
                if res.lower() not in ("y", "yes"):
                    sys.exit("Exiting program.")

        print("Start loading data...")
        # Store underlying data
        for period in new_underlying_periods:
            print(
                f"Loading underlying price from {period[0].ISO()} to {period[1].ISO()}"
            )
            underlying_file = self.cached_underlying_file_path(period[0].to_date())
            if os.path.exists(underlying_file):
                underlying_px_data = pd.read_parquet(underlying_file)
                ois_px_data = self.load_underlying_ois_price(
                    period[0].to_date(),
                    period[1].to_date(),
                )
                next_day = self.calendar.advance(
                    ql.Date.from_date(underlying_px_data.index[-1][0]), 1, ql.Days
                )
                if next_day <= period[1]:
                    print(
                        f"Some underlying price history missing, re-checking from {next_day.ISO()} to {period[1].ISO()}"
                    )
                    underlying_tickers, ois_tickers = (
                        self.get_single_period_option_chain_underlying_futures_and_ois_tickers(
                            period[0], period[1]
                        )
                    )
                    underlying_px_data = self.load_underlying_price(
                        period[0].to_date(),
                        period[1].to_date(),
                        tickers=underlying_tickers,
                        check_missing_tickers=True,
                    )
                    ois_px_data = self.load_underlying_ois_price(
                        period[0].to_date(),
                        period[1].to_date(),
                        tickers=ois_tickers,
                        check_missing_tickers=True,  # check if there will be new underlying OIS tickers
                    )
            else:
                underlying_tickers, ois_tickers = (
                    self.get_single_period_option_chain_underlying_futures_and_ois_tickers(
                        period[0], period[1]
                    )
                )
                underlying_px_data = self.load_underlying_price(
                    period[0].to_date(),
                    period[1].to_date(),
                    tickers=underlying_tickers,
                    check_missing_tickers=False,  # doesn't matter
                )
                ois_px_data = self.load_underlying_ois_price(
                    period[0].to_date(),
                    period[1].to_date(),
                    tickers=ois_tickers,
                    check_missing_tickers=True,  # check if there will be new underlying OIS tickers
                )

            whole_underlying_px_data.append(underlying_px_data)
            whole_underlying_px_data.append(ois_px_data)

        whole_underlying_px_data = pd.concat(whole_underlying_px_data, axis=0)
        whole_underlying_px_data = whole_underlying_px_data[
            ~whole_underlying_px_data.index.duplicated(keep="last")
        ].sort_index()  # remove duplicated
        assert not whole_underlying_px_data.empty

        for period in new_option_periods:
            print(f"Loading option price from {period[0].ISO()} to {period[1].ISO()}")
            file = self.cached_file_path(period[0].to_date())
            to_save, option_px_data = False, None
            if os.path.exists(file):
                option_px_data = pd.read_parquet(file)
                next_day = self.calendar.advance(
                    ql.Date.from_date(option_px_data.index[-1][0]), 1, ql.Days
                )
                if next_day <= period[1]:
                    option_tickers = self.get_single_period_option_chain_tickers(
                        period[0],
                        whole_underlying_px_data.loc[
                            next_day.to_date() : period[1].to_date()
                        ],
                    )
                    print(
                        f"Some option price history missing (tickers num: {len(option_tickers)}), read data from bbg from {next_day.ISO()} to {period[1].ISO()}"
                    )
                    missing_time_data = blp.bdh(
                        tickers=option_tickers,
                        flds=["PX_SETTLE"],
                        start_date=next_day.ISO(),
                        end_date=period[1].ISO(),
                        sess=self.bbg_session,
                        port=self._PORT_,
                    )
                    if not missing_time_data.empty:
                        missing_time_data = self._process_option_price_data(
                            missing_time_data
                        )
                        option_px_data = pd.concat(
                            [option_px_data, missing_time_data], axis=0
                        ).sort_index()
                        to_save = True
                if check_missing_tickers:
                    exist_end_date = (next_day - 1).to_date()
                    option_tickers = self.get_single_period_option_chain_tickers(
                        period[0],
                        whole_underlying_px_data.loc[
                            period[0].to_date() : exist_end_date
                        ],
                    )
                    exist_tickers = (
                        option_px_data.loc[period[0].to_date() : exist_end_date]
                        .index.map(lambda idx: f"{idx[1]} {idx[2]} Comdty")
                        .unique()
                    )
                    missing_tickers = list(set(option_tickers) - set(exist_tickers))
                    if len(missing_tickers) > 0:
                        print(
                            f"Some tickers missing, read data from bbg (tickers num: {len(missing_tickers)}): {missing_tickers}"
                        )
                        missing_tickers_data = blp.bdh(
                            tickers=missing_tickers,
                            flds=["PX_SETTLE"],
                            start_date=period[0].ISO(),
                            end_date=str(exist_end_date),
                            # str(max(period[1].to_date(), option_px_data.index[-1][0])) which was necessary
                            sess=self.bbg_session,
                            port=self._PORT_,
                        )
                        if not missing_tickers_data.empty:
                            missing_tickers_data = self._process_option_price_data(
                                missing_tickers_data
                            )
                            option_px_data = pd.concat(
                                [option_px_data, missing_tickers_data], axis=0
                            ).sort_index()
                            to_save = True

            else:
                option_tickers = self.get_single_period_option_chain_tickers(
                    period[0],
                    whole_underlying_px_data.loc[
                        period[0].to_date() : period[1].to_date()
                    ],
                )
                print(
                    f"Whole option price history missing (tickers num: {len(option_tickers)}), read data from bbg from {period[0].ISO()} to {period[1].ISO()}"
                )
                option_px_data = blp.bdh(
                    tickers=option_tickers,
                    flds=["PX_SETTLE"],
                    start_date=period[0].ISO(),
                    end_date=period[1].ISO(),
                    sess=self.bbg_session,
                    port=self._PORT_,
                )
                if not option_px_data.empty:
                    option_px_data = self._process_option_price_data(option_px_data)
                    to_save = True

            if to_save:
                print(
                    f"Storing new option ({self.ir_option_name}) price data from bbg:",
                    file,
                )
                os.makedirs(self.cached_file_dir(), exist_ok=True)
                option_px_data.to_parquet(file)

            whole_option_px_data.append(option_px_data)

        whole_option_px_data = pd.concat(whole_option_px_data, axis=0)
        assert not whole_option_px_data.empty
        whole_option_px_data = (
            whole_option_px_data[~whole_option_px_data.index.duplicated(keep="last")]
            .reset_index()
            .pivot(index=["date", "ticker_root"], columns="strike", values="price")
        )  # remove duplicated

        "**************** Finish loading option px data and the underlying data ****************"

        print(
            f"Load raw {self.ir_option_name} data ---> time:",
            "%.3f" % (time.time() - load_start_time),
        )
        return whole_option_px_data, whole_underlying_px_data

    def load_raw_live_option_price(
        self, maturities, option_types, last_option_data, last_underlying_data
    ):
        if not isinstance(maturities, list):
            maturities = [maturities]
        if len(maturities) != len(option_types):
            raise ValueError(
                "Length of maturities and option_types must be the same for live data"
            )
        ql_today = ql.Date.todaysDate()
        candidates = pd.DataFrame(
            self.get_single_period_option_chain_imm_codes(ql_today),
            columns=["imm_code"],
        )
        candidates["option_expiry_date"] = candidates["imm_code"].map(
            lambda imm_code: self._get_option_expiry_date(imm_code, ql_today)
        )
        candidates["tte"] = candidates["option_expiry_date"] - ql_today
        candidates["underlying_future_ticker"] = candidates["imm_code"].map(
            lambda code: self.get_underlying_futures_tickers(
                [code],
                self.ir_option_name,
                ql_today,
            )[0]
        )
        candidates.sort_values(by="tte", ignore_index=True, inplace=True)
        option_imm_codes, underlying_tickers, ttes = (
            [None] * 2 * len(maturities),
            [None] * 2 * len(maturities),
            [None] * 2 * len(maturities),
        )
        for i in range(len(maturities)):
            less_than = candidates[candidates["tte"] <= maturities[i]]
            greater_than = candidates[candidates["tte"] > maturities[i]]
            if not less_than.empty:
                option_imm_codes[2 * i] = less_than.iloc[-1]["imm_code"]
                underlying_tickers[2 * i] = less_than.iloc[-1][
                    "underlying_future_ticker"
                ]
                ttes[2 * i] = less_than.iloc[-1]["tte"]
            if not greater_than.empty:
                option_imm_codes[2 * i + 1] = greater_than.iloc[0]["imm_code"]
                underlying_tickers[2 * i + 1] = greater_than.iloc[0][
                    "underlying_future_ticker"
                ]
                ttes[2 * i + 1] = greater_than.iloc[0]["tte"]

        print("Loading live underlying price... :", set(underlying_tickers))
        underlying_px_data = blp.bdp(
            tickers=set(underlying_tickers),
            flds=(
                ["PX_LAST", "CONVENTIONAL_CTD_FORWARD_FRSK"]
                if self.is_bond_future
                else ["PX_LAST"]
            ),
            sess=self.bbg_session,
            port=self._PORT_,
        )
        underlying_px_data = underlying_px_data.stack().to_frame(name="price").dropna()
        underlying_px_data.index = underlying_px_data.index.get_level_values(
            0
        ) + underlying_px_data.index.get_level_values(1).str.lower().map(
            {
                "px_last": "",
                "conventional_ctd_forward_frsk": " CTD_FORWARD_DV01",
            }
        )
        underlying_px_data.rename_axis(index="ticker", inplace=True)
        option_tickers = []
        for i in range(len(option_imm_codes)):
            underlying_ticker = underlying_tickers[i]
            if underlying_ticker not in underlying_px_data.index:
                continue
            option_types_lst = option_types[i // 2]
            need_call, need_put = False, False
            for option_type in option_types_lst:
                if (
                    option_type.endswith("C") and option_type[-2].isdigit()
                ) or "P+" in option_type:
                    need_call = True
                elif (
                    option_type.endswith("P") and option_type[-2].isdigit()
                ) or "C-" in option_type:
                    need_put = True
            config = load_yaml_config(
                self.ir_option_name, "ir_future_option_config", self.currency
            )
            min_tick_size, tick_size = config["min_tick_size"], config["tick_size"]
            if (
                "min_tick_size_constraint_month" in config
                and ttes[i] > 32 * config["min_tick_size_constraint_month"]
            ):  # 32 days is largest difference between option expiry dates
                min_tick_size = tick_size
            atm_low, atm_high = (
                np.floor(
                    underlying_px_data.loc[underlying_ticker, "price"] / min_tick_size
                )
                * min_tick_size,
                np.ceil(
                    underlying_px_data.loc[underlying_ticker, "price"] / min_tick_size
                )
                * min_tick_size,
            )
            mid_strikes = np.arange(atm_low, atm_high + min_tick_size, min_tick_size)
            call_strikes = put_strikes = mid_strikes
            if need_call:
                ticker = self.ir_option_name + option_imm_codes[i] + "C"
                if ticker in last_option_data.index:
                    last_strikes = last_option_data.loc[ticker]
                    last_high_strike = (
                        (last_strikes[last_strikes > 0].index.astype(float).max())
                        + underlying_px_data.loc[underlying_ticker, "price"]
                        - last_underlying_data.loc[underlying_ticker, "price"]
                    )
                    num_steps = max((last_high_strike - atm_high) / min_tick_size, 0.0)
                    if num_steps <= 4:
                        high_strikes = np.arrange(
                            atm_high + min_tick_size,
                            last_high_strike + min_tick_size,
                            min_tick_size,
                        )
                    else:
                        high_strikes = np.arange(
                            atm_high + min_tick_size,
                            last_high_strike + min_tick_size,
                            np.ceil(num_steps / 5) * min_tick_size,
                        )
                else:
                    high_strikes = np.arange(
                        atm_high + 10 * tick_size,
                        atm_high,
                        -2 * tick_size,
                    )
                call_strikes = np.concatenate([mid_strikes, high_strikes])
            if need_put:
                ticker = self.ir_option_name + option_imm_codes[i] + "P"
                if ticker in last_option_data.index:
                    last_strikes = last_option_data.loc[ticker]
                    last_low_strike = (
                        (last_strikes[last_strikes > 0].index.astype(float).min())
                        + underlying_px_data.loc[underlying_ticker, "price"]
                        - last_underlying_data.loc[underlying_ticker, "price"]
                    )
                    num_steps = max((atm_low - last_low_strike) / min_tick_size, 0)
                    if num_steps <= 4:
                        low_strikes = np.arrange(
                            atm_low - min_tick_size,
                            last_low_strike - min_tick_size,
                            -min_tick_size,
                        )
                    else:
                        low_strikes = np.arange(
                            atm_low - min_tick_size,
                            last_low_strike - min_tick_size,
                            -np.ceil(num_steps / 5) * min_tick_size,
                        )
                else:
                    low_strikes = np.arange(
                        atm_low - 10 * tick_size,
                        atm_low,
                        2 * tick_size,
                    )
                put_strikes = np.concatenate([mid_strikes, low_strikes])
            option_tickers.append(
                np.concatenate(
                    [
                        np.unique(
                            self.ticker_formatter(
                                option_imm_codes[i], call_strikes, "C"
                            )
                        ),
                        np.unique(
                            self.ticker_formatter(option_imm_codes[i], put_strikes, "P")
                        ),
                    ]
                )
            )
        option_tickers = np.unique(np.concatenate(option_tickers)).tolist()
        print("Loading live option price... :", option_tickers)
        option_px_data = blp.bdp(
            tickers=set(option_tickers),
            flds=["PX_LAST"],
            sess=self.bbg_session,
            port=self._PORT_,
        )
        option_px_data.rename_axis(index="ticker", inplace=True)
        option_px_data.columns = ["price"]
        option_px_data = (
            interestRateFutureOptionLoader._split_option_by_ticker_and_strike(
                option_px_data.reset_index()
            )
            .reset_index()
            .pivot(index=["ticker_root"], columns="strike", values="price")
        )
        return option_px_data, underlying_px_data

    def filter_option_price_by_maturity(
        self, whole_option_px_data, whole_underlying_px_data, maturity: int
    ):
        whole_option_px_data = whole_option_px_data.groupby(
            "date", group_keys=False
        ).apply(
            interestRateFutureOptionLoader._filter_by_time_to_maturity,
            time_to_maturity=maturity,
        )  # need to reset index
        whole_option_px_data["tte_bus_days"] = whole_option_px_data.apply(
            lambda row: self.calendar.businessDaysBetween(
                row["option_pricing_date"],
                row["option_expiry_date"],
            ),
            axis=1,
        )
        whole_option_px_data["underlying_future_ticker"] = whole_option_px_data.apply(
            lambda row: self.get_underlying_futures_tickers(
                [row["ticker_root"]],
                self.ir_option_name,
                row["option_pricing_date"],
            )[0],
            axis=1,
        )
        whole_option_px_data["ois_ticker"] = whole_option_px_data["tte"].map(
            lambda tte: interestRateFutureOptionLoader.get_ois_ticker(
                self.ois_root, tte
            )
        )

        # Transform underlying future ticker to be consistent with the option ticker
        # e.g. in the future, SFRM5 Comdty will be SFRM25 Comdty, but the underlying ticker is stored as SFRM5 Comdty
        pattern = rf"^((?:{re.escape(self.ir_option_name)}|{re.escape(self.udl_name)})[HMUZ])(\d{{2,3}})(.*)$"
        whole_option_px_data["underlying_future_ticker"] = whole_option_px_data[
            "underlying_future_ticker"
        ].map(
            lambda ticker: re.sub(
                pattern, lambda m: m.group(1) + m.group(2)[-1] + m.group(3), ticker
            )
        )
        whole_underlying_px_data.rename(
            index=lambda t: re.sub(
                pattern, lambda m: m.group(1) + m.group(2)[-1] + m.group(3), t
            ),
            level=1,
            inplace=True,
        )

        # Merge underlying and OIS data
        whole_option_px_data.reset_index(drop=True, inplace=True)
        whole_option_px_data = whole_option_px_data.merge(
            whole_underlying_px_data,
            how="left",
            left_on=["date", "underlying_future_ticker"],
            right_index=True,
        )
        whole_option_px_data.rename(columns={"price": "price_atmf"}, inplace=True)
        if self.is_bond_future:
            whole_option_px_data["underlying_ctd_fwd_dv01_ticker"] = (
                whole_option_px_data["underlying_future_ticker"] + " CTD_FORWARD_DV01"
            )
            whole_option_px_data = whole_option_px_data.merge(
                whole_underlying_px_data,
                how="inner",
                left_on=["date", "underlying_ctd_fwd_dv01_ticker"],
                right_index=True,
            )  # Review this!
            whole_option_px_data.rename(
                columns={"price": "price_ctd_fwd_dv01"}, inplace=True
            )

        whole_underlying_px_data.reset_index(inplace=True)
        whole_option_px_data["date"], whole_underlying_px_data["date"] = (
            pd.to_datetime(whole_option_px_data["date"]),
            pd.to_datetime(whole_underlying_px_data["date"]),
        )
        whole_option_px_data = pd.merge_asof(
            whole_option_px_data,
            whole_underlying_px_data,
            on="date",
            left_by="ois_ticker",
            right_by="ticker",
        )
        whole_option_px_data.rename(columns={"price": "price_ois"}, inplace=True)
        whole_option_px_data["discount_factor"] = 1.0 / (
            1
            + whole_option_px_data["price_ois"]
            * whole_option_px_data["tte"]
            / (100 * self.day_counter_denominator)
        )
        whole_option_px_data.drop(
            columns=[
                "option_pricing_date",
                "option_expiry_date",
                "underlying_future_ticker",
                "underlying_ctd_fwd_dv01_ticker",
                "ois_ticker",
                "ticker",
                "price_ois",
            ],
            inplace=True,
            errors="ignore",
        )
        return whole_option_px_data

    def load_option_price(self, start_date: dt.date, end_date: dt.date, **params):
        """
        Returns
        ----------
        DataFrame : Whole option chain settlement price DataFrame.
        """
        whole_option_px_data, whole_underlying_px_data = self.load_raw_option_price(
            start_date, end_date, **params
        )
        use_live = params.get("use_live", False) and dt.date.today() == end_date
        maturities = params.get("maturity")
        if use_live and maturities:
            # Load live data
            option_types = params.get("option_types")
            if option_types and isinstance(option_types, list):
                try:
                    last_date = whole_underlying_px_data.index.get_level_values(
                        "date"
                    ).max()
                    live_option_px_data, live_underlying_px_data = (
                        self.load_raw_live_option_price(
                            maturities,
                            option_types,
                            whole_option_px_data.loc[last_date],
                            whole_underlying_px_data.loc[last_date],
                        )
                    )
                    live_option_px_data = pd.concat(
                        {end_date: live_option_px_data}, names=["date"]
                    )
                    live_underlying_px_data = pd.concat(
                        {end_date: live_underlying_px_data}, names=["date"]
                    )
                    whole_option_px_data = pd.concat(
                        [whole_option_px_data, live_option_px_data], axis=0
                    )
                    whole_underlying_px_data = pd.concat(
                        [whole_underlying_px_data, live_underlying_px_data], axis=0
                    )
                except Exception as e:
                    print(f"Error loading live option price data:\n {e}")
        process_start_time = time.time()
        whole_option_px_data.reset_index(inplace=True)
        # Preprocess data for option pricing if maturities (e.g. 30, 60, 90) are provided
        if not maturities:
            whole_option_px_data["date"] = pd.to_datetime(whole_option_px_data["date"])
            whole_option_px_data.set_index(["date", "ticker_root"], inplace=True)
            return whole_option_px_data
        whole_option_px_data["option_pricing_date"] = whole_option_px_data["date"].map(
            lambda date: ql.Date.from_date(date)
        )
        whole_option_px_data["option_expiry_date"] = whole_option_px_data.apply(
            lambda row: ql.IMM.date(
                row["ticker_root"].removeprefix(self.ir_option_name).rstrip("cpCP"),
                row["option_pricing_date"],
            ),
            axis=1,
        )
        if self.is_bond_future:
            whole_option_px_data["option_expiry_date"] = whole_option_px_data[
                "option_expiry_date"
            ].map(
                lambda date: interestRateFutureOptionLoader.get_friday_before_second_last_business_day(
                    date, self.calendar, 1
                )
            )
        else:
            whole_option_px_data["option_expiry_date"] -= 5
        whole_option_px_data["tte"] = (
            whole_option_px_data["option_expiry_date"]
            - whole_option_px_data["option_pricing_date"]
        )
        if isinstance(maturities, list):
            option_px_data_lst = [None] * len(maturities)
            for i in range(len(maturities)):
                option_px_data_lst[i] = self.filter_option_price_by_maturity(
                    whole_option_px_data.copy(),
                    whole_underlying_px_data.copy(),
                    maturities[i],
                )
        else:
            option_px_data_lst = self.filter_option_price_by_maturity(
                whole_option_px_data, whole_underlying_px_data, maturities
            )
        print(
            f"1. Filter raw {self.ir_option_name} data by maturities: {maturities} ---> time:",
            "%.3f" % (time.time() - process_start_time),
        )
        return option_px_data_lst

    def load_underlying_ois_price(
        self, start_date: dt.date, end_date: dt.date, **params
    ):
        if self.is_bond_future:
            whole_ois_px_data = list()
            periods = self._get_periods(start_date - pd.DateOffset(months=1), end_date)
            for period in periods:
                if period[1].to_date() >= start_date:
                    ois_px_data = self.load_underlying_price(
                        period[0].to_date(),
                        period[1].to_date(),
                        fields=["PX_LAST"],
                        file=self.cached_ois_file_path(period[0].to_date()),
                        **params,
                    )
                    whole_ois_px_data.append(ois_px_data)
            whole_ois_px_data = pd.concat(whole_ois_px_data, axis=0).loc[
                start_date:end_date
            ]
        else:
            whole_ois_px_data = self.load_underlying_price(
                start_date,
                end_date,
                fields=["PX_LAST"],
                file=self.cached_ois_file_path(start_date),
                **params,
            )
        return whole_ois_px_data

    def load_underlying_price(self, start_date: dt.date, end_date: dt.date, **params):
        check_missing_tickers = params.get("check_missing_tickers", False)
        tickers = params.get("tickers")
        file = params.get("file", self.cached_underlying_file_path(start_date))
        fields = params.get(
            "fields",
            (
                ["PX_SETTLE", "CONVENTIONAL_CTD_FORWARD_FRSK"]
                if self.is_bond_future
                else ["PX_SETTLE"]
            ),
        )
        to_save, px_data = False, None
        if os.path.exists(file):
            px_data = pd.read_parquet(file)
            next_day = self.calendar.advance(
                ql.Date.from_date(px_data.index[-1][0]), 1, ql.Days
            ).to_date()
            if next_day <= end_date:
                exist_tickers = px_data.index.get_level_values(1).unique()
                print(
                    f"Some underlying price history missing, read {fields} data from bbg from {next_day} to {end_date}"
                )
                missing_time_data = blp.bdh(
                    tickers=exist_tickers,
                    flds=fields,
                    start_date=str(next_day),
                    end_date=str(end_date),
                    sess=self.bbg_session,
                    port=self._PORT_,
                )
                if not missing_time_data.empty:
                    missing_time_data = self._process_underlying_price_data(
                        missing_time_data
                    )
                    px_data = pd.concat(
                        [px_data, missing_time_data], axis=0
                    ).sort_index()
                    to_save = True
            if check_missing_tickers:
                missing_tickers = list(
                    set(tickers) - set(px_data.index.get_level_values(1))
                )
                if len(missing_tickers) > 0:
                    missing_tickers.sort()
                    print(
                        f"Some underlying tickers missing, read {fields} data from bbg from {start_date} to {max(end_date, px_data.index[-1][0])}: {missing_tickers}"
                    )
                    missing_tickers_data = blp.bdh(
                        tickers=missing_tickers,
                        flds=fields,
                        start_date=str(start_date),
                        # The below is necessary
                        end_date=str(max(end_date, px_data.index[-1][0])),
                        sess=self.bbg_session,
                        port=self._PORT_,
                    )
                    if not missing_tickers_data.empty:
                        missing_tickers_data = self._process_underlying_price_data(
                            missing_tickers_data
                        )
                        px_data = pd.concat(
                            [px_data, missing_tickers_data], axis=0  # NB
                        ).sort_index()
                        to_save = True

        elif tickers:
            print(
                f"Whole underlying price history missing, read {fields} data from bbg from {start_date} to {end_date}: {tickers}"
            )
            px_data = blp.bdh(
                tickers=tickers,
                flds=fields,
                start_date=str(start_date),
                end_date=str(end_date),
                sess=self.bbg_session,
                port=self._PORT_,
            )
            if not px_data.empty:
                px_data = self._process_underlying_price_data(px_data)
                to_save = True

        if to_save:
            print("Storing new underlying price data from bbg:", file)
            os.makedirs(os.path.dirname(file), exist_ok=True)
            px_data.to_parquet(file)

        return px_data


if __name__ == "__main__":
    import cProfile, pstats

    # profiler = cProfile.Profile()
    # profiler.enable()
    loader = interestRateFutureOptionLoader("SFR")
    loader.load_option_price(dt.date(2024, 1, 1), dt.date(2025, 2, 1), maturity=90)
    # profiler.disable()
    # stats = pstats.Stats(profiler)
    # stats.sort_stats("cumtime").print_stats(30)
