{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0006643449634774458"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import QuantLib as ql\n", "import math\n", "\n", "sigma = 0.015  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "t = 5\n", "T = 5.25\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "Z"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0006645656894695"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["math.exp(Z)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["dayCount = 90/360\n", "QuotedFuture = 94"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"data": {"text/plain": ["94.26963448010044"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["AdjustedFuture = 100 - 100 * (((100 - QuotedFuture)/100 * dayCount + 1)/math.exp(Z) - 1) / dayCount\n", "AdjustedFuture"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.2696344801004358"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Bias = AdjustedFuture - QuotedFuture\n", "Bias"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0026573798539097832"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.2657379853909783"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["sigma = 0.015\n", "QuotedFuture_Rate = (100-94)/100\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "AdjustedFuture_Rate = ((QuotedFuture_Rate * dayCount + 1)/math.exp(Z) - 1) / dayCount"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.002696344801004391"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["QuotedFuture_Rate - AdjustedFuture_Rate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["FF Curves on 2024.12.11 using FF 30 days futures"]}, {"cell_type": "code", "execution_count": 229, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.491589763888889e-05\n", "<PERSON><PERSON> estimation: 0.00012127292362500001\n", "Hull-White estimation: 0.00012079787500848943\n", "Hull–White model: 0.00012123676776809589\n", "Implied vol: 123.44267996967352\n", "Implied vol: 123.68516657957588\n"]}], "source": ["QuotedFuture = 95.64\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(1,1,2025))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(31,1,2025))\n", "sigma = 135.94/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.0001/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.0001/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 220, "metadata": {}, "outputs": [{"data": {"text/plain": ["136.58585079999997"]}, "execution_count": 220, "metadata": {}, "output_type": "execute_result"}], "source": ["(100-95.64)/100*31.32703/100*10000"]}, {"cell_type": "code", "execution_count": 230, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.00010099863854166668\n", "<PERSON><PERSON> estimation: 0.0003965131735339506\n", "Hull-White estimation: 0.00039412994184426806\n", "Hull–White model: 0.0003954039084703709\n", "Implied vol: 124.57183564162233\n", "Implied vol: 124.94789955676927\n"]}], "source": ["QuotedFuture = 95.69\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(1,2,2025))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(28,2,2025))\n", "sigma = 136.55/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00033/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00033/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [{"data": {"text/plain": ["135.96683730000004"]}, "execution_count": 221, "metadata": {}, "output_type": "execute_result"}], "source": ["(100-95.69)/100*31.54683/100*10000"]}, {"cell_type": "code", "execution_count": 231, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.00017949067500000004\n", "<PERSON><PERSON> estimation: 0.0008376231500000001\n", "Hull-White estimation: 0.0008305779384303317\n", "Hull–White model: 0.0008335192801354196\n", "Implied vol: 131.74650984805197\n", "Implied vol: 132.30408669704775\n"]}], "source": ["QuotedFuture = 95.75\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(1,3,2025))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(31,3,2025))\n", "sigma = 139.23/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00075/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00075/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [{"data": {"text/plain": ["139.72334"]}, "execution_count": 222, "metadata": {}, "output_type": "execute_result"}], "source": ["(100-95.75)/100*32.87608/100*10000"]}, {"cell_type": "code", "execution_count": 232, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.00024053442117708332\n", "<PERSON><PERSON> estimation: 0.0014017350751354166\n", "Hull-White estimation: 0.0013865904881750553\n", "Hull–White model: 0.0013913368577220808\n", "Implied vol: 133.50806824286965\n", "Implied vol: 134.2351875900172\n"]}], "source": ["QuotedFuture = 95.75\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(1,4,2025))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(30,4,2025))\n", "sigma = 139.17/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00129/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00129/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 234, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0003159888741319444\n", "<PERSON><PERSON> estimation: 0.002117125456684028\n", "Hull-White estimation: 0.0020890335772876774\n", "Hull–White model: 0.00209643041286256\n", "Implied vol: 134.2279713809605\n", "Implied vol: 135.12746003728367\n"]}], "source": ["QuotedFuture = 95.75\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(1,5,2025))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.Date(31,5,2025))\n", "sigma = 139.15/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00197/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00197/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["SOFR"]}, {"cell_type": "code", "execution_count": 223, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.00035368994779243836\n", "<PERSON><PERSON> estimation: 0.0010882767624382718\n", "Hull-White estimation: 0.0010727091128647018\n", "Hull–White model: 0.0010838657549783193\n", "Implied vol: 103.31530611740986\n", "Implied vol: 104.06228544723844\n"]}], "source": ["QuotedFuture = 95.885\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.date(\"H5\"))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.nextDate(\"H5\"))\n", "sigma = 101.39/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00113/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00113/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 236, "metadata": {}, "outputs": [{"data": {"text/plain": ["65.32519999999992"]}, "execution_count": 236, "metadata": {}, "output_type": "execute_result"}], "source": ["15.76/100*(100-95.855)/100*10000"]}, {"cell_type": "code", "execution_count": 228, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0006821163278854167\n", "<PERSON><PERSON> estimation: 0.0027809357983020833\n", "Hull-White estimation: 0.002722149752675041\n", "Hull–White model: 0.0027494234172991128\n", "Implied vol: 103.71609006917673\n", "Implied vol: 104.83000606911324\n"]}], "source": ["QuotedFuture = 96.035\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.date(\"M5\"))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.nextDate(\"M5\"))\n", "sigma = 101.39/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00291/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00291/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 227, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0010105427079783946\n", "<PERSON><PERSON> estimation: 0.005130447594351851\n", "Hull-White estimation: 0.004986042328370458\n", "Hull–White model: 0.005034723541584185\n", "Implied vol: 103.53671608175738\n", "Implied vol: 105.0253248541771\n"]}], "source": ["QuotedFuture = 96.135\n", "t = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.date(\"U5\"))\n", "T = ql.Actual360().yearFraction(ql.Date(11,12,2024), ql.IMM.nextDate(\"U5\"))\n", "sigma = 101.39/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00535/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00535/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0010141517890783183\n", "<PERSON><PERSON> estimation: 0.0051599151466292445\n", "Hull-White estimation: 0.0050142808258112175\n", "Hull–White model: 0.005062920691983663\n"]}], "source": ["QuotedFuture = 96.16\n", "t = ql.Actual360().yearFraction(ql.Date(10,12,2024), ql.IMM.date(\"U5\"))\n", "T = ql.Actual360().yearFraction(ql.Date(10,12,2024), ql.IMM.nextDate(\"U5\"))\n", "sigma = 101.39/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["EURIBOR 3M"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["t = ql.TARGET().businessDaysBetween(ql.Date(10,12,2024), ql.IMM.date(\"Z4\"))/252\n", "T = ql.TARGET().businessDaysBetween(ql.Date(10,12,2024), ql.IMM.nextDate(\"Z4\"))/252"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.1750123456790127e-05\n", "<PERSON><PERSON> estimation: 4.5412345679012356e-05\n", "Hull-White estimation: 4.504665372742684e-05\n", "Hull–White model: 4.536889741757477e-05\n", "Implied vol: 71.52474728151235\n", "Implied vol: 71.81448195129218\n"]}], "source": ["QuotedFuture = 97.17\n", "t = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.date(\"Z4\"))\n", "T = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.nextDate(\"Z4\"))\n", "sigma = 88/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00003/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00003/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0002632422222222223\n", "<PERSON><PERSON> estimation: 0.0008193044444444447\n", "Hull-White estimation: 0.0008076502375239288\n", "Hull–White model: 0.0008120521302004136\n", "Implied vol: 82.49474130687652\n", "Implied vol: 83.08779960487318\n"]}], "source": ["QuotedFuture = 97.795\n", "t = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.date(\"H5\"))\n", "T = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.nextDate(\"H5\"))\n", "sigma = 88/10000 # /(1+(T-t)*(100-QuotedFuture)/100)  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(sigma**2 /2  * (T-t) * t * 100)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00072/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00072/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.0020557451851851856\n", "Hull-White estimation: 0.002012736825310744\n", "Hull–White model: 0.0020222855847435994\n", "Implied vol: 83.25435159584288\n", "Implied vol: 84.13914361449544\n"]}], "source": ["QuotedFuture = 98.08\n", "t = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.date(\"M5\"))\n", "T = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.nextDate(\"M5\"))\n", "sigma = 88/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00184/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00184/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.003782041728395061\n", "Hull-White estimation: 0.003676717761612756\n", "Hull–White model: 0.0036933379351751228\n", "Implied vol: 83.06813584800877\n", "Implied vol: 84.24952791785697\n"]}], "source": ["QuotedFuture = 98.19\n", "a = 0.03\n", "t = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.date(\"U5\"))\n", "T = ql.Thirty360(ql.Thirty360.USA).yearFraction(ql.Date(10,12,2024), ql.IMM.nextDate(\"U5\"))\n", "sigma = 88/10000 # /(1+(T-t)*(100-QuotedFuture)/100)   # sigma is in absolute form, i.e. for rates = 0.06\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00337/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00337/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [{"data": {"text/plain": ["109.75958576209491"]}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["sigma = 82/10000\n", "sigma * math.sqrt(ql.daysBetween(ql.Date(10,12,2024), ql.IMM.date(\"u6\"))/360) * 10000"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.005346202592592594\n", "Hull-White estimation: 0.005157542977655122\n", "Hull–White model: 0.005180519729250932\n", "Implied vol: 81.79880562884651\n", "Implied vol: 83.28144285098352\n"]}], "source": ["QuotedFuture = 98.235\n", "t = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.date(\"Z5\"))/360\n", "T = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.nextDate(\"Z5\"))/360\n", "sigma = 82/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "a = 0.03\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00532/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00532/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.007746997453703706\n", "Hull-White estimation: 0.007418849081954663\n", "Hull–White model: 0.007451878609742835\n", "Implied vol: 81.5382770996378\n", "Implied vol: 83.32205430474198\n"]}], "source": ["QuotedFuture = 98.235\n", "t = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.date(\"H6\"))/360\n", "T = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.nextDate(\"H6\"))/360\n", "sigma = 82/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00766/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.00766/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.010022499165530864\n", "Hull-White estimation: 0.009527308588372519\n", "Hull–White model: 0.009570181359265462\n", "Implied vol: 81.27022887887544\n", "Implied vol: 83.3555230719417\n"]}], "source": ["sigma = 79.82/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "QuotedFuture = 98.215\n", "a = 0.03\n", "t = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.date(\"M6\"))/360\n", "T = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.nextDate(\"M6\"))/360\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.01039/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.01039/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON> estimation: 0.013299468473848376\n", "Hull-White estimation: 0.012549059080825932\n", "Hull–White model: 0.012606433171285403\n", "Implied vol: 81.02379129993481\n", "Implied vol: 83.41115235952603\n"]}], "source": ["sigma = 80.39/10000  # sigma is in absolute form, i.e. for rates = 0.06\n", "QuotedFuture = 98.185\n", "a = 0.03\n", "t = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.date(\"U6\"))/360\n", "T = ql.daysBetween(ql.Date(10,12,2024), ql.IMM.nextDate(\"U6\"))/360\n", "B0 = (1-math.exp(-a*t))/a\n", "B = (1-math.exp(-a * (T-t)))/a\n", "v = (1- math.exp(-2*a*t))/(2*a)\n", "Z = sigma**2 * v * B**2 + sigma**2 / 2 * B * (B0**2)\n", "print(\"<PERSON><PERSON> estimation:\", sigma**2 /2 * (2 * (T-t) * t + t**2) * 100)\n", "print(\"Hull-White estimation:\", sigma**2 /2 * B / (T-t) * (2 * B*v + B0**2) * 100)\n", "print(\"Hull–White model:\", 100 - 100 * (((100 - QuotedFuture)/100 * (T-t) + 1)/math.exp(Z) - 1) / (T-t) - QuotedFuture)\n", "print(\"Implied vol:\", 10000*math.sqrt(0.01351/(1 / 2 * (2 * (T-t) * t + t**2) * 100)))\n", "print(\"Implied vol:\", 10000*math.sqrt(0.01351/(1 / 2 * B / (T-t) * (2 * B*v + B0**2) * 100)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import datetime as dt\n", "import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "from loader.curve_data_loader import curveBenchmarkLoader"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-01, 2024-12-10]\n", "Loading close price from 2024-01-01 to 2024-12-10\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_3M_20240101_marketclose.parquet\n"]}], "source": ["loader = curveBenchmarkLoader(\"EUR.EURIBOR.3M\")\n", "benchmark_data, bbg_data = loader.load_close_px_for_period(dt.date(2024,1,1), dt.date(2024,12,10))"]}, {"cell_type": "code", "execution_count": 264, "metadata": {}, "outputs": [{"data": {"text/plain": ["2024-01-03    0.00024\n", "2024-01-04   -0.00007\n", "2024-01-05    0.00014\n", "2024-01-08   -0.00011\n", "2024-01-09    0.00003\n", "               ...   \n", "2024-12-04   -0.00002\n", "2024-12-05    0.00004\n", "2024-12-06   -0.00013\n", "2024-12-09   -0.00006\n", "2024-12-10    0.00010\n", "Name: EUR003M Index, Length: 241, dtype: float64"]}, "execution_count": 264, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_data = bbg_data/100\n", "change = bbg_data.diff().dropna()\n", "change"]}, {"cell_type": "code", "execution_count": 267, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(27.944335260963264)"]}, "execution_count": 267, "metadata": {}, "output_type": "execute_result"}], "source": ["np.std(change) * 10000 * np.sqrt(252)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["from curves import euribor3MCurve\n", "c = euribor3MCurve(\"20241016\")"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["b, d = c.fetch_curve_benchmarks()"]}, {"cell_type": "code", "execution_count": 100, "metadata": {}, "outputs": [{"data": {"text/plain": ["'PiecewiseLogCubicDiscount'"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["params= {}\n", "params.setdefault(\"interp_method\", \"PiecewiseLogCubicDiscount\")"]}, {"cell_type": "code", "execution_count": 101, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'interp_method': 'PiecewiseLogCubicDiscount'}"]}, "execution_count": 101, "metadata": {}, "output_type": "execute_result"}], "source": ["params"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["bbg_data = pd.read_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_ESTR_20240101_marketclose.parquet\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([2024-01-01, 2024-01-02, 2024-01-03, 2024-01-04, 2024-01-05, 2024-01-08,\n", "       2024-01-09, 2024-01-10, 2024-01-11, 2024-01-12,\n", "       ...\n", "       2024-11-27, 2024-11-28, 2024-11-29, 2024-12-02, 2024-12-03, 2024-12-04,\n", "       2024-12-05, 2024-12-06, 2024-12-09, 2024-12-10],\n", "      dtype='object', length=246)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_data.index"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["idx = bbg_data.columns[bbg_data.columns.str.endswith(\"Index\")]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["(dt.date(2024,1,1)-dt.date(2024,1,1)).days"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [], "source": ["ql.Settings.instance().evaluationDate = ql.Date(11,12,2024)\n", "helper = ql.DepositRateHelper(\n", "                    1 / 100, ql.Euribor3M()\n", "                )"]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(13,3,2025)"]}, "execution_count": 124, "metadata": {}, "output_type": "execute_result"}], "source": ["helper.maturityDate()"]}, {"cell_type": "code", "execution_count": 125, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(13,12,2024)"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["helper.earliestDate()"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [], "source": ["data = bbg_data.loc[dt.date(2024,1,1)]"]}, {"cell_type": "code", "execution_count": 202, "metadata": {}, "outputs": [], "source": ["a = data.index[data.isna()]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["ql.Euribor3M().fixingDays()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["loader = curveBenchmarkLoader(\"EUR.EURIBOR.3M\")\n", "b = loader.get_benchmarks(dt.date(2024,1,2))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["benchmarks_meta_data = loader.get_benchmarks(dt.date(2024,12,12))[\"benchmarks\"]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-01, 2024-10-01]\n", "Loading close price from 2024-01-01 to 2024-10-01\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_3M_20240101_marketclose.parquet\n"]}], "source": ["benchmark_data, data = loader.load_close_px_for_period(dt.date(2024,1,1), dt.date(2024,10,1))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["bbg_tickers = benchmark_data[0][\"benchmarks\"][\"ticker\"]\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["bbg_bar_data = data[0].loc[dt.date(2024,1,2)].copy()"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["EUR003M Index       3.9050\n", "ER1 Comdty         96.3600\n", "ER2 Comdty         96.8700\n", "ER3 Comdty         97.3350\n", "ER4 Comdty         97.6750\n", "ER5 Comdty         97.9050\n", "ER6 Comdty         98.0400\n", "ER7 Comdty         98.0800\n", "ER8 Comdty         98.0700\n", "EUSW4V3 Curncy      2.4190\n", "EUSW5V3 Curncy      2.3928\n", "EUSW6V3 Curncy      2.3880\n", "EUSW7V3 Curncy      2.4080\n", "EUSW8V3 Curncy      2.4317\n", "EUSW9V3 Curncy      2.4592\n", "EUSW10V3 Curncy     2.4928\n", "EUSW11V3 Curncy     2.5262\n", "EUSW12V3 Curncy     2.5575\n", "EUSW15V3 Curncy     2.6150\n", "EUSW20V3 Curncy     2.6033\n", "EUSW25V3 Curncy     2.5255\n", "EUSW30V3 Curncy     2.4497\n", "EUSW40V3 Curncy     2.3252\n", "EUSW50V3 Curncy     2.2150\n", "Name: 2024-01-02, dtype: float64"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["a = bbg_bar_data.reindex(bbg_bar_data.index.union([\"EUSW25V3 Curncy\", \"EUR003M Index\"]), copy=True)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["EUR003M Index       3.9050\n", "ER1 Comdty         96.3600\n", "ER2 Comdty         96.8700\n", "ER3 Comdty         97.3350\n", "ER4 Comdty         97.6750\n", "ER5 Comdty         97.9050\n", "ER6 Comdty         98.0400\n", "ER7 Comdty         98.0800\n", "ER8 Comdty         98.0700\n", "EUSW4V3 Curncy      2.4190\n", "EUSW5V3 Curncy      2.3928\n", "EUSW6V3 Curncy      2.3880\n", "EUSW7V3 Curncy      2.4080\n", "EUSW8V3 Curncy      2.4317\n", "EUSW9V3 Curncy      2.4592\n", "EUSW10V3 Curncy     2.4928\n", "EUSW11V3 Curncy     2.5262\n", "EUSW12V3 Curncy     2.5575\n", "EUSW15V3 Curncy     2.6150\n", "EUSW20V3 Curncy     2.6033\n", "EUSW25V3 Curncy     2.5255\n", "EUSW30V3 Curncy     2.4497\n", "EUSW40V3 Curncy     2.3252\n", "EUSW50V3 Curncy     2.2150\n", "Name: 2024-01-02, dtype: float64"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['EUR003M Index', 'ER1 Comdty', 'ER2 Comdty', 'ER3 Comdty', 'ER4 Comdty',\n", "       'ER5 Comdty', 'ER6 Comdty', 'ER7 Comdty', 'ER8 Comdty',\n", "       'EUSW4V3 Curncy', 'EUSW5V3 Curncy', 'EUSW6V3 Curncy', 'EUSW7V3 Curncy',\n", "       'EUSW8V3 Curncy', 'EUSW9V3 Curncy', 'EUSW10V3 Curncy',\n", "       'EUSW11V3 Curncy', 'EUSW12V3 Curncy', 'EUSW15V3 Curncy',\n", "       'EUSW20V3 Curncy', 'EUSW25V3 Curncy', 'EUSW30V3 Curncy',\n", "       'EUSW40V3 Curncy', 'EUSW50V3 Curncy'],\n", "      dtype='object')"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index.union([\"EUSW25V3 Curncy\", \"EUR003M Index\"], sort=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['E', 'ER1 Comdty', 'ER2 Comdty', 'ER3 Comdty', 'ER4 Comdty',\n", "       'ER5 Comdty', 'ER6 Comdty', 'ER7 Comdty', 'ER8 Comdty', 'EUR003M Index',\n", "       'EUSW10V3 Curncy', 'EUSW11V3 Curncy', 'EUSW12V3 Curncy',\n", "       'EUSW15V3 Curncy', 'EUSW20V3 Curncy', 'EUSW25V3 Curncy',\n", "       'EUSW30V3 Curncy', 'EUSW40V3 Curncy', 'EUSW4V3 Curncy',\n", "       'EUSW50V3 Curncy', 'EUSW5V3 Curncy', 'EUSW6V3 Curncy', 'EUSW7V3 Curncy',\n", "       'EUSW8V3 Curncy', 'EUSW9V3 Curncy', 'a', 'b', 'c'],\n", "      dtype='object')"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index.union([\"a\",\"b\",\"c\",\"E\"])"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/plain": ["E                      NaN\n", "ER1 Comdty         96.3600\n", "ER2 Comdty         96.8700\n", "ER3 Comdty         97.3350\n", "ER4 Comdty         97.6750\n", "ER5 Comdty         97.9050\n", "ER6 Comdty         98.0400\n", "ER7 Comdty         98.0800\n", "ER8 Comdty         98.0700\n", "EUR003M Index       3.9050\n", "EUSW10V3 Curncy     2.4928\n", "EUSW11V3 Curncy     2.5262\n", "EUSW12V3 Curncy     2.5575\n", "EUSW15V3 Curncy     2.6150\n", "EUSW20V3 Curncy     2.6033\n", "EUSW25V3 Curncy     2.5255\n", "EUSW30V3 Curncy     2.4497\n", "EUSW40V3 Curncy     2.3252\n", "EUSW4V3 Curncy      2.4190\n", "EUSW50V3 Curncy     2.2150\n", "EUSW5V3 Curncy      2.3928\n", "EUSW6V3 Curncy      2.3880\n", "EUSW7V3 Curncy      2.4080\n", "EUSW8V3 Curncy      2.4317\n", "EUSW9V3 Curncy      2.4592\n", "a                      NaN\n", "b                      <PERSON><PERSON>\n", "c                      NaN\n", "Name: 2024-01-02, dtype: float64"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["d = pd.Series({\"EUR003M Index\": 1000, \"EUSW25V3 Curncy\": 2000, \"t\": 3000},name=dt.date(2024,1,1))"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index([             1000,              2000,              3000,\n", "            'ER1 Comdty',      'ER2 Comdty',      'ER3 Comdty',\n", "            'ER4 Comdty',      'ER5 Comdty',      'ER6 Comdty',\n", "            'ER7 Comdty',      'ER8 Comdty',   'EUR003M Index',\n", "       'EUSW10V3 Curncy', 'EUSW11V3 Curncy', 'EUSW12V3 Curncy',\n", "       'EUSW15V3 Curncy', 'EUSW20V3 Curncy', 'EUSW25V3 Curncy',\n", "       'EUSW30V3 Curncy', 'EUSW40V3 Curncy',  'EUSW4V3 Curncy',\n", "       'EUSW50V3 Curncy',  'EUSW5V3 Curncy',  'EUSW6V3 Curncy',\n", "        'EUSW7V3 Curncy',  'EUSW8V3 Curncy',  'EUSW9V3 Curncy'],\n", "      dtype='object')"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index.union(d)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1000.0)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["d.combine_first(bbg_bar_data)[benchmark_data[0][\"benchmarks\"][\"ticker\"]][[\"EUR003M Index\"]]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["bbg_bar_data.update(d)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["EUR003M Index      1000.0000\n", "ER1 Comdty           96.3600\n", "ER2 Comdty           96.8700\n", "ER3 Comdty           97.3350\n", "ER4 Comdty           97.6750\n", "ER5 Comdty           97.9050\n", "ER6 Comdty           98.0400\n", "ER7 Comdty           98.0800\n", "ER8 Comdty           98.0700\n", "EUSW4V3 Curncy        2.4190\n", "EUSW5V3 Curncy        2.3928\n", "EUSW6V3 Curncy        2.3880\n", "EUSW7V3 Curncy        2.4080\n", "EUSW8V3 Curncy        2.4317\n", "EUSW9V3 Curncy        2.4592\n", "EUSW10V3 Curncy       2.4928\n", "EUSW11V3 Curncy       2.5262\n", "EUSW12V3 Curncy       2.5575\n", "EUSW15V3 Curncy       2.6150\n", "EUSW20V3 Curncy       2.6033\n", "EUSW25V3 Curncy    2000.0000\n", "EUSW30V3 Curncy       2.4497\n", "EUSW40V3 Curncy       2.3252\n", "EUSW50V3 Curncy       2.2150\n", "Name: 2024-01-02, dtype: float64"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["bbg_bar_data.index.union()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["0       EUR003M Index\n", "1          ER1 Comdty\n", "2          ER2 Comdty\n", "3          ER3 Comdty\n", "4          ER4 Comdty\n", "5          ER5 Comdty\n", "6          ER6 Comdty\n", "7          ER7 Comdty\n", "8          ER8 Comdty\n", "9      EUSW4V3 Curncy\n", "10     EUSW5V3 Curncy\n", "11     EUSW6V3 Curncy\n", "12     EUSW7V3 Curncy\n", "13     EUSW8V3 Curncy\n", "14     EUSW9V3 Curncy\n", "15    EUSW10V3 Curncy\n", "16    EUSW11V3 Curncy\n", "17    EUSW12V3 Curncy\n", "18    EUSW15V3 Curncy\n", "19    EUSW20V3 Curncy\n", "20    EUSW25V3 Curncy\n", "21    EUSW30V3 Curncy\n", "22    EUSW40V3 Curncy\n", "23    EUSW50V3 Curncy\n", "Name: ticker, dtype: object"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["benchmark_data[0][\"benchmarks\"][\"ticker\"]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Read live data from bbg...\n", "Live data fetched from bbg.\n", "NB Live: Loading index fixing on 2024-12-11\n"]}], "source": ["loader = curveBenchmarkLoader(\"USD.SOFR\")\n", "benchmark_data, data = loader.load_live_px(dt.date(2024,12,12))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["SOFRRATE Index     4.62000\n", "USOSFR10 Curncy    3.83955\n", "USOSFR11 Curncy    3.84785\n", "USOSFR12 <PERSON><PERSON>cy    3.86010\n", "USOSFR15 Curncy    3.88505\n", "USOSFR1F <PERSON>cy    4.05050\n", "USOSFR1Z Curncy    4.45820\n", "USOSFR2 Curncy     4.00425\n", "USOSFR20 Curncy    3.87500\n", "USOSFR25 C<PERSON><PERSON>    3.80420\n", "USOSFR2Z Curncy    4.41940\n", "USOSFR3 Curncy     3.93295\n", "USOSFR30 Curncy    3.71700\n", "USOSFR3Z Curncy    4.41300\n", "USOSFR4 Curncy     3.88235\n", "USOSFR40 Curncy    3.51870\n", "USOSFR5 Curncy     3.85070\n", "USOSFR50 Curncy    3.32150\n", "USOSFR6 Curncy     3.83650\n", "USOSFR7 Curncy     3.82800\n", "USOSFR8 Curncy     3.82930\n", "USOSFR9 Curncy     3.83295\n", "USOSFRA Curncy     4.40085\n", "USOSFRB Curncy     4.36700\n", "USOSFRC Curncy     4.35575\n", "USSOSR1 Curncy     4.37300\n", "USSOSR2 Curncy     4.29600\n", "USSOSR3 Curncy     4.16800\n", "USSOSR4 Curncy     4.06600\n", "USSOSR5 Curncy     4.00000\n", "USSOSR6 Curncy     3.95000\n", "USSOSR7 Curncy     3.89800\n", "Name: px_mid, dtype: float64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["helper = ql.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(100, 1, ql.Euribor6M())"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(20,1,2025)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["helper.earliestDate()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["Date(18,7,2025)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["helper.maturityDate()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}