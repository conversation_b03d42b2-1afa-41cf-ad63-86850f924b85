name: excel
channels:
  - defaults
dependencies:
  - _anaconda_depends=2024.06
  - abseil-cpp=20211102.0
  - aext-assistant=4.0.15
  - aext-assistant-server=4.0.15
  - aext-core=4.0.15
  - aext-core-server=4.0.15
  - aext-panels=4.0.15
  - aext-panels-server=4.0.15
  - aext-share-notebook=4.0.15
  - aext-share-notebook-server=4.0.15
  - aext-shared=4.0.15
  - aiobotocore=2.12.3
  - aiohttp=3.9.5
  - aioitertools=0.7.1
  - aiosignal=1.2.0
  - alabaster=0.7.16
  - altair=5.0.1
  - anaconda-anon-usage=0.4.4
  - anaconda-catalogs=0.2.0
  - anaconda-client=1.12.3
  - anaconda-cloud-auth=0.5.1
  - anaconda-navigator=2.6.3
  - anaconda-project=0.11.1
  - anaconda-toolbox=4.0.15
  - annotated-types=0.6.0
  - anyio=4.2.0
  - aom=3.6.0
  - appdirs=1.4.4
  - archspec=0.2.3
  - argon2-cffi=21.3.0
  - argon2-cffi-bindings=21.2.0
  - arrow=1.2.3
  - arrow-cpp=14.0.2
  - astroid=2.14.2
  - astropy=6.1.0
  - astropy-iers-data=0.2024.6.3.0.31.14
  - asttokens=2.0.5
  - async-lru=2.0.4
  - atomicwrites=1.4.0
  - attrs=23.1.0
  - automat=20.2.0
  - autopep8=2.0.4
  - aws-c-auth=0.6.19
  - aws-c-cal=0.5.20
  - aws-c-common=0.8.5
  - aws-c-compression=0.2.16
  - aws-c-event-stream=0.2.15
  - aws-c-http=0.6.25
  - aws-c-io=0.13.10
  - aws-c-mqtt=0.7.13
  - aws-c-s3=0.1.51
  - aws-c-sdkutils=0.1.6
  - aws-checksums=0.1.13
  - aws-crt-cpp=0.18.16
  - aws-sdk-cpp=1.10.55
  - babel=2.11.0
  - bcrypt=3.2.0
  - beautifulsoup4=4.12.3
  - binaryornot=0.4.4
  - black=24.4.2
  - blas=1.0
  - bleach=4.1.0
  - blinker=1.6.2
  - blosc=1.21.3
  - bokeh=3.4.1
  - boltons=23.0.0
  - boost-cpp=1.82.0
  - botocore=1.34.69
  - bottleneck=1.3.7
  - brotli=1.0.9
  - brotli-bin=1.0.9
  - brotli-python=1.0.9
  - bzip2=1.0.8
  - c-ares=1.19.1
  - c-blosc2=2.12.0
  - ca-certificates=2024.9.24
  - cachetools=5.3.3
  - certifi=2024.8.30
  - cffi=1.16.0
  - cfitsio=3.470
  - chardet=4.0.0
  - charls=2.2.0
  - charset-normalizer=2.0.4
  - click=8.1.7
  - cloudpickle=2.2.1
  - colorama=0.4.6
  - colorcet=3.1.0
  - comm=0.2.1
  - conda=24.9.1
  - conda-build=24.5.1
  - conda-content-trust=0.2.0
  - conda-index=0.5.0
  - conda-libmamba-solver=24.1.0
  - conda-pack=0.7.1
  - conda-package-handling=2.3.0
  - conda-package-streaming=0.10.0
  - conda-repo-cli=1.0.88
  - conda-token=0.5.0
  - console_shortcut=0.1.1
  - constantly=23.10.4
  - contourpy=1.2.0
  - cookiecutter=2.6.0
  - cryptography=42.0.5
  - cssselect=1.2.0
  - curl=8.7.1
  - cycler=0.11.0
  - cytoolz=0.12.2
  - dask=2024.5.0
  - dask-core=2024.5.0
  - dask-expr=1.1.0
  - datashader=0.16.2
  - dav1d=1.2.1
  - debugpy=1.6.7
  - decorator=5.1.1
  - defusedxml=0.7.1
  - diff-match-patch=20200713
  - dill=0.3.8
  - distributed=2024.5.0
  - distro=1.9.0
  - docstring-to-markdown=0.11
  - docutils=0.18.1
  - entrypoints=0.4
  - et_xmlfile=1.1.0
  - executing=0.8.3
  - expat=2.6.2
  - filelock=3.13.1
  - flake8=7.0.0
  - flask=3.0.3
  - fmt=9.1.0
  - fonttools=4.51.0
  - freetype=2.12.1
  - frozendict=2.4.2
  - frozenlist=1.4.0
  - fsspec=2024.3.1
  - gensim=4.3.2
  - gflags=2.2.2
  - giflib=5.2.1
  - gitdb=4.0.7
  - gitpython=3.1.37
  - glog=0.5.0
  - greenlet=3.0.1
  - grpc-cpp=1.48.2
  - h11=0.14.0
  - h5py=3.11.0
  - hdf5=1.12.1
  - heapdict=1.0.1
  - holoviews=1.19.0
  - httpcore=1.0.2
  - httpx=0.27.0
  - hvplot=0.10.0
  - hyperlink=21.0.0
  - icc_rt=2022.1.0
  - icu=73.1
  - idna=3.7
  - imagecodecs=2023.1.23
  - imageio=2.33.1
  - imagesize=1.4.1
  - imbalanced-learn=0.12.3
  - importlib-metadata=7.0.1
  - incremental=22.10.0
  - inflection=0.5.1
  - iniconfig=1.1.1
  - intake=0.7.0
  - intel-openmp=2023.1.0
  - intervaltree=3.1.0
  - ipykernel=6.28.0
  - ipython=8.25.0
  - ipython_genutils=0.2.0
  - ipywidgets=7.8.1
  - isort=5.13.2
  - itemadapter=0.3.0
  - itemloaders=1.1.0
  - itsdangerous=2.2.0
  - jaraco.classes=3.2.1
  - jedi=0.18.1
  - jellyfish=1.0.1
  - jinja2=3.1.4
  - jmespath=1.0.1
  - joblib=1.4.2
  - jpeg=9e
  - jq=1.6
  - json5=0.9.6
  - jsonpatch=1.33
  - jsonpointer=2.1
  - jsonschema=4.19.2
  - jsonschema-specifications=2023.7.1
  - jupyter=1.0.0
  - jupyter-lsp=2.2.0
  - jupyter_client=8.6.0
  - jupyter_console=6.6.3
  - jupyter_core=5.7.2
  - jupyter_events=0.10.0
  - jupyter_server=2.14.1
  - jupyter_server_terminals=0.4.4
  - jupyterlab=4.0.11
  - jupyterlab-variableinspector=3.1.0
  - jupyterlab_pygments=0.1.2
  - jupyterlab_server=2.25.1
  - jupyterlab_widgets=1.0.0
  - keyring=24.3.1
  - kiwisolver=1.4.4
  - krb5=1.20.1
  - lazy-object-proxy=1.10.0
  - lazy_loader=0.4
  - lcms2=2.12
  - lerc=3.0
  - libaec=1.0.4
  - libarchive=3.6.2
  - libavif=0.11.1
  - libboost=1.82.0
  - libbrotlicommon=1.0.9
  - libbrotlidec=1.0.9
  - libbrotlienc=1.0.9
  - libclang=14.0.6
  - libclang13=14.0.6
  - libcurl=8.7.1
  - libdeflate=1.17
  - libevent=2.1.12
  - libffi=3.4.4
  - libiconv=1.16
  - liblief=0.12.3
  - libmamba=1.5.8
  - libmambapy=1.5.8
  - libpng=1.6.39
  - libpq=12.17
  - libprotobuf=3.20.3
  - libsodium=1.0.18
  - libsolv=0.7.24
  - libspatialindex=1.9.3
  - libssh2=1.11.0
  - libthrift=0.15.0
  - libtiff=4.5.1
  - libwebp-base=1.3.2
  - libxml2=2.10.4
  - libxslt=1.1.37
  - libzopfli=1.0.3
  - linkify-it-py=2.0.0
  - llvmlite=0.42.0
  - locket=1.0.0
  - lxml=5.2.1
  - lz4=4.3.2
  - lz4-c=1.9.4
  - lzo=2.10
  - m2-msys2-runtime=2.5.0.17080.65c939c
  - m2-patch=2.7.5
  - m2w64-libwinpthread-git=5.0.0.4634.697f757
  - markdown=3.4.1
  - markdown-it-py=2.2.0
  - markupsafe=2.1.3
  - matplotlib=3.8.4
  - matplotlib-base=3.8.4
  - matplotlib-inline=0.1.6
  - mccabe=0.7.0
  - mdit-py-plugins=0.3.0
  - mdurl=0.1.0
  - menuinst=2.1.1
  - mistune=2.0.4
  - mkl=2023.1.0
  - mkl-service=2.4.0
  - mkl_fft=1.3.8
  - mkl_random=1.2.4
  - more-itertools=10.1.0
  - mpmath=1.3.0
  - msgpack-python=1.0.3
  - msys2-conda-epoch=20160418
  - multidict=6.0.4
  - multipledispatch=0.6.0
  - mypy=1.10.0
  - mypy_extensions=1.0.0
  - navigator-updater=0.5.1
  - nbclient=0.8.0
  - nbconvert=7.10.0
  - nbformat=5.9.2
  - nest-asyncio=1.6.0
  - networkx=3.2.1
  - nltk=3.8.1
  - notebook=7.0.8
  - notebook-shim=0.2.3
  - numba=0.59.1
  - numexpr=2.8.7
  - numpy=1.26.4
  - numpy-base=1.26.4
  - numpydoc=1.7.0
  - openjpeg=2.4.0
  - openpyxl=3.1.2
  - openssl=3.0.15
  - orc=1.7.4
  - overrides=7.4.0
  - packaging=23.2
  - pandas=2.2.2
  - pandocfilters=1.5.0
  - panel=1.4.4
  - param=2.1.0
  - paramiko=2.8.1
  - parsel=1.8.1
  - parso=0.8.3
  - partd=1.4.1
  - pathspec=0.10.3
  - patsy=0.5.6
  - pcre2=10.42
  - pexpect=4.8.0
  - pickleshare=0.7.5
  - pillow=10.3.0
  - pip=24.0
  - pkce=1.0.3
  - pkginfo=1.10.0
  - platformdirs=3.10.0
  - plotly=5.22.0
  - pluggy=1.0.0
  - ply=3.11
  - powershell_shortcut=0.0.1
  - prometheus_client=0.14.1
  - prompt-toolkit=3.0.43
  - prompt_toolkit=3.0.43
  - protego=0.1.16
  - protobuf=3.20.3
  - psutil=5.9.0
  - ptyprocess=0.7.0
  - pure_eval=0.2.2
  - py-cpuinfo=9.0.0
  - py-lief=0.12.3
  - pyarrow=14.0.2
  - pyasn1=0.4.8
  - pyasn1-modules=0.2.8
  - pybind11-abi=5
  - pycodestyle=2.11.1
  - pycosat=0.6.6
  - pycparser=2.21
  - pyct=0.5.0
  - pycurl=7.45.2
  - pydantic=2.5.3
  - pydantic-core=2.14.6
  - pydeck=0.8.0
  - pydispatcher=2.0.5
  - pydocstyle=6.3.0
  - pyerfa=*******
  - pyflakes=3.2.0
  - pygments=2.15.1
  - pyjwt=2.8.0
  - pylint=2.16.2
  - pylint-venv=3.0.3
  - pyls-spyder=0.4.0
  - pynacl=1.5.0
  - pyodbc=5.0.1
  - pyopenssl=24.0.0
  - pyparsing=3.0.9
  - pyqt=5.15.10
  - pyqt5-sip=12.13.0
  - pyqtwebengine=5.15.10
  - pysocks=1.7.1
  - pytables=3.9.2
  - pytest=7.4.4
  - python=3.12.4
  - python-dateutil=2.9.0post0
  - python-dotenv=0.21.0
  - python-fastjsonschema=2.16.2
  - python-json-logger=2.0.7
  - python-libarchive-c=2.9
  - python-lmdb=1.4.1
  - python-lsp-black=2.0.0
  - python-lsp-jsonrpc=1.1.2
  - python-lsp-server=1.10.0
  - python-slugify=5.0.2
  - python-snappy=0.6.1
  - python-tzdata=2023.3
  - pytoolconfig=1.2.6
  - pytz=2024.1
  - pyviz_comms=3.0.2
  - pywavelets=1.5.0
  - pywin32=305
  - pywin32-ctypes=0.2.2
  - pywinpty=2.0.10
  - pyyaml=6.0.1
  - pyzmq=25.1.2
  - qdarkstyle=3.2.3
  - qstylizer=0.2.2
  - qt-main=5.15.2
  - qt-webengine=5.15.9
  - qtawesome=1.2.2
  - qtconsole=5.5.1
  - qtpy=2.4.1
  - queuelib=1.6.2
  - re2=2022.04.01
  - referencing=0.30.2
  - regex=2023.10.3
  - reproc=14.2.4
  - reproc-cpp=14.2.4
  - requests=2.32.2
  - requests-file=1.5.1
  - requests-toolbelt=1.0.0
  - rfc3339-validator=0.1.4
  - rfc3986-validator=0.1.1
  - rich=13.3.5
  - rope=1.12.0
  - rpds-py=0.10.6
  - rtree=1.0.1
  - ruamel.yaml=0.17.21
  - ruamel_yaml=0.17.21
  - s3fs=2024.3.1
  - scikit-image=0.23.2
  - scikit-learn=1.4.2
  - scipy=1.13.1
  - scrapy=2.11.1
  - seaborn=0.13.2
  - semver=3.0.2
  - send2trash=1.8.2
  - service_identity=18.1.0
  - setuptools=69.5.1
  - sip=6.7.12
  - six=1.16.0
  - smart_open=5.2.1
  - smmap=4.0.0
  - snappy=1.1.10
  - sniffio=1.3.0
  - snowballstemmer=2.2.0
  - sortedcontainers=2.4.0
  - soupsieve=2.5
  - sphinx=7.3.7
  - sphinxcontrib-applehelp=1.0.2
  - sphinxcontrib-devhelp=1.0.2
  - sphinxcontrib-htmlhelp=2.0.0
  - sphinxcontrib-jsmath=1.0.1
  - sphinxcontrib-qthelp=1.0.3
  - sphinxcontrib-serializinghtml=1.1.10
  - spyder=5.5.1
  - spyder-kernels=2.5.0
  - sqlalchemy=2.0.30
  - sqlite=3.45.3
  - stack_data=0.2.0
  - statsmodels=0.14.2
  - streamlit=1.32.0
  - sympy=1.12
  - tabulate=0.9.0
  - tbb=2021.8.0
  - tblib=1.7.0
  - tenacity=8.2.2
  - terminado=0.17.1
  - text-unidecode=1.3
  - textdistance=4.2.1
  - threadpoolctl=2.2.0
  - three-merge=0.1.1
  - tifffile=2023.4.12
  - tinycss2=1.2.1
  - tk=8.6.14
  - tldextract=3.2.0
  - toml=0.10.2
  - tomli=2.0.1
  - tomlkit=0.11.1
  - toolz=0.12.0
  - tornado=6.4.1
  - tqdm=4.66.4
  - traitlets=5.14.3
  - truststore=0.8.0
  - twisted=23.10.0
  - twisted-iocpsupport=1.0.2
  - typing-extensions=4.11.0
  - typing_extensions=4.11.0
  - tzdata=2024a
  - uc-micro-py=1.0.1
  - ujson=5.10.0
  - unicodedata2=15.1.0
  - unidecode=1.2.0
  - urllib3=2.2.2
  - utf8proc=2.6.1
  - vc=14.2
  - vs2015_runtime=14.29.30133
  - w3lib=2.1.2
  - watchdog=4.0.1
  - wcwidth=0.2.5
  - webencodings=0.5.1
  - websocket-client=1.8.0
  - werkzeug=3.0.3
  - whatthepatch=1.0.2
  - wheel=0.43.0
  - widgetsnbextension=3.6.6
  - win_inet_pton=1.1.0
  - winpty=0.4.3
  - wrapt=1.14.1
  - xarray=2023.6.0
  - xlwings=0.31.4
  - xyzservices=2022.9.0
  - xz=5.4.6
  - yaml=0.2.5
  - yaml-cpp=0.8.0
  - yapf=0.40.2
  - yarl=1.9.3
  - zeromq=4.3.5
  - zfp=1.0.0
  - zict=3.0.0
  - zipp=3.17.0
  - zlib=1.2.13
  - zlib-ng=2.0.7
  - zope=1.0
  - zope.interface=5.4.0
  - zstandard=0.22.0
  - zstd=1.5.5
