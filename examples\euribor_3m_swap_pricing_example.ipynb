{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["['c:\\\\ProgramData\\\\anaconda3\\\\python312.zip',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\DLLs',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib',\n", " 'c:\\\\ProgramData\\\\anaconda3',\n", " '',\n", " 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python312\\\\site-packages',\n", " 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python312\\\\site-packages\\\\win32',\n", " 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python312\\\\site-packages\\\\win32\\\\lib',\n", " 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Python\\\\Python312\\\\site-packages\\\\Pythonwin',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\win32',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\win32\\\\lib',\n", " 'c:\\\\ProgramData\\\\anaconda3\\\\Lib\\\\site-packages\\\\Pythonwin']"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "investment_parent_path = os.path.abspath(os.path.join(os.getcwd(), \"..\"))\n", "sys.path.append(investment_parent_path)\n", "import pandas as pd\n", "import datetime as dt\n", "import QuantLib as ql\n", "from curves import estrCurve, euribor3MCurve\n", "from loader.curve_data_loader import curveBenchmarkLoader\n", "from helpers.plot_tools import plot_curves"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from pricer.swap import iborSwapPricer\n", "euribor_pricer = iborSwapPricer(\"EUR.EURIBOR.6M\", debug_mode=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-08, 2024-01-15]\n", "Loading close price from 2024-01-01 to 2024-01-15\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_6M_20240101_marketclose.parquet\n", "Swap start date: 2024-01-10 Swap maturity date: 2025-01-10\n", "Populate fixings data: EUR006M Index [datetime.date(2024, 1, 8), datetime.date(2024, 7, 8)] < 2024-01-15\n", "Swap Notional: -9,828,208.737810953\n"]}, {"data": {"text/plain": ["2024-01-08    83616.839068\n", "2024-01-09    86129.522425\n", "2024-01-10    88085.596455\n", "2024-01-11    83230.543286\n", "2024-01-12    79860.863503\n", "2024-01-15    83436.118568\n", "dtype: float64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["euribor_pricer.NPV(pricingStartDate=dt.date(2024,1,8), pricingEndDate=dt.date(2024,1,15), receiveFixed=True, fixedRate=2.682/100, swapTenor=\"1Y\", tradeDate=dt.date(2024,1,8), nominal=1e7, dv01=494.75)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-08, 2024-01-15]\n", "Loading close price from 2024-01-01 to 2024-01-15\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_6M_20240101_marketclose.parquet\n", "Swap start date: 2024-01-10 Swap maturity date: 2025-01-10\n", "Populate fixings data: EUR006M Index [datetime.date(2024, 1, 8), datetime.date(2024, 7, 8)] < 2024-01-15\n", "Swap Notional: -9,828,208.737810953\n"]}, {"data": {"text/plain": ["2024-01-08    83616.839068\n", "2024-01-09    86129.522425\n", "2024-01-10    88085.596455\n", "2024-01-11    83230.543286\n", "2024-01-12    79860.863503\n", "2024-01-15    83436.118568\n", "dtype: float64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["euribor_pricer.NPV(pricingStartDate=dt.date(2024,1,8), pricingEndDate=dt.date(2024,1,15), receiveFixed=True, fixedRate=2.682/100, swapTenor=\"1Y\", tradeDate=dt.date(2024,1,8), nominal=1e7, dv01=494.75)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading close price for period [2024-01-08, 2024-01-15]\n", "Loading close price from 2024-01-01 to 2024-01-15\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_6M_20240101_marketclose.parquet\n", "Swap start date: 2024-01-10 Swap maturity date: 2027-01-11\n", "EUR006M Index [datetime.date(2024, 1, 8), datetime.date(2024, 7, 8), datetime.date(2025, 1, 8), datetime.date(2025, 7, 8), datetime.date(2026, 1, 8), datetime.date(2026, 7, 8)] 2024-01-15\n"]}, {"data": {"text/plain": ["2024-01-08       -0.000005\n", "2024-01-09   -19265.728597\n", "2024-01-10   -26134.944321\n", "2024-01-11   -22076.633776\n", "2024-01-12     3593.234843\n", "2024-01-15    -7594.363486\n", "dtype: float64"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["euribor_pricer.NPV(pricingStartDate=dt.date(2024,1,8), pricingEndDate=dt.date(2024,1,15), receiveFixed=True, fixedRate=2.682/100, swapTenor=\"3Y\", tradeDate=dt.date(2024,1,8), nominal=10e6)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["loader = curveBenchmarkLoader(\"EUR.EURIBOR.3M\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NB: Loading index fixing on 2024-12-10, lag: 1d\n"]}], "source": ["b, d = loader.load_close_px(dt.date(2024,12,11))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>instrument_type</th>\n", "      <th>maturities</th>\n", "      <th>ticker</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Deposit</td>\n", "      <td>3M</td>\n", "      <td>EUR003M Index</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Future</td>\n", "      <td>c1</td>\n", "      <td>ER1 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Future</td>\n", "      <td>c2</td>\n", "      <td>ER2 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Future</td>\n", "      <td>c3</td>\n", "      <td>ER3 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Future</td>\n", "      <td>c4</td>\n", "      <td>ER4 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Future</td>\n", "      <td>c5</td>\n", "      <td>ER5 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Future</td>\n", "      <td>c6</td>\n", "      <td>ER6 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Future</td>\n", "      <td>c7</td>\n", "      <td>ER7 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Future</td>\n", "      <td>c8</td>\n", "      <td>ER8 Comdty</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>VanillaSwap</td>\n", "      <td>4Y</td>\n", "      <td>EUSW4V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>VanillaSwap</td>\n", "      <td>5Y</td>\n", "      <td>EUSW5V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>VanillaSwap</td>\n", "      <td>6Y</td>\n", "      <td>EUSW6V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>VanillaSwap</td>\n", "      <td>7Y</td>\n", "      <td>EUSW7V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>VanillaSwap</td>\n", "      <td>8Y</td>\n", "      <td>EUSW8V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>VanillaSwap</td>\n", "      <td>9Y</td>\n", "      <td>EUSW9V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>VanillaSwap</td>\n", "      <td>10Y</td>\n", "      <td>EUSW10V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>VanillaSwap</td>\n", "      <td>11Y</td>\n", "      <td>EUSW11V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>VanillaSwap</td>\n", "      <td>12Y</td>\n", "      <td>EUSW12V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>VanillaSwap</td>\n", "      <td>15Y</td>\n", "      <td>EUSW15V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>VanillaSwap</td>\n", "      <td>20Y</td>\n", "      <td>EUSW20V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>VanillaSwap</td>\n", "      <td>25Y</td>\n", "      <td>EUSW25V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>VanillaSwap</td>\n", "      <td>30Y</td>\n", "      <td>EUSW30V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>VanillaSwap</td>\n", "      <td>40Y</td>\n", "      <td>EUSW40V3 Curncy</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>VanillaSwap</td>\n", "      <td>50Y</td>\n", "      <td>EUSW50V3 Curncy</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   instrument_type maturities           ticker\n", "0          Deposit         3M    EUR003M Index\n", "1           Future         c1       ER1 Comdty\n", "2           Future         c2       ER2 Comdty\n", "3           Future         c3       ER3 Comdty\n", "4           Future         c4       ER4 Comdty\n", "5           Future         c5       ER5 Comdty\n", "6           Future         c6       ER6 Comdty\n", "7           Future         c7       ER7 Comdty\n", "8           Future         c8       ER8 Comdty\n", "9      VanillaSwap         4Y   EUSW4V3 Curncy\n", "10     VanillaSwap         5Y   EUSW5V3 Curncy\n", "11     VanillaSwap         6Y   EUSW6V3 Curncy\n", "12     VanillaSwap         7Y   EUSW7V3 Curncy\n", "13     VanillaSwap         8Y   EUSW8V3 Curncy\n", "14     VanillaSwap         9Y   EUSW9V3 Curncy\n", "15     VanillaSwap        10Y  EUSW10V3 Curncy\n", "16     VanillaSwap        11Y  EUSW11V3 Curncy\n", "17     VanillaSwap        12Y  EUSW12V3 Curncy\n", "18     VanillaSwap        15Y  EUSW15V3 Curncy\n", "19     VanillaSwap        20Y  EUSW20V3 Curncy\n", "20     VanillaSwap        25Y  EUSW25V3 Curncy\n", "21     VanillaSwap        30Y  EUSW30V3 Curncy\n", "22     VanillaSwap        40Y  EUSW40V3 Curncy\n", "23     VanillaSwap        50Y  EUSW50V3 Curncy"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["\"c1\" in b[\"maturities\"].values"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["347"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["abs(ql.Date(1,1,2024) - ql.Date.todaysDate())"]}, {"cell_type": "code", "execution_count": 301, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Start calibration of EUR_ESTR on 2024-12-10, Interp method: PiecewiseLogMixedLinearCubicDiscount...\n", "NB: Loading index fixing on 2024-12-09, lag: 1d\n", "OIS term: 1D , Maturity: 2024-12-11, Quote: 3.1640, Pillar date: 2024-12-18... NB: Proxy with Quote: 3.1650\n", "OIS term: 18M, Maturity: 2026-06-12, Quote: 1.9564, Pillar date: 2026-06-12\n", "OIS term: 2Y , Maturity: 2026-12-14, Quote: 1.8884, Pillar date: 2026-12-14\n", "OIS term: 3Y , Maturity: 2027-12-13, Quote: 1.8496, Pillar date: 2027-12-13\n", "OIS term: 4Y , Maturity: 2028-12-12, Quote: 1.8521, Pillar date: 2028-12-12\n", "OIS term: 5Y , Maturity: 2029-12-12, Quote: 1.8670, Pillar date: 2029-12-12\n", "OIS term: 6Y , Maturity: 2030-12-12, Quote: 1.8898, Pillar date: 2030-12-12\n", "OIS term: 7Y , Maturity: 2031-12-12, Quote: 1.9168, Pillar date: 2031-12-12\n", "OIS term: 8Y , Maturity: 2032-12-13, Quote: 1.9448, Pillar date: 2032-12-13\n", "OIS term: 9Y , Maturity: 2033-12-12, Quote: 1.9742, Pillar date: 2033-12-12\n", "OIS term: 10Y, Maturity: 2034-12-12, Quote: 2.0044, Pillar date: 2034-12-12\n", "OIS term: 11Y, Maturity: 2035-12-12, Quote: 2.0314, Pillar date: 2035-12-12\n", "OIS term: 12Y, Maturity: 2036-12-12, Quote: 2.0575, Pillar date: 2036-12-12\n", "OIS term: 15Y, Maturity: 2039-12-12, Quote: 2.1150, Pillar date: 2039-12-12\n", "OIS term: 20Y, Maturity: 2044-12-12, Quote: 2.0987, Pillar date: 2044-12-12\n", "OIS term: 25Y, Maturity: 2049-12-13, Quote: 2.0270, Pillar date: 2049-12-13\n", "OIS term: 30Y, Maturity: 2054-12-14, Quote: 1.9540, Pillar date: 2054-12-14\n", "OIS term: 40Y, Maturity: 2064-12-12, Quote: 1.8288, Pillar date: 2064-12-12\n", "OIS term: 50Y, Maturity: 2074-12-12, Quote: 1.7020, Pillar date: 2074-12-12\n", "OIS term: 60Y, Maturity: 2084-12-12, Quote: 1.5980, Pillar date: 2084-12-12\n", "Meeting OIS term s1 2024-12-18 - 2025-02-05, Maturity: 2025-02-05, Quote: 2.9075, Pillar date: 2025-02-05\n", "Meeting OIS term s2 2025-02-05 - 2025-03-12, Maturity: 2025-03-12, Quote: 2.5915, Pillar date: 2025-03-12\n", "Meeting OIS term s3 2025-03-12 - 2025-04-23, Maturity: 2025-04-23, Quote: 2.2579, Pillar date: 2025-04-23\n", "Meeting OIS term s4 2025-04-23 - 2025-06-11, Maturity: 2025-06-11, Quote: 2.0101, Pillar date: 2025-06-11\n", "Meeting OIS term s5 2025-06-11 - 2025-07-30, Maturity: 2025-07-30, Quote: 1.8371, Pillar date: 2025-07-30\n", "Meeting OIS term s6 2025-07-30 - 2025-09-17, Maturity: 2025-09-17, Quote: 1.7536, Pillar date: 2025-09-17\n", "Meeting OIS term s7 2025-09-17 - 2025-11-05, Maturity: 2025-11-05, Quote: 1.6900, Pillar date: 2025-11-05\n", "Finish calibration of EUR_ESTR on 2024-12-10.\n", "\n", "Loading close price for period [2024-12-10, 2024-12-10]\n", "Loading close price from 2024-01-01 to 2024-12-10\n", "Opening curve benchmark data from bbg: c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_3M_20240101_marketclose.parquet\n", "NB: Loading index fixing on 2024-12-09, lag: 1d\n", "\n", "Start calibration of EUR_EURIBOR_3M on 2024-12-10, Interp method: PiecewiseLogCubicDiscount...\n", "Deposit term: 3M, Maturity: 2025-03-11 Quote: 2.8620, Pillar date: 2025-03-11...\n", "Future term c1 2024-12-18 - 2025-03-19, Quote: 97.1700, Pillar date: 2025-03-19... convexity: 0.000000%\n", "Future term c2 2025-03-19 - 2025-06-18, Quote: 97.7950, Pillar date: 2025-06-18... convexity: 0.000000%\n", "Future term c3 2025-06-18 - 2025-09-17, Quote: 98.0800, Pillar date: 2025-09-17... convexity: 0.000000%\n", "Future term c4 2025-09-17 - 2025-12-17, Quote: 98.1900, Pillar date: 2025-12-17... convexity: 0.000000%\n", "Future term c5 2025-12-17 - 2026-03-18, Quote: 98.2300, Pillar date: 2026-03-18... convexity: 0.000000%\n", "Future term c6 2026-03-18 - 2026-06-17, Quote: 98.2250, Pillar date: 2026-06-17... convexity: 0.000000%\n", "Future term c7 2026-06-17 - 2026-09-16, Quote: 98.2050, Pillar date: 2026-09-16... convexity: 0.000000%\n", "Future term c8 2026-09-16 - 2026-12-16, Quote: 98.1800, Pillar date: 2026-12-16... convexity: 0.000000%\n", "Vanilla swap term: 3Y , Maturity: 2027-12-13, Quote: 1.9975, Pillar date: 2027-12-13\n", "Vanilla swap term: 4Y , Maturity: 2028-12-12, Quote: 1.9983, Pillar date: 2028-12-12\n", "Vanilla swap term: 5Y , Maturity: 2029-12-12, Quote: 2.0095, Pillar date: 2029-12-12\n", "Vanilla swap term: 6Y , Maturity: 2030-12-12, Quote: 2.0287, Pillar date: 2030-12-12\n", "Vanilla swap term: 7Y , Maturity: 2031-12-12, Quote: 2.0512, Pillar date: 2031-12-12\n", "Vanilla swap term: 8Y , Maturity: 2032-12-13, Quote: 2.0765, Pillar date: 2032-12-13\n", "Vanilla swap term: 9Y , Maturity: 2033-12-12, Quote: 2.1030, Pillar date: 2033-12-12\n", "Vanilla swap term: 10Y, Maturity: 2034-12-12, Quote: 2.1302, Pillar date: 2034-12-12\n", "Vanilla swap term: 11Y, Maturity: 2035-12-12, Quote: 2.1545, Pillar date: 2035-12-12\n", "Vanilla swap term: 12Y, Maturity: 2036-12-12, Quote: 2.1785, Pillar date: 2036-12-12\n", "Vanilla swap term: 15Y, Maturity: 2039-12-12, Quote: 2.2270, Pillar date: 2039-12-12\n", "Vanilla swap term: 20Y, Maturity: 2044-12-12, Quote: 2.2007, Pillar date: 2044-12-12\n", "Vanilla swap term: 25Y, Maturity: 2049-12-13, Quote: 2.1155, Pillar date: 2049-12-13\n", "Vanilla swap term: 30Y, Maturity: 2054-12-14, Quote: 2.0310, Pillar date: 2054-12-14\n", "Vanilla swap term: 40Y, Maturity: 2064-12-12, Quote: 1.8911, Pillar date: 2064-12-12\n", "Vanilla swap term: 50Y, Maturity: 2074-12-12, Quote: 1.7527, Pillar date: 2074-12-12\n", "Finish calibration of EUR_EURIBOR_3M on 2024-12-10.\n", "\n", "Vanilla swap term: 6M , Maturity: 2025-06-12, Quote: 2.5920, Pillar date: 2025-06-12\n", "Vanilla swap term: 1Y , Maturity: 2025-12-12, Quote: 2.2578, Pillar date: 2025-12-12\n", "Vanilla swap term: 18M, Maturity: 2026-06-12, Quote: 2.1003, Pillar date: 2026-06-12\n", "Vanilla swap term: 2Y , Maturity: 2026-12-14, Quote: 2.0368, Pillar date: 2026-12-14\n", "0.025920000000000002\n", "0.022578\n", "0.021002999999999997\n", "0.020368\n"]}], "source": ["euribor_curve = euribor3MCurve(\"20241210\", debug_mode=True)\n", "swaps = euribor_curve.set_up_helpers()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["2.8300000000044414"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["100 * euribor_curve.curve.forwardRate(ql.Date(18,12,2024), ql.Date(19,3,2025), euribor_curve.index.dayCounter(), ql.Simple).rate()"]}, {"cell_type": "code", "execution_count": 302, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Floating Leg NPV: -12767.98070873049  > Fixed Leg NPV: 12797.94602683496\n", "Floating Leg NPV: -22101.20865304643  > Fixed Leg NPV: 22100.338297975646\n", "Floating Leg NPV: -30818.15410880994  > Fixed Leg NPV: 30759.91910427095\n", "Floating Leg NPV: -39773.87180312173  > Fixed Leg NPV: 39651.281677689105\n"]}], "source": ["for swap in swaps:\n", "    print(\"Floating Leg NPV:\", swap.floatingLegNPV(), \" > Fixed Leg NPV:\", swap.fixedLegNPV())"]}, {"cell_type": "code", "execution_count": 272, "metadata": {}, "outputs": [], "source": ["ql.Settings.instance().evaluationDate = ql.Date.from_date(euribor_curve.date)\n", "yts = ql.RelinkableYieldTermStructureHandle()\n", "euribor_3m_index = ql.Euribor3M(yts)\n", "yts_discount = ql.RelinkableYieldTermStructureHandle()\n", "engine = ql.DiscountingSwapEngine(yts_discount)\n", "swap = ql.MakeVanillaSwap(ql.Period(\"1y\"), euribor_3m_index, 2.2578/100, ql.Period(), nominal=1e6, receiveFixed=True, fixedLegDayCount=ql.Thirty360(ql.Thirty360.USA), pricingEngine=engine)"]}, {"cell_type": "code", "execution_count": 273, "metadata": {}, "outputs": [], "source": ["yts.linkTo(euribor_curve.curve)\n", "yts_discount.linkTo(euribor_curve.ois_curve.curve)"]}, {"cell_type": "code", "execution_count": 327, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.02867929420501536)"]}, "execution_count": 327, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([{\n", "    'fixingDate': cf.fixingDate().ISO(),\n", "    'accrualStart': cf.accrualStartDate().ISO(),\n", "    'accrualEnd': cf.accrualEndDate().ISO(),\n", "    \"paymentDate\": cf.date().ISO(),\n", "    'gearing': cf.gearing(),\n", "    'forward': cf.indexFixing(),\n", "    'rate': cf.rate(),\n", "    \"amount\": cf.amount()\n", "} for cf in map(ql.as_floating_rate_coupon, swap.floatingLeg())])[\"rate\"].max()"]}, {"cell_type": "code", "execution_count": 276, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>accrualStart</th>\n", "      <th>accrualEnd</th>\n", "      <th>paymentDate</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-12-12</td>\n", "      <td>2025-12-12</td>\n", "      <td>2025-12-12</td>\n", "      <td>22578.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  accrualStart  accrualEnd paymentDate   amount\n", "0   2024-12-12  2025-12-12  2025-12-12  22578.0"]}, "execution_count": 276, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame([{\n", "    'accrualStart': cf.accrualStartDate().ISO(),\n", "    'accrualEnd': cf.accrualEndDate().ISO(),\n", "    \"paymentDate\": cf.date().ISO(),\n", "    \"amount\": cf.amount()\n", "} for cf in map(ql.as_fixed_rate_coupon, swap.fixedLeg())])"]}, {"cell_type": "code", "execution_count": 277, "metadata": {}, "outputs": [{"data": {"text/plain": ["-0.8703550707832619"]}, "execution_count": 277, "metadata": {}, "output_type": "execute_result"}], "source": ["swap.NPV()"]}, {"cell_type": "code", "execution_count": 279, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.022578889166334163"]}, "execution_count": 279, "metadata": {}, "output_type": "execute_result"}], "source": ["swap.fairRate()"]}, {"cell_type": "code", "execution_count": 280, "metadata": {}, "outputs": [{"data": {"text/plain": ["-22101.20865304643"]}, "execution_count": 280, "metadata": {}, "output_type": "execute_result"}], "source": ["swap.floatingLegNPV()"]}, {"cell_type": "code", "execution_count": 281, "metadata": {}, "outputs": [{"data": {"text/plain": ["22100.338297975646"]}, "execution_count": 281, "metadata": {}, "output_type": "execute_result"}], "source": ["swap.fixedLegNPV()"]}, {"cell_type": "code", "execution_count": 283, "metadata": {}, "outputs": [], "source": ["swap_2y = ql.MakeVanillaSwap(ql.Period(\"2y\"), euribor_3m_index, 2.0368/100, ql.Period(), nominal=1e6, receiveFixed=True, fixedLegDayCount=ql.Thirty360(ql.Thirty360.USA), pricingEngine=engine)"]}, {"cell_type": "code", "execution_count": 285, "metadata": {}, "outputs": [{"data": {"text/plain": ["-122.59012543262361"]}, "execution_count": 285, "metadata": {}, "output_type": "execute_result"}], "source": ["swap_2y.NPV()"]}, {"cell_type": "code", "execution_count": 286, "metadata": {}, "outputs": [{"data": {"text/plain": ["-39773.87180312173"]}, "execution_count": 286, "metadata": {}, "output_type": "execute_result"}], "source": ["swap_2y.floatingLegNPV()"]}, {"cell_type": "code", "execution_count": 287, "metadata": {}, "outputs": [{"data": {"text/plain": ["39651.281677689105"]}, "execution_count": 287, "metadata": {}, "output_type": "execute_result"}], "source": ["swap_2y.fixedLegNPV()"]}, {"cell_type": "code", "execution_count": 288, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-12-12 2025-03-12 0.0285811368182749 0.0285811368182749 7145.284204568725\n", "2025-03-12 2025-06-12 0.022482711775412136 0.022482711775412136 5745.581898160879\n", "2025-06-12 2025-09-12 0.019283462965931378 0.019283462965931378 4927.996091293574\n", "2025-09-12 2025-12-12 0.018143006443738525 0.018143006443738525 4586.148851056127\n", "2025-12-12 2026-03-12 0.01770669143304371 0.01770669143304371 4426.672858260928\n", "2026-03-12 2026-06-12 0.017741416702633083 0.017741416702633083 4533.91760178401\n", "2026-06-12 2026-09-14 0.01794219536404273 0.01794219536404273 4684.906567277824\n", "2026-09-14 2026-12-14 0.018195314790837594 0.018195314790837594 4599.371238795058\n"]}], "source": ["for cf in map(ql.as_floating_rate_coupon, swap_2y.floatingLeg()):\n", "    print(cf.accrualStartDate().ISO(), cf.accrualEndDate().ISO(), cf.indexFixing(), cf.rate(), cf.amount())"]}, {"cell_type": "code", "execution_count": 289, "metadata": {}, "outputs": [{"data": {"text/plain": ["39773.87180312173"]}, "execution_count": 289, "metadata": {}, "output_type": "execute_result"}], "source": ["floatLegNPV = 0\n", "for cf in map(ql.as_floating_rate_coupon, swap_2y.floatingLeg()):\n", "    floatLegNPV += cf.amount() * euribor_curve.ois_curve.curve.discount(cf.date())\n", "floatLegNPV"]}, {"cell_type": "code", "execution_count": 290, "metadata": {}, "outputs": [], "source": ["def objective(variance):\n", "    floatLegNPV = 0\n", "    for cf in map(ql.as_floating_rate_coupon, swap_2y.floatingLeg()):\n", "        T1 = euribor_3m_index.dayCounter().yearFraction(ql.Date.from_date(euribor_curve.date), cf.accrualStartDate())\n", "        T2 = euribor_3m_index.dayCounter().yearFraction(ql.Date.from_date(euribor_curve.date), cf.accrualEndDate())\n", "        convexity = (variance / 2 * (2 * (T2-T1) * T1 + T1**2)) / 10000**2\n", "        floatLegNPV +=  cf.amount() / cf.rate() * (cf.rate() - convexity) * euribor_curve.ois_curve.curve.discount(cf.date())\n", "    print(\"float NPV:\", floatLegNPV)\n", "    return (floatLegNPV - swap_2y.fixedLegNPV())**2"]}, {"cell_type": "code", "execution_count": 291, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["float NPV: 39773.87180312173\n"]}, {"data": {"text/plain": ["15028.33885358639"]}, "execution_count": 291, "metadata": {}, "output_type": "execute_result"}], "source": ["objective(0)"]}, {"cell_type": "code", "execution_count": 292, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["float NPV: 39648.07513810096\n"]}, {"data": {"text/plain": ["10.281896130332395"]}, "execution_count": 292, "metadata": {}, "output_type": "execute_result"}], "source": ["objective(8000)"]}, {"cell_type": "code", "execution_count": 293, "metadata": {}, "outputs": [], "source": ["from scipy.optimize import minimize"]}, {"cell_type": "code", "execution_count": 294, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["float NPV: [39734.5603453]\n", "float NPV: [39734.5603453]\n", "float NPV: [39734.54462072]\n", "float NPV: [39734.54462072]\n", "float NPV: [39734.52032806]\n", "float NPV: [39734.52032806]\n", "float NPV: [39641.25014451]\n", "float NPV: [39641.25014451]\n", "float NPV: [39651.70469016]\n", "float NPV: [39651.70469016]\n", "float NPV: [39651.28164009]\n", "float NPV: [39651.28164009]\n"]}], "source": ["result = minimize(objective, x0 = 50**2, bounds=[(0, None)])"]}, {"cell_type": "code", "execution_count": 295, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([7796.08349791])"]}, "execution_count": 295, "metadata": {}, "output_type": "execute_result"}], "source": ["result.x"]}, {"cell_type": "code", "execution_count": 297, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_values([1.4137525310854912e-09, array([1.14903068e-06]), 12, 6, 4, 0, 'CONVERGENCE: NORM_OF_PROJECTED_GRADIENT_<=_PGTOL', array([7796.08349791]), True, <1x1 LbfgsInvHessProduct with dtype=float64>])"]}, "execution_count": 297, "metadata": {}, "output_type": "execute_result"}], "source": ["result.values()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EUR003M Index</th>\n", "      <th>ER1 Comdty</th>\n", "      <th>ER2 Comdty</th>\n", "      <th>ER3 Comdty</th>\n", "      <th>ER4 Comdty</th>\n", "      <th>EUSW1VC Curncy</th>\n", "      <th>EUSWV31F Curncy</th>\n", "      <th>EUSW2V3 Curncy</th>\n", "      <th>EUSW3V3 Curncy</th>\n", "      <th>EUSW4V3 Curncy</th>\n", "      <th>...</th>\n", "      <th>EUSW20V3 Curncy</th>\n", "      <th>EUSW25V3 Curncy</th>\n", "      <th>EUSW30V3 Curncy</th>\n", "      <th>EUSW40V3 Curncy</th>\n", "      <th>EUSW50V3 Curncy</th>\n", "      <th>ER5 Comdty</th>\n", "      <th>ER6 Comdty</th>\n", "      <th>ER7 Comdty</th>\n", "      <th>ER8 Comdty</th>\n", "      <th>EUSWFVC Curncy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-02</th>\n", "      <td>3.905</td>\n", "      <td>96.360</td>\n", "      <td>96.870</td>\n", "      <td>97.335</td>\n", "      <td>97.675</td>\n", "      <td>3.3700</td>\n", "      <td>2.9800</td>\n", "      <td>2.7455</td>\n", "      <td>2.5060</td>\n", "      <td>2.4190</td>\n", "      <td>...</td>\n", "      <td>2.6033</td>\n", "      <td>2.5255</td>\n", "      <td>2.4497</td>\n", "      <td>2.3252</td>\n", "      <td>2.2150</td>\n", "      <td>97.905</td>\n", "      <td>98.040</td>\n", "      <td>98.080</td>\n", "      <td>98.070</td>\n", "      <td>3.8050</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-03</th>\n", "      <td>3.929</td>\n", "      <td>96.360</td>\n", "      <td>96.880</td>\n", "      <td>97.350</td>\n", "      <td>97.700</td>\n", "      <td>3.3530</td>\n", "      <td>2.9451</td>\n", "      <td>2.6962</td>\n", "      <td>2.4495</td>\n", "      <td>2.3560</td>\n", "      <td>...</td>\n", "      <td>2.5505</td>\n", "      <td>2.4725</td>\n", "      <td>2.3950</td>\n", "      <td>2.2810</td>\n", "      <td>2.1704</td>\n", "      <td>97.940</td>\n", "      <td>98.075</td>\n", "      <td>98.120</td>\n", "      <td>98.115</td>\n", "      <td>3.7990</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-04</th>\n", "      <td>3.922</td>\n", "      <td>96.320</td>\n", "      <td>96.800</td>\n", "      <td>97.235</td>\n", "      <td>97.570</td>\n", "      <td>3.4240</td>\n", "      <td>3.0443</td>\n", "      <td>2.8190</td>\n", "      <td>2.5790</td>\n", "      <td>2.4925</td>\n", "      <td>...</td>\n", "      <td>2.6370</td>\n", "      <td>2.5645</td>\n", "      <td>2.4830</td>\n", "      <td>2.3403</td>\n", "      <td>2.2290</td>\n", "      <td>97.805</td>\n", "      <td>97.940</td>\n", "      <td>97.990</td>\n", "      <td>97.990</td>\n", "      <td>3.8180</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>3.936</td>\n", "      <td>96.310</td>\n", "      <td>96.770</td>\n", "      <td>97.195</td>\n", "      <td>97.530</td>\n", "      <td>3.4500</td>\n", "      <td>3.0816</td>\n", "      <td>2.8700</td>\n", "      <td>2.6350</td>\n", "      <td>2.5425</td>\n", "      <td>...</td>\n", "      <td>2.6690</td>\n", "      <td>2.5913</td>\n", "      <td>2.5090</td>\n", "      <td>2.3640</td>\n", "      <td>2.2440</td>\n", "      <td>97.755</td>\n", "      <td>97.885</td>\n", "      <td>97.935</td>\n", "      <td>97.940</td>\n", "      <td>3.8450</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-08</th>\n", "      <td>3.925</td>\n", "      <td>96.300</td>\n", "      <td>96.760</td>\n", "      <td>97.190</td>\n", "      <td>97.530</td>\n", "      <td>3.4230</td>\n", "      <td>3.0492</td>\n", "      <td>2.8210</td>\n", "      <td>2.5905</td>\n", "      <td>2.5065</td>\n", "      <td>...</td>\n", "      <td>2.6480</td>\n", "      <td>2.5697</td>\n", "      <td>2.4862</td>\n", "      <td>2.3447</td>\n", "      <td>2.2210</td>\n", "      <td>97.760</td>\n", "      <td>97.895</td>\n", "      <td>97.945</td>\n", "      <td>97.950</td>\n", "      <td>3.8230</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-05</th>\n", "      <td>2.881</td>\n", "      <td>97.170</td>\n", "      <td>97.765</td>\n", "      <td>98.055</td>\n", "      <td>98.145</td>\n", "      <td>2.2910</td>\n", "      <td>2.1455</td>\n", "      <td>2.0870</td>\n", "      <td>2.0455</td>\n", "      <td>2.0395</td>\n", "      <td>...</td>\n", "      <td>2.1740</td>\n", "      <td>2.0845</td>\n", "      <td>1.9935</td>\n", "      <td>1.8398</td>\n", "      <td>1.6899</td>\n", "      <td>98.170</td>\n", "      <td>98.165</td>\n", "      <td>98.145</td>\n", "      <td>98.120</td>\n", "      <td>2.6140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-06</th>\n", "      <td>2.868</td>\n", "      <td>97.170</td>\n", "      <td>97.755</td>\n", "      <td>98.035</td>\n", "      <td>98.135</td>\n", "      <td>2.2880</td>\n", "      <td>2.1335</td>\n", "      <td>2.0720</td>\n", "      <td>2.0255</td>\n", "      <td>2.0199</td>\n", "      <td>...</td>\n", "      <td>2.1775</td>\n", "      <td>2.0864</td>\n", "      <td>1.9975</td>\n", "      <td>1.8455</td>\n", "      <td>1.6993</td>\n", "      <td>98.175</td>\n", "      <td>98.175</td>\n", "      <td>98.160</td>\n", "      <td>98.140</td>\n", "      <td>2.6135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-09</th>\n", "      <td>2.862</td>\n", "      <td>97.175</td>\n", "      <td>97.785</td>\n", "      <td>98.060</td>\n", "      <td>98.165</td>\n", "      <td>2.2717</td>\n", "      <td>2.1193</td>\n", "      <td>2.0582</td>\n", "      <td>2.0157</td>\n", "      <td>2.0121</td>\n", "      <td>...</td>\n", "      <td>2.1897</td>\n", "      <td>2.1028</td>\n", "      <td>2.0165</td>\n", "      <td>1.8733</td>\n", "      <td>1.7320</td>\n", "      <td>98.205</td>\n", "      <td>98.205</td>\n", "      <td>98.190</td>\n", "      <td>98.165</td>\n", "      <td>2.5980</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-10</th>\n", "      <td>2.872</td>\n", "      <td>97.170</td>\n", "      <td>97.795</td>\n", "      <td>98.080</td>\n", "      <td>98.190</td>\n", "      <td>2.2578</td>\n", "      <td>2.1003</td>\n", "      <td>2.0368</td>\n", "      <td>1.9975</td>\n", "      <td>1.9983</td>\n", "      <td>...</td>\n", "      <td>2.2007</td>\n", "      <td>2.1155</td>\n", "      <td>2.0310</td>\n", "      <td>1.8911</td>\n", "      <td>1.7527</td>\n", "      <td>98.230</td>\n", "      <td>98.225</td>\n", "      <td>98.205</td>\n", "      <td>98.180</td>\n", "      <td>2.5920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-11</th>\n", "      <td>2.888</td>\n", "      <td>97.160</td>\n", "      <td>97.795</td>\n", "      <td>98.090</td>\n", "      <td>98.205</td>\n", "      <td>2.2538</td>\n", "      <td>2.0970</td>\n", "      <td>2.0340</td>\n", "      <td>1.9948</td>\n", "      <td>2.0025</td>\n", "      <td>...</td>\n", "      <td>2.2241</td>\n", "      <td>2.1430</td>\n", "      <td>2.0617</td>\n", "      <td>1.9285</td>\n", "      <td>1.7958</td>\n", "      <td>98.245</td>\n", "      <td>98.245</td>\n", "      <td>98.225</td>\n", "      <td>98.195</td>\n", "      <td>2.6010</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>246 rows × 29 columns</p>\n", "</div>"], "text/plain": ["            EUR003M Index  ER1 Comdty  ER2 Comdty  ER3 Comdty  ER4 Comdty  \\\n", "2024-01-02          3.905      96.360      96.870      97.335      97.675   \n", "2024-01-03          3.929      96.360      96.880      97.350      97.700   \n", "2024-01-04          3.922      96.320      96.800      97.235      97.570   \n", "2024-01-05          3.936      96.310      96.770      97.195      97.530   \n", "2024-01-08          3.925      96.300      96.760      97.190      97.530   \n", "...                   ...         ...         ...         ...         ...   \n", "2024-12-05          2.881      97.170      97.765      98.055      98.145   \n", "2024-12-06          2.868      97.170      97.755      98.035      98.135   \n", "2024-12-09          2.862      97.175      97.785      98.060      98.165   \n", "2024-12-10          2.872      97.170      97.795      98.080      98.190   \n", "2024-12-11          2.888      97.160      97.795      98.090      98.205   \n", "\n", "            EUSW1VC Curncy  EUSWV31F Curncy  EUSW2V3 Curncy  EUSW3V3 Curncy  \\\n", "2024-01-02          3.3700           2.9800          2.7455          2.5060   \n", "2024-01-03          3.3530           2.9451          2.6962          2.4495   \n", "2024-01-04          3.4240           3.0443          2.8190          2.5790   \n", "2024-01-05          3.4500           3.0816          2.8700          2.6350   \n", "2024-01-08          3.4230           3.0492          2.8210          2.5905   \n", "...                    ...              ...             ...             ...   \n", "2024-12-05          2.2910           2.1455          2.0870          2.0455   \n", "2024-12-06          2.2880           2.1335          2.0720          2.0255   \n", "2024-12-09          2.2717           2.1193          2.0582          2.0157   \n", "2024-12-10          2.2578           2.1003          2.0368          1.9975   \n", "2024-12-11          2.2538           2.0970          2.0340          1.9948   \n", "\n", "            EUSW4V3 Curncy  ...  EUSW20V3 Curncy  EUSW25V3 Curncy  \\\n", "2024-01-02          2.4190  ...           2.6033           2.5255   \n", "2024-01-03          2.3560  ...           2.5505           2.4725   \n", "2024-01-04          2.4925  ...           2.6370           2.5645   \n", "2024-01-05          2.5425  ...           2.6690           2.5913   \n", "2024-01-08          2.5065  ...           2.6480           2.5697   \n", "...                    ...  ...              ...              ...   \n", "2024-12-05          2.0395  ...           2.1740           2.0845   \n", "2024-12-06          2.0199  ...           2.1775           2.0864   \n", "2024-12-09          2.0121  ...           2.1897           2.1028   \n", "2024-12-10          1.9983  ...           2.2007           2.1155   \n", "2024-12-11          2.0025  ...           2.2241           2.1430   \n", "\n", "            EUSW30V3 Curncy  EUSW40V3 Curncy  EUSW50V3 Curncy  ER5 Comdty  \\\n", "2024-01-02           2.4497           2.3252           2.2150      97.905   \n", "2024-01-03           2.3950           2.2810           2.1704      97.940   \n", "2024-01-04           2.4830           2.3403           2.2290      97.805   \n", "2024-01-05           2.5090           2.3640           2.2440      97.755   \n", "2024-01-08           2.4862           2.3447           2.2210      97.760   \n", "...                     ...              ...              ...         ...   \n", "2024-12-05           1.9935           1.8398           1.6899      98.170   \n", "2024-12-06           1.9975           1.8455           1.6993      98.175   \n", "2024-12-09           2.0165           1.8733           1.7320      98.205   \n", "2024-12-10           2.0310           1.8911           1.7527      98.230   \n", "2024-12-11           2.0617           1.9285           1.7958      98.245   \n", "\n", "            ER6 Comdty  ER7 Comdty  ER8 Comdty  EUSWFVC Curncy  \n", "2024-01-02      98.040      98.080      98.070          3.8050  \n", "2024-01-03      98.075      98.120      98.115          3.7990  \n", "2024-01-04      97.940      97.990      97.990          3.8180  \n", "2024-01-05      97.885      97.935      97.940          3.8450  \n", "2024-01-08      97.895      97.945      97.950          3.8230  \n", "...                ...         ...         ...             ...  \n", "2024-12-05      98.165      98.145      98.120          2.6140  \n", "2024-12-06      98.175      98.160      98.140          2.6135  \n", "2024-12-09      98.205      98.190      98.165          2.5980  \n", "2024-12-10      98.225      98.205      98.180          2.5920  \n", "2024-12-11      98.245      98.225      98.195          2.6010  \n", "\n", "[246 rows x 29 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_parquet(r\"c:\\Users\\<USER>\\Documents\\investment\\loader\\curve_benchmark_data\\EUR_EURIBOR_3M_20240101_marketclose.parquet\")\n", "data"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Start calibration of EUR_EURIBOR_3M on 2024-12-12, Interp method: PiecewiseLogCubicDiscount...\n", "\n", "Start calibration of EUR_ESTR on 2024-12-12, Interp method: PiecewiseLogMixedLinearCubicDiscount...\n", "NB: Loading index fixing on 2024-12-11, lag: 1d\n", "OIS term: 1D , Maturity: 2024-12-13, Quote: 3.1640, Pillar date: 2024-12-18... NB: Proxy with Quote: 3.1647\n", "OIS term: 18M, Maturity: 2026-06-16, Quote: 2.0000, Pillar date: 2026-06-16\n", "OIS term: 2Y , Maturity: 2026-12-16, Quote: 1.9430, Pillar date: 2026-12-16\n", "OIS term: 3Y , Maturity: 2027-12-16, Quote: 1.9078, Pillar date: 2027-12-16\n", "OIS term: 4Y , Maturity: 2028-12-18, Quote: 1.9212, Pillar date: 2028-12-18\n", "OIS term: 5Y , Maturity: 2029-12-17, Quote: 1.9460, Pillar date: 2029-12-17\n", "OIS term: 6Y , Maturity: 2030-12-16, Quote: 1.9681, Pillar date: 2030-12-16\n", "OIS term: 7Y , Maturity: 2031-12-16, Quote: 1.9965, Pillar date: 2031-12-16\n", "OIS term: 8Y , Maturity: 2032-12-16, Quote: 2.0230, Pillar date: 2032-12-16\n", "OIS term: 9Y , Maturity: 2033-12-16, Quote: 2.0520, Pillar date: 2033-12-16\n", "OIS term: 10Y, Maturity: 2034-12-18, Quote: 2.0798, Pillar date: 2034-12-18\n", "OIS term: 11Y, Maturity: 2035-12-17, Quote: 2.1066, Pillar date: 2035-12-17\n", "OIS term: 12Y, Maturity: 2036-12-16, Quote: 2.1332, Pillar date: 2036-12-16\n", "OIS term: 15Y, Maturity: 2039-12-16, Quote: 2.1892, Pillar date: 2039-12-16\n", "OIS term: 20Y, Maturity: 2044-12-16, Quote: 2.1785, Pillar date: 2044-12-16\n", "OIS term: 25Y, Maturity: 2049-12-16, Quote: 2.1070, Pillar date: 2049-12-16\n", "OIS term: 30Y, Maturity: 2054-12-16, Quote: 2.0330, Pillar date: 2054-12-16\n", "OIS term: 40Y, Maturity: 2064-12-16, Quote: 1.9136, Pillar date: 2064-12-16\n", "OIS term: 50Y, Maturity: 2074-12-17, Quote: 1.7866, Pillar date: 2074-12-17\n", "OIS term: 60Y, Maturity: 2084-12-18, Quote: 1.6970, Pillar date: 2084-12-18\n", "Meeting OIS term s1 2024-12-18 - 2025-02-05, Maturity: 2025-02-05, Quote: 2.9170, Pillar date: 2025-02-05\n", "Meeting OIS term s2 2025-02-05 - 2025-03-12, Maturity: 2025-03-12, Quote: 2.6109, Pillar date: 2025-03-12\n", "Meeting OIS term s3 2025-03-12 - 2025-04-23, Maturity: 2025-04-23, Quote: 2.2936, Pillar date: 2025-04-23\n", "Meeting OIS term s4 2025-04-23 - 2025-06-11, Maturity: 2025-06-11, Quote: 2.0480, Pillar date: 2025-06-11\n", "Meeting OIS term s5 2025-06-11 - 2025-07-30, Maturity: 2025-07-30, Quote: 1.8913, Pillar date: 2025-07-30\n", "Meeting OIS term s6 2025-07-30 - 2025-09-17, Maturity: 2025-09-17, Quote: 1.8113, Pillar date: 2025-09-17\n", "Meeting OIS term s7 2025-09-17 - 2025-11-05, Maturity: 2025-11-05, Quote: 1.7530, Pillar date: 2025-11-05\n", "Finish calibration of EUR_ESTR on 2024-12-12.\n", "\n", "NB: Loading index fixing on 2024-12-11, lag: 1d\n", "\n", "Start calibration of EUR_EURIBOR_3M on 2024-12-12, Interp method: PiecewiseLogCubicDiscount...\n", "Future term c1 2024-12-18 - 2025-03-19, Quote: 97.1400, Pillar date: 2025-03-19... convexity: 0.000000%\n", "Future term c2 2025-03-19 - 2025-06-18, Quote: 97.7700, Pillar date: 2025-06-18... convexity: 0.000000%\n", "Future term c3 2025-06-18 - 2025-09-17, Quote: 98.0500, Pillar date: 2025-09-17... convexity: 0.000000%\n", "Future term c4 2025-09-17 - 2025-12-17, Quote: 98.1550, Pillar date: 2025-12-17... convexity: 0.000000%\n", "Future term c5 2025-12-17 - 2026-03-18, Quote: 98.1900, Pillar date: 2026-03-18... convexity: 0.000000%\n", "Future term c6 2026-03-18 - 2026-06-17, Quote: 98.1850, Pillar date: 2026-06-17... convexity: 0.000000%\n", "Future term c7 2026-06-17 - 2026-09-16, Quote: 98.1600, Pillar date: 2026-09-16... convexity: 0.000000%\n", "Future term c8 2026-09-16 - 2026-12-16, Quote: 98.1350, Pillar date: 2026-12-16... convexity: 0.000000%\n", "Vanilla swap term: 3Y , Maturity: 2027-12-16, Quote: 2.0660, Pillar date: 2027-12-16\n", "Vanilla swap term: 4Y , Maturity: 2028-12-18, Quote: 2.0760, Pillar date: 2028-12-18\n", "Vanilla swap term: 5Y , Maturity: 2029-12-17, Quote: 2.0900, Pillar date: 2029-12-17\n", "Vanilla swap term: 6Y , Maturity: 2030-12-16, Quote: 2.1085, Pillar date: 2030-12-16\n", "Vanilla swap term: 7Y , Maturity: 2031-12-16, Quote: 2.1298, Pillar date: 2031-12-16\n", "Vanilla swap term: 8Y , Maturity: 2032-12-16, Quote: 2.1547, Pillar date: 2032-12-16\n", "Vanilla swap term: 9Y , Maturity: 2033-12-16, Quote: 2.1813, Pillar date: 2033-12-16\n", "Vanilla swap term: 10Y, Maturity: 2034-12-18, Quote: 2.2073, Pillar date: 2034-12-18\n", "Vanilla swap term: 11Y, Maturity: 2035-12-17, Quote: 2.2314, Pillar date: 2035-12-17\n", "Vanilla swap term: 12Y, Maturity: 2036-12-16, Quote: 2.2557, Pillar date: 2036-12-16\n", "Vanilla swap term: 15Y, Maturity: 2039-12-16, Quote: 2.3020, Pillar date: 2039-12-16\n", "Vanilla swap term: 20Y, Maturity: 2044-12-16, Quote: 2.2752, Pillar date: 2044-12-16\n", "Vanilla swap term: 25Y, Maturity: 2049-12-16, Quote: 2.1937, Pillar date: 2049-12-16\n", "Vanilla swap term: 30Y, Maturity: 2054-12-16, Quote: 2.1128, Pillar date: 2054-12-16\n", "Vanilla swap term: 40Y, Maturity: 2064-12-16, Quote: 1.9737, Pillar date: 2064-12-16\n", "Vanilla swap term: 50Y, Maturity: 2074-12-17, Quote: 1.8332, Pillar date: 2074-12-17\n", "Finish calibration of EUR_EURIBOR_3M on 2024-12-12.\n", "\n", "Vanilla swap term: 6M , Maturity: 2025-06-16, Quote: 2.6130, Pillar date: 2025-06-16\n", "Vanilla swap term: 1Y , Maturity: 2025-12-16, Quote: 2.3015, Pillar date: 2025-12-16\n", "Vanilla swap term: 18M, Maturity: 2026-06-16, Quote: 2.1567, Pillar date: 2026-06-16\n", "Vanilla swap term: 2Y , Maturity: 2026-12-16, Quote: 2.1020, Pillar date: 2026-12-16\n", "0.02613\n", "0.023014999999999997\n", "0.021567\n", "0.021019999999999997\n", "[-40000. -40000.]\n", "Future term c1 2024-12-18 - 2025-03-19, Quote: 97.1400, Pillar date: 2025-03-19... convexity: -0.000169%\n", "Future term c2 2025-03-19 - 2025-06-18, Quote: 97.7700, Pillar date: 2025-06-18... convexity: -0.002724%\n", "Future term c3 2025-06-18 - 2025-09-17, Quote: 98.0500, Pillar date: 2025-09-17... convexity: -0.005280%\n", "Future term c4 2025-09-17 - 2025-12-17, Quote: 98.1550, Pillar date: 2025-12-17... convexity: -0.007836%\n", "Future term c5 2025-12-17 - 2026-03-18, Quote: 98.1900, Pillar date: 2026-03-18... convexity: -0.010392%\n", "Future term c6 2026-03-18 - 2026-06-17, Quote: 98.1850, Pillar date: 2026-06-17... convexity: -0.012948%\n", "Future term c7 2026-06-17 - 2026-09-16, Quote: 98.1600, Pillar date: 2026-09-16... convexity: -0.015504%\n", "Future term c8 2026-09-16 - 2026-12-16, Quote: 98.1350, Pillar date: 2026-12-16... convexity: -0.018060%\n", "Vanilla swap term: 3Y , Maturity: 2027-12-16, Quote: 2.0660, Pillar date: 2027-12-16\n", "Vanilla swap term: 4Y , Maturity: 2028-12-18, Quote: 2.0760, Pillar date: 2028-12-18\n", "Vanilla swap term: 5Y , Maturity: 2029-12-17, Quote: 2.0900, Pillar date: 2029-12-17\n", "Vanilla swap term: 6Y , Maturity: 2030-12-16, Quote: 2.1085, Pillar date: 2030-12-16\n", "Vanilla swap term: 7Y , Maturity: 2031-12-16, Quote: 2.1298, Pillar date: 2031-12-16\n", "Vanilla swap term: 8Y , Maturity: 2032-12-16, Quote: 2.1547, Pillar date: 2032-12-16\n", "Vanilla swap term: 9Y , Maturity: 2033-12-16, Quote: 2.1813, Pillar date: 2033-12-16\n", "Vanilla swap term: 10Y, Maturity: 2034-12-18, Quote: 2.2073, Pillar date: 2034-12-18\n", "Vanilla swap term: 11Y, Maturity: 2035-12-17, Quote: 2.2314, Pillar date: 2035-12-17\n", "Vanilla swap term: 12Y, Maturity: 2036-12-16, Quote: 2.2557, Pillar date: 2036-12-16\n", "Vanilla swap term: 15Y, Maturity: 2039-12-16, Quote: 2.3020, Pillar date: 2039-12-16\n", "Vanilla swap term: 20Y, Maturity: 2044-12-16, Quote: 2.2752, Pillar date: 2044-12-16\n", "Vanilla swap term: 25Y, Maturity: 2049-12-16, Quote: 2.1937, Pillar date: 2049-12-16\n", "Vanilla swap term: 30Y, Maturity: 2054-12-16, Quote: 2.1128, Pillar date: 2054-12-16\n", "Vanilla swap term: 40Y, Maturity: 2064-12-16, Quote: 1.9737, Pillar date: 2064-12-16\n", "Vanilla swap term: 50Y, Maturity: 2074-12-17, Quote: 1.8332, Pillar date: 2074-12-17\n", "Finish calibration of EUR_EURIBOR_3M on 2024-12-12.\n", "\n"]}], "source": ["euribor_curve = euribor3MCurve(\"20241212\", debug_mode=True)\n", "euribor_curve.calibrate(convexity_calibration=True)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'swaps' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[29], line 10\u001b[0m\n\u001b[0;32m      1\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame(\n\u001b[0;32m      2\u001b[0m             [\n\u001b[0;32m      3\u001b[0m                 {\n\u001b[0;32m      4\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mT1\u001b[39m\u001b[38;5;124m\"\u001b[39m: cf\u001b[38;5;241m.\u001b[39mdayCounter()\u001b[38;5;241m.\u001b[39myearFraction(euribor_curve\u001b[38;5;241m.\u001b[39mreference_date, cf\u001b[38;5;241m.\u001b[39maccrualStartDate()),\n\u001b[0;32m      5\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mT2\u001b[39m\u001b[38;5;124m\"\u001b[39m: cf\u001b[38;5;241m.\u001b[39mdayCounter()\u001b[38;5;241m.\u001b[39myearFraction(euribor_curve\u001b[38;5;241m.\u001b[39mreference_date, cf\u001b[38;5;241m.\u001b[39maccrualEndDate()),\n\u001b[0;32m      6\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrate\u001b[39m\u001b[38;5;124m\"\u001b[39m: cf\u001b[38;5;241m.\u001b[39mrate(),\n\u001b[0;32m      7\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mamount\u001b[39m\u001b[38;5;124m\"\u001b[39m: cf\u001b[38;5;241m.\u001b[39mamount(),\n\u001b[0;32m      8\u001b[0m                     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdiscount\u001b[39m\u001b[38;5;124m\"\u001b[39m: euribor_curve\u001b[38;5;241m.\u001b[39mois_curve\u001b[38;5;241m.\u001b[39mcurve\u001b[38;5;241m.\u001b[39mdiscount(cf\u001b[38;5;241m.\u001b[39mdate()),\n\u001b[0;32m      9\u001b[0m                 }\n\u001b[1;32m---> 10\u001b[0m                 \u001b[38;5;28;01mfor\u001b[39;00m cf \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mmap\u001b[39m(ql\u001b[38;5;241m.\u001b[39mas_floating_rate_coupon, \u001b[43mswaps\u001b[49m[\u001b[38;5;241m3\u001b[39m]\u001b[38;5;241m.\u001b[39mfloatingLeg())\n\u001b[0;32m     11\u001b[0m             ]\n\u001b[0;32m     12\u001b[0m         )\n", "\u001b[1;31mNameError\u001b[0m: name 'swaps' is not defined"]}], "source": ["df = pd.DataFrame(\n", "            [\n", "                {\n", "                    \"T1\": cf.dayCounter().yearFraction(euribor_curve.reference_date, cf.accrualStartDate()),\n", "                    \"T2\": cf.dayCounter().yearFraction(euribor_curve.reference_date, cf.accrualEndDate()),\n", "                    \"rate\": cf.rate(),\n", "                    \"amount\": cf.amount(),\n", "                    \"discount\": euribor_curve.ois_curve.curve.discount(cf.date()),\n", "                }\n", "                for cf in map(ql.as_floating_rate_coupon, swaps[3].floatingLeg())\n", "            ]\n", "        )"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["ql.Settings.instance().evaluationDate = ql.Date.from_date(euribor_curve.date)\n", "yts = ql.RelinkableYieldTermStructureHandle()\n", "euribor_3m_index = ql.Euribor3M(yts)\n", "yts_discount = ql.RelinkableYieldTermStructureHandle()\n", "engine = ql.DiscountingSwapEngine(yts_discount)\n", "swap_18m = ql.MakeVanillaSwap(ql.Period(\"18m\"), euribor_3m_index, 2.1567/100, ql.Period(), nominal=1e6, receiveFixed=True, fixedLegDayCount=ql.Thirty360(ql.Thirty360.USA), pricingEngine=engine)\n", "swap_2y = ql.MakeVanillaSwap(ql.Period(\"2y\"), euribor_3m_index, 2.1020/100, ql.Period(), nominal=1e6, receiveFixed=True, fixedLegDayCount=ql.Thirty360(ql.Thirty360.USA), pricingEngine=engine)\n", "swap_3y = ql.MakeVanillaSwap(ql.Period(\"3y\"), euribor_3m_index, 2.0660/100, ql.Period(), nominal=1e6, receiveFixed=True, fixedLegDayCount=ql.Thirty360(ql.Thirty360.USA), pricingEngine=engine)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["yts.linkTo(euribor_curve.curve)\n", "yts_discount.linkTo(euribor_curve.ois_curve.curve)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["394.4301508513454\n", "529.7632152076549\n", "8.577626431360841e-08\n"]}], "source": ["# Before convexity calibration:\n", "print(swap_18m.NPV())\n", "print(swap_2y.NPV())\n", "print(swap_3y.NPV())"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["135.38588121965586\n", "-52.3872886159661\n", "8.3498889580369e-08\n"]}], "source": ["# After convexity calibration:\n", "print(swap_18m.NPV())\n", "print(swap_2y.NPV())\n", "print(swap_3y.NPV())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.021047005985926872"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["swap_2y.fairRate()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["297.7662506443048\n", "350.9973487177631\n", "8.501956472173333e-08\n"]}], "source": ["# After convexity calibration:\n", "print(swap_18m.NPV())\n", "print(swap_2y.NPV())\n", "print(swap_3y.NPV())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}